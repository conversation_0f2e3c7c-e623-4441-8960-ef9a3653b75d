package controller

import (
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	r "github.com/tricobbler/echo-tool/httpError"
	kit "github.com/tricobbler/rp-kit"
	"order-api/models"
	"order-api/proto/dac"
	"order-api/utils"
)

// @Summary SCRM用户token
// @Tags SCRM用户相关
// @Accept plain
// @Produce json
// @Param user_mobile query string false "用户手机号"
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order-api/user/token [get]
func GetToken(c echo.Context) error {
	type params struct {
		UserMobile string `query:"user_mobile" label:"用户手机号"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//数据中心
	dcClient := dac.GetDataCenterClient()
	defer dcClient.Close()

	in := &dac.GetScrmUserRequest{
		UserMobile: p.UserMobile,
	}
	res, err := dcClient.RPC.GetScrmUser(dcClient.Ctx, in)
	if err != nil {
		glog.Error("调用GetScrmUser失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}
	if res.Code != 200 {
		return r.NewHTTPError(400, res.Message)
	}

	if res.Data == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	glog.Info("zx11111xxx", res)
	//生成token
	token, err := utils.CreateJwtToken(res.Data.UserMobile, res.Data.UserId, res.Data.UserName)
	if err != nil {
		glog.Error("调用CreateJwtToken失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}

	out := models.UserTokenResponse{}
	out.Code = 200
	out.Data = &models.UserToken{
		Token: token,
	}

	return c.JSON(200, out)
}
