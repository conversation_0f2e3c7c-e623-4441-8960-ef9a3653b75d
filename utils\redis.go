package utils

import (
	"github.com/go-redis/redis"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"strconv"
	"strings"
)

var (
	redisHandle *kit.DBEngine
)

//redis链接
func NewRedisClient() *redis.Client {
	defer kit.CatchPanic()

	DB, _ := strconv.Atoi(config.GetString("redis.DB"))
	client := redis.NewClient(&redis.Options{
		Addr:     config.GetString("redis.Addr"),
		Password: config.GetString("redis.Password"),
		DB:       DB,
	})

	return client
}

func getRedisDsn(dsn ...string) string {
	if len(dsn) > 0 {
		return dsn[0]
	}

	dsnSlice := []string{
		config.GetString("redis.Addr"),
		config.GetString("redis.Password"),
		config.GetString("redis.DB"),
	}

	//glog.Info(fmt.Sprintf("redis连接参数:%s,%s,%s", config.GetString("redis.Addr"), config.GetString("redis.Password"), config.GetString("redis.DB")))

	return strings.Join(dsnSlice, "|")
}

//获取redis集群客户端
func GetRedisConn(dsn ...string) *redis.Client {
	if redisHandle == nil || redisHandle.Engine == nil {
		redisHandle = kit.NewRedisEngine(getRedisDsn(dsn...))
	}
	return redisHandle.Engine.(*redis.Client)
}

// 通过key获取hash的元素值
func HashGet(key, field string) string {
	client := NewRedisClient()
	defer client.Close()

	val, err := client.HGet(key, field).Result()
	if err != nil && err != redis.Nil {
		glog.Error(key, ", hashget失败，", err)
		return ""
	}
	return val
}
