package controller

import (
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"net/http"
	"order-api/dto"
	"order-api/proto/oc"
)

//当前只用于异常订单oms出库的回调 正常出库的情况在不需要回调
// @Summary oms订单出库成功 回调
// @Tags OMS
// @Accept json
// @Produce json
// @Param models.PushOmsRequest body models.PushOmsRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/oms/delivered [post]
func RpOmsOrderDeliveredCallback(c echo.Context) error {
	out := new(oc.BaseResponse)
	out.Code = http.StatusBadRequest
	model := new(dto.RpOmsOrderDeliverSuccessRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数错误"
		return c.JSON(http.StatusOK, out)
	}
	if model.OrderSn == "" {
		out.Message = "订单号不能为空"
		return c.JSON(http.StatusOK, out)
	}
	glog.Info("oms订单出库成功 回调参数", kit.JsonEncode(model))

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()
	request := new(oc.RpOmsOrderDeliveredRequest)
	request.OrderSn = model.OrderSn
	glog.Error("RpOmsOrderDelivered 入参:", kit.JsonEncode(request))
	rpcRes, err := ocClient.OMS.RpOmsOrderDelivered(ocClient.Ctx, request)
	glog.Error("RpOmsOrderDelivered 出参:", kit.JsonEncode(request), "结果", rpcRes)

	if err != nil {
		glog.Error("RpOmsOrderDelivered 出库成功回调出错:", err)
		out.Message = "回调"
		return c.JSON(http.StatusOK, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("RpOmsOrderDelivered 出库成功回调异常:", rpcRes.Error)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusOK, out)
	}
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}
