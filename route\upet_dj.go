package route

import (
	"net/http"
	"order-api/controller"

	"github.com/labstack/echo/v4"
)

func UpetDjRoute(e *echo.Group) {
	g := e.Group("/upetDj")
	//加载省市区列表
	g.GET("/QuerySection", controller.QuerySection)
	// 根据购物车商品计算优惠信息以及运费
	g.POST("/CalcPromotionMoney", controller.CalcPromotionMoney)
	// SAAS宠物计算购物车价格
	g.POST("/GetDeliveryMoney", controller.GetDeliveryMoney)
	//修改订单状态，确认收货
	g.PUT("/ConfirmUpetDj", controller.ConfirmUpetDj)
	//查询阿闻到家订单
	g.GET("/QueryUpetDjOrderList", controller.QueryUpetDjOrderList)
	//查询阿闻到家订单
	g.GET("/QueryUpetDjOrderListByModile", controller.QueryUpetDjOrderListByModile)
	//查询阿闻到家订单详情
	g.GET("/QueryUpetDjOrderDetail", controller.QueryUpetDjOrderDetail)
	//查询店铺的配送配置
	g.GET("/QueryUpetDjShopDelivery", controller.QueryUpetDjShopDelivery)
	//查询阿闻到家订单是否需要打印
	g.GET("/QueryUpetDjOrderIsAutoPrint", controller.QueryUpetDjOrderIsAutoPrint)
	//根据单号信息发送消息到阿闻管家后台
	g.GET("/PrintWithOrderId", controller.PrintWithOrderId)

	g.GET("/pickup-stations", controller.PickupStations)
	g.GET("/pickup-station/state", controller.PickupStationState)
	g.GET("/pickup-station/nearest", controller.PickupStationNearest)

	// 版本号码
	g.GET("/ver", func(c echo.Context) error {
		return c.String(http.StatusOK, "v2.3 20200709 16:43")
	})
}
