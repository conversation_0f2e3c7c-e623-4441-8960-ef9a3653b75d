package route

import (
	"github.com/labstack/echo/v4"
	"order-api/controller"
)

func CardRoute(e *echo.Group) {
	g := e.Group("/card")
	g.POST("/new", controller.CardNew)
	g.POST("/new-by-code", controller.CardNewByCode)
	g.POST("/service-pack-activity", controller.CardServicePackActivity)
	g.POST("/equity-receive", controller.CardEquityReceive)
	g.GET("/query-sign-id", controller.CardQuerySignId)
}
