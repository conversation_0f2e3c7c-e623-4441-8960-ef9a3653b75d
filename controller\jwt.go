package controller

import (
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"time"
)

type Jwt struct {
}

const (
	SecretKey = "s2b2c"
)

func createJwt(scrmId, mobile string) string {
	token := jwt.New(jwt.SigningMethodHS256)
	claims := make(jwt.MapClaims)
	claims["exp"] = time.Now().Add(time.Hour * time.Duration(24)).Unix()
	claims["iat"] = time.Now().Unix()
	claims["scrmId"] = scrmId
	claims["mobile"] = mobile
	token.Claims = claims

	tokenString, err := token.SignedString([]byte(SecretKey))
	if err != nil {
		//w.WriteHeader(http.StatusInternalServerError)
		//fmt.Println("Error while signing the token")
		//panic(err)
		return ""
	}
	return tokenString
}

func ParseJwt(tokenString string) jwt.MapClaims {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Don't forget to validate the alg is what you expect:
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("Unexpected signing method: %v", token.Header["alg"])
		}
		// hmacSampleSecret is a []byte containing your secret, e.g. []byte("my_secret_key")
		return []byte(SecretKey), nil
	})
	if err != nil {
		return nil
	}

	var claims jwt.MapClaims
	var ok bool

	if claims, ok = token.Claims.(jwt.MapClaims); ok && token.Valid {
		//fmt.Println(claims["foo"], claims["nbf"])
		return claims
	} else {
		//fmt.Println(err)
		return nil
	}
	//return claims
}
