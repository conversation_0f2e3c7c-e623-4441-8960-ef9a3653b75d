module order-api

go 1.13

require (
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-playground/validator/v10 v10.4.1
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.5.1
	github.com/labstack/echo/v4 v4.1.17
	github.com/limitedlee/microservice v0.1.0
	github.com/maybgit/glog v0.1.23-0.20210726022127-a3704c82c0c0
	github.com/maybgit/pbgo v0.0.0-20200601050928-85c4ece4a248
	github.com/olivere/elastic/v7 v7.0.17
	github.com/shopspring/decimal v1.2.0
	github.com/sony/sonyflake v1.0.0
	github.com/spf13/cast v1.3.1
	github.com/swaggo/echo-swagger v1.3.0
	github.com/swaggo/swag v1.16.2
	github.com/tricobbler/echo-tool v0.0.0-20210122102021-57d9a22ae6cf
	github.com/tricobbler/rp-kit v0.0.0-20210326101043-100a30604458
	golang.org/x/crypto v0.0.0-20220411220226-7b82a4e95df4 // indirect
	google.golang.org/genproto v0.0.0-20210317182105-75c7a8546eb9
	google.golang.org/grpc v1.36.0
	google.golang.org/protobuf v1.26.0
)

replace order-api => ./
