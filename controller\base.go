package controller

import (
	"context"
	"order-api/proto/oc"
	"sync"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"google.golang.org/grpc"
)

type Client struct {
	lock      sync.Mutex
	Conn      *grpc.ClientConn
	Ctx       context.Context
	Cf        context.CancelFunc
	RPC       oc.OrderServiceClient
	OMS       oc.OmsOrderServiceClient
	OES       oc.OrderExceptionServiceClient
	ROC       oc.RefundOrderServiceClient
	AfterSale oc.AfterSaleServiceClient
	Cart      oc.CartServiceClient
	Integral  oc.OrderIntegralServiceClient
	UpetDJ    oc.UpetDjServiceClient
	SM        oc.SubscribeMessageServiceClient
	MP        oc.AdvertisementMpServiceClient
	IV        oc.InvoiceServiceClient
	CG        oc.CommunityGroupServiceClient
}

func GetOrderServiceClient() *Client {
	var grpcClient Client
	var err error
	url := config.GetString("grpc.order-center")
	url = "127.0.0.1:11005"
	if url == "" {
		url = "127.0.0.1:11005"
	}

	if grpcClient.Conn, err = grpc.Dial(url, grpc.WithInsecure()); err != nil {
		glog.Error("order-center，grpc连接失败，", err)
		return nil
	} else {
		grpcClient.RPC = oc.NewOrderServiceClient(grpcClient.Conn)
		grpcClient.OMS = oc.NewOmsOrderServiceClient(grpcClient.Conn)
		grpcClient.OES = oc.NewOrderExceptionServiceClient(grpcClient.Conn)
		grpcClient.ROC = oc.NewRefundOrderServiceClient(grpcClient.Conn)
		grpcClient.AfterSale = oc.NewAfterSaleServiceClient(grpcClient.Conn)
		grpcClient.Cart = oc.NewCartServiceClient(grpcClient.Conn)
		grpcClient.Integral = oc.NewOrderIntegralServiceClient(grpcClient.Conn)
		grpcClient.UpetDJ = oc.NewUpetDjServiceClient(grpcClient.Conn)
		grpcClient.SM = oc.NewSubscribeMessageServiceClient(grpcClient.Conn)
		grpcClient.MP = oc.NewAdvertisementMpServiceClient(grpcClient.Conn)
		grpcClient.IV = oc.NewInvoiceServiceClient(grpcClient.Conn)
		grpcClient.Ctx = context.Background()
		grpcClient.Ctx, grpcClient.Cf = context.WithTimeout(grpcClient.Ctx, time.Minute*30)
		grpcClient.CG = oc.NewCommunityGroupServiceClient(grpcClient.Conn)

		return &grpcClient
	}
}

func (c *Client) Close() {
	c.Conn.Close()
	c.Cf()
}
