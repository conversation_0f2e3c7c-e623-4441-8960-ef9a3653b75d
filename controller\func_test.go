package controller

import (
	"encoding/json"
	"order-api/models"
	"order-api/proto/oc"
	"reflect"
	"testing"
)

func Test_freedSecKillStock(t *testing.T) {
	type args struct {
		sku       string
		secKillId int32
		number    int32
	}
	tests := []struct {
		name  string
		args  args
		want  bool
		want1 error
		want2 string
	}{
		// TODO: Add test cases.
		{
			name: "测试释放库存",
			args: args{
				sku:       "1020754001",
				secKillId: 1,
				number:    100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			FreedSecKillStock(tt.args.sku, tt.args.secKillId, tt.args.number)
		})
	}
}

func TestDealChildProduct(t *testing.T) {
	type args struct {
		orderProducts []*oc.OrderProductModel
		childProducts []*models.OrderProductModel
		orderProduct  *oc.OrderProductModel
	}
	products := []*oc.OrderProductModel{
		&oc.OrderProductModel{
			Sku:           "1030659001",
			ProductId:     "1030659",
			Price:         100,
			Number:        2,
			ProductType:   1,
			PromotionType: 1,
			PromotionId:   12,
		},
		&oc.OrderProductModel{
			Sku:           "1023419099",
			ProductId:     "1023419",
			Price:         10000,
			Number:        2,
			ProductType:   3,
			CombineType:   3,
			PromotionType: 1,
			PromotionId:   12,
		},
	}
	childProducts := []*models.OrderProductModel{
		&models.OrderProductModel{
			Sku:                "1023394001",
			ProductId:          "1023394",
			Price:              6000,
			Number:             2,
			ProductType:        1,
			GroupDiscountType:  1,
			GroupDiscountValue: 80,
		},
		&models.OrderProductModel{
			Sku:                "1023400001",
			ProductId:          "1023400",
			Price:              2000,
			Number:             2,
			ProductType:        1,
			GroupDiscountType:  1,
			GroupDiscountValue: 80,
		},
		&models.OrderProductModel{
			Sku:                "1031383001",
			ProductId:          "1031383",
			Price:              9000,
			Number:             1,
			ProductType:        2,
			TermType:           1,
			TermValue:          1637208112,
			GroupDiscountType:  2,
			GroupDiscountValue: 6000,
		},
		&models.OrderProductModel{
			Sku:                "1031388001",
			ProductId:          "1031388",
			Price:              10000,
			Number:             1,
			ProductType:        2,
			TermType:           1,
			TermValue:          1637208112,
			GroupDiscountType:  2,
			GroupDiscountValue: 8000,
		},
	}
	product := &oc.OrderProductModel{
		Sku:           "1023419099",
		ProductId:     "1023419",
		Price:         100,
		Number:        2,
		ProductType:   3,
		CombineType:   3,
		PromotionType: 2,
		PromotionId:   12,
		SkuPayTotal:   9000,
		PaymentTotal:  50000,
	}
	tests := []struct {
		name  string
		args  args
		want  []*oc.OrderProductModel
		want1 int32
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				orderProducts: products,
				childProducts: childProducts,
				orderProduct:  product,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := DealChildProduct(tt.args.orderProducts, tt.args.childProducts, tt.args.orderProduct)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DealChildProduct() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("DealChildProduct() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestMallJunTan(t *testing.T) {
	type args struct {
		model *models.OrderSubmitRequest
	}
	tests := []struct {
		name         string
		args         args
		wantCode     int32
		wantMsg      string
		wantAddOrder *oc.MtAddOrderRequest
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				model: &models.OrderSubmitRequest{
					OrderProducts: []*models.OrderProductModel{
						&models.OrderProductModel{
							Sku:           "1030659001",
							ProductId:     "1030659",
							Price:         10000,
							Number:        2,
							ProductType:   1,
							PromotionType: 1,
							PromotionId:   12,
						},
						&models.OrderProductModel{
							Sku:           "1023419099",
							ProductId:     "1023419",
							Price:         20000,
							Number:        2,
							ProductType:   3,
							CombineType:   3,
							PromotionType: 1,
							PromotionId:   12,
							ChildProductList: []*models.OrderProductModel{
								&models.OrderProductModel{
									Sku:                "1023394001",
									ProductId:          "1023394",
									Price:              6000,
									Number:             2,
									ProductType:        1,
									GroupDiscountType:  1,
									GroupDiscountValue: 80,
								},
								&models.OrderProductModel{
									Sku:                "1023400001",
									ProductId:          "1023400",
									Price:              2000,
									Number:             2,
									ProductType:        1,
									GroupDiscountType:  1,
									GroupDiscountValue: 80,
								},
								&models.OrderProductModel{
									Sku:                "1031383001",
									ProductId:          "1031383",
									Price:              9000,
									Number:             1,
									ProductType:        2,
									TermType:           1,
									TermValue:          1637208112,
									GroupDiscountType:  2,
									GroupDiscountValue: 6000,
								},
								&models.OrderProductModel{
									Sku:                "1031388001",
									ProductId:          "1031388",
									Price:              10000,
									Number:             1,
									ProductType:        2,
									TermType:           1,
									TermValue:          1637208112,
									GroupDiscountType:  2,
									GroupDiscountValue: 8000,
								},
							},
						},
					},
					Order: &models.Order{
						ChannelId:     5,
						ReceiverPhone: "13332970406",
						ShopId:        "YC0014",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotCode, gotMsg, gotAddOrder := MallJunTan(tt.args.model)
			if gotCode != tt.wantCode {
				t.Errorf("MallJunTan() gotCode = %v, want %v", gotCode, tt.wantCode)
			}
			if gotMsg != tt.wantMsg {
				t.Errorf("MallJunTan() gotMsg = %v, want %v", gotMsg, tt.wantMsg)
			}
			if !reflect.DeepEqual(gotAddOrder, tt.wantAddOrder) {
				t.Errorf("MallJunTan() gotAddOrder = %v, want %v", gotAddOrder, tt.wantAddOrder)
			}
		})
	}
}

func TestJunTan(t *testing.T) {
	type args struct {
		model *models.OrderSubmitRequest
	}
	var mod models.OrderSubmitRequest
	modjSON := []byte(`{"order":{"old_order_sn":"","shop_id":"CX0004","channel_id":1,"shop_name":"宠颐生北京爱之都","receiver_name":"杨阳","receiver_state":"","receiver_city":"","receiver_district":"","receiver_address":"广东省 深圳市 福田区KK ONE商场(滨河大道9289号)1101","receiver_phone":"133****0406","privilege":0,"total":662,"goods_total":662,"freight":0,"invoice":"","buyer_memo":"","latitude":22.528873,"longitude":114.02644,"total_weight":10,"expected_time":"2021-11-11 11:49:38","order_type":1,"is_virtual":0,"is_split":0,"power_id":0,"user_agent":0,"device_current_time":"","order_pay_type":"","dis_type":0,"receiver_date_msg":"","tel_phone":"","source":0,"first_order":"","open_id":"","dis_id":"","address_id":""},"order_products":[{"sku":"1031666099","product_type":3,"parent_sku":"","product_id":"1031666","product_name":"IDCAUTO-实物+实物rh7Vh","bar_code":"","price":662,"number":1,"image":"http://file.vetscloud.com/047a9785594a0c234dfa6e18adf09a8d|equal_proportion","discount_count":0,"promotion_id":0,"discount_price":0,"promotion_type":0,"source":0,"combine_type":0,"term_type":0,"term_value":0,"virtual_invalid_refund":0,"is_third_product":0,"group_discount_type":0,"group_discount_value":0,"rec_id":0,"specs":"","child_product_list":[{"sku":"1023972001","product_type":1,"parent_sku":"1031666","product_id":"1023972","product_name":"258-自动上架S0801XX026","bar_code":"","price":3150,"number":1,"image":"http://file.vetscloud.com/9f84e3f3c62ee12d8abeaf71bb10bb10,,,,","discount_count":0,"promotion_id":0,"discount_price":0,"promotion_type":0,"source":0,"combine_type":0,"term_type":0,"term_value":0,"virtual_invalid_refund":1,"is_third_product":0,"group_discount_type":1,"group_discount_value":20,"rec_id":0,"specs":"","child_product_list":null,"pay_price":0,"payment_total":0,"warehouse_type":0},{"sku":"1023985001","product_type":0,"parent_sku":"1031666","product_id":"1023985","product_name":"258-自动上架S0107XX005","bar_code":"","price":158,"number":1,"image":"http://file.vetscloud.com/9619996c8ffc15301ba6b91fc38a1059,,,,","discount_count":0,"promotion_id":0,"discount_price":0,"promotion_type":0,"source":0,"combine_type":0,"term_type":0,"term_value":0,"virtual_invalid_refund":1,"is_third_product":0,"group_discount_type":1,"group_discount_value":20,"rec_id":0,"specs":"","child_product_list":null,"pay_price":0,"payment_total":0,"warehouse_type":0}],"pay_price":0,"payment_total":0,"warehouse_type":0}],"order_promotions":[],"pay_info":{},"warehouse_type":0}`)
	_ = json.Unmarshal(modjSON, &mod)
	tests := []struct {
		name                string
		args                args
		wantCode            int32
		wantMsg             string
		wantAddOrder        *oc.MtAddOrderRequest
		wantFailProductList []*models.CannotSumbitProduct
	}{
		// TODO: Add test cases.
		{
			name: "",
			args: args{
				model: &mod,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotCode, gotMsg, gotAddOrder, gotFailProductList := JunTan(tt.args.model)
			if gotCode != tt.wantCode {
				t.Errorf("JunTan() gotCode = %v, want %v", gotCode, tt.wantCode)
			}
			if gotMsg != tt.wantMsg {
				t.Errorf("JunTan() gotMsg = %v, want %v", gotMsg, tt.wantMsg)
			}
			if !reflect.DeepEqual(gotAddOrder, tt.wantAddOrder) {
				t.Errorf("JunTan() gotAddOrder = %v, want %v", gotAddOrder, tt.wantAddOrder)
			}
			if !reflect.DeepEqual(gotFailProductList, tt.wantFailProductList) {
				t.Errorf("JunTan() gotFailProductList = %v, want %v", gotFailProductList, tt.wantFailProductList)
			}
		})
	}
}
