// Code generated by swaggo/swag. DO NOT EDIT.

package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/order-api/aftersale/apply/list": {
            "get": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单售后相关"
                ],
                "summary": "售后申请单列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订单编号",
                        "name": "order_sn",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.ApplyListResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/aftersale/refund/detail": {
            "get": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单售后相关"
                ],
                "summary": "服务单详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "退款单号",
                        "name": "refund_sn",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.RefundDetailResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/aftersale/refund/list": {
            "get": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单售后相关"
                ],
                "summary": "售后服务单列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订单/服务单编号",
                        "name": "order_refund_sn",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.RefundListResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/aftersale/refund/test": {
            "get": {
                "responses": {}
            }
        },
        "/order-api/card/equity-receive": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "会员卡/服务包"
                ],
                "summary": "权益领取",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.CardEquityReceiveReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.CardBaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.CardBaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/card/new": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "会员卡/服务包"
                ],
                "summary": "会员卡/服务包 开卡",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.CardNewReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.CardNewRes"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.CardNewRes"
                        }
                    }
                }
            }
        },
        "/order-api/card/new-by-code": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "会员卡/服务包"
                ],
                "summary": "会员卡通过卡密开卡",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.CardNewByCodeReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.CardBaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.CardBaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/card/query-sign-id": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "会员卡/服务包"
                ],
                "summary": "查询签约ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订单号",
                        "name": "order_sn",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "前端不用传，用户id",
                        "name": "scrm_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.QuerySignIdRes"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.QuerySignIdRes"
                        }
                    }
                }
            }
        },
        "/order-api/card/service-pack-activity": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "会员卡/服务包"
                ],
                "summary": "服务包激活",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.CardServicePackActivityReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.CardBaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.CardBaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/health/cards-list": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康管理"
                ],
                "summary": "健康管理- 卡权益查询",
                "parameters": [
                    {
                        "description": " ",
                        "name": "EnsureCardsListReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.EnsureCardsListReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.EnsureCardsList"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.EnsureCardsList"
                        }
                    }
                }
            }
        },
        "/order-api/health/company-category": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康管理"
                ],
                "summary": "获取健康管理订阅套餐信息",
                "parameters": [
                    {
                        "description": " ",
                        "name": "HealthSubOrderRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CompanyCategoryReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.GetCompanyCategoryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.GetCompanyCategoryResponse"
                        }
                    }
                }
            }
        },
        "/order-api/health/get": {
            "get": {
                "responses": {}
            }
        },
        "/order-api/health/order-submit": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康管理"
                ],
                "summary": "健康管理订阅订单提交",
                "parameters": [
                    {
                        "description": " ",
                        "name": "HealthSubOrderRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.HealthSubOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.HealthSubOrderResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.HealthSubOrderResponse"
                        }
                    }
                }
            }
        },
        "/order-api/health/refund-amount": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康管理"
                ],
                "summary": "健康管理- 退款预算",
                "parameters": [
                    {
                        "description": " ",
                        "name": "RefundHealthPlanCardRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.RefundAmountReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.RefundHealthAmountResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.RefundHealthAmountResponse"
                        }
                    }
                }
            }
        },
        "/order-api/health/refund-card": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康管理"
                ],
                "summary": "健康管理-退卡",
                "parameters": [
                    {
                        "description": " ",
                        "name": "RefundHealthPlanCardReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.RefundHealthPlanCardReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/health/set-meal": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "健康管理"
                ],
                "summary": "获取健康管理订阅套餐信息（带详情）",
                "parameters": [
                    {
                        "description": " ",
                        "name": "HealthSubOrderRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SetMealReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.GetSetMealsRes"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.GetSetMealsRes"
                        }
                    }
                }
            }
        },
        "/order-api/invoice-title/add": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "发票抬头"
                ],
                "summary": "添加票抬头",
                "parameters": [
                    {
                        "description": " ",
                        "name": "InvoiceTitleAdd",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceTitleAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceTitleAddResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceTitleAddResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice-title/list": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "发票抬头"
                ],
                "summary": "发票抬头列表",
                "parameters": [
                    {
                        "description": " ",
                        "name": "InvoiceTitleList",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceTitleListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceTitleListResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceTitleListResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice/apply": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "开票"
                ],
                "summary": "发票申请",
                "parameters": [
                    {
                        "description": " ",
                        "name": "InvoiceApply",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.CreateInvoiceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice/companies": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "开票"
                ],
                "summary": "模糊查询公司",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "keyword",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.QueryInvoiceCompanyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice/company-info": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "开票"
                ],
                "summary": "通过编码查询企业信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "企业编码",
                        "name": "code",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceCompanyInfoResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceCompanyInfoResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice/detail": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "开票"
                ],
                "summary": "发票详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订单号",
                        "name": "order_sn",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceDetailResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceDetailResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice/refund": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "开票"
                ],
                "summary": "发票退款，红冲",
                "parameters": [
                    {
                        "description": " ",
                        "name": "InvoiceSendEmail",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.RefundInvoiceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice/send-email": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "开票"
                ],
                "summary": "发送邮件",
                "parameters": [
                    {
                        "description": " ",
                        "name": "InvoiceSendEmail",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceSendEmailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceResponse"
                        }
                    }
                }
            }
        },
        "/order-api/invoice/status": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "开票"
                ],
                "summary": "开票状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订单号",
                        "name": "order_sn",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceStatusResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.InvoiceStatusResponse"
                        }
                    }
                }
            }
        },
        "/order-api/oms/delivered": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "OMS"
                ],
                "summary": "oms订单出库成功 回调",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.PushOmsRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PushOmsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/OrderPayCompleteTemporary": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "完成订单",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.OrderPayRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/cancle": {
            "post": {
                "tags": [
                    "订单"
                ],
                "summary": "订单取消",
                "parameters": [
                    {
                        "description": " ",
                        "name": "AwenOrderCancleRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.AwenOrderCancleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/docommit": {
            "post": {
                "description": "Header头部添加group_id参数，社区团购id",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "电商和小程序订单通用下单",
                "parameters": [
                    {
                        "description": " ",
                        "name": "OrderSubmitRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.OrderSubmitRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.OrderSubmitResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.OrderSubmitResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/expresscompanylist": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单物流"
                ],
                "summary": "物流公司列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "每页显示数量",
                        "name": "page_size",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "当前页码",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "物流公司名称",
                        "name": "keyword",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.ExpressCompanyListResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.ExpressCompanyListResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/expressinfo": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单物流"
                ],
                "summary": "物流路由对接",
                "parameters": [
                    {
                        "type": "string",
                        "description": "物流单号",
                        "name": "express_no",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "订单号",
                        "name": "order_sn",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "服务单号",
                        "name": "refund_sn",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索类型 默认空：售后服务物流 1:子单号 2：父单号",
                        "name": "search_type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.ExpressInfoResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.ExpressInfoResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/expressinfoupdate": {
            "post": {
                "tags": [
                    "订单物流"
                ],
                "summary": "物流信息更新",
                "parameters": [
                    {
                        "description": " ",
                        "name": "ExpressInfoUpdateRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.ExpressInfoUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/link": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "新订单链路值保存",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dac.OrderLinkStoreReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dac.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dac.Response"
                        }
                    }
                }
            }
        },
        "/order-api/order/offlinenotify": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "订单支付成功回调",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.NotifyRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NotifyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.NotifyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.NotifyResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/pay": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "订单支付",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.OrderPayRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/pay-query": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "订单支付状态查询",
                "parameters": [
                    {
                        "type": "string",
                        "description": "order-api/order/pay接口返回的order_id",
                        "name": "order_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.AwenOrderPayQueryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/pay/standard": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "订单标准支付",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.PayRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PayRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.StandardPayResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.StandardPayResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/paydyb2c": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "订单支付（电银B2C）",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.OrderPayRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayDyB2CRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.OrderPayResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/paynotify": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "电银订单支付成功回调",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.NotifyRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DYPayNotifyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.PayNotifyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.PayNotifyResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/pinpaynotify": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "拼团订单支付成功回调",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.NotifyRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NotifyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.PayNotifyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.PayNotifyResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/pre-sale/push-integral": {
            "post": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "预售订单定金积分",
                "parameters": [
                    {
                        "description": " ",
                        "name": "mallPushPreSaleRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MallPushPreSaleIntegralRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/pre-sale/push-message": {
            "post": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "预售订单提醒尾款支付订阅消息",
                "parameters": [
                    {
                        "description": " ",
                        "name": "mallPushPreSaleRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MallPushPreSaleMessageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/prescribe": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "开处方单",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.OrderPrescribeReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.OrderPrescribeRes"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.OrderPrescribeRes"
                        }
                    }
                }
            }
        },
        "/order-api/order/prescribe-check": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "检查是否开过处方单",
                "parameters": [
                    {
                        "description": " ",
                        "name": "model",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.OrderPrescribeCheckReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.OrderPrescribeCheckRes"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.OrderPrescribeCheckRes"
                        }
                    }
                }
            }
        },
        "/order-api/order/re-push-oms": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "重推OMS，有无库存都可以调用",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.PushOmsRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PushOmsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/rider-location/get": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单物流"
                ],
                "summary": "获取美团骑手当前所在的地址",
                "parameters": [
                    {
                        "description": " ",
                        "name": "ExpressInfoUpdateRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.RiderLocationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.ExternalResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.ExternalResponse"
                        }
                    }
                }
            }
        },
        "/order-api/order/step-log": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "下单前浏览链路统计",
                "parameters": [
                    {
                        "description": " ",
                        "name": "dac.OrderStepLogRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dac.OrderStepLogRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dac.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dac.Response"
                        }
                    }
                }
            }
        },
        "/order-api/order/subscribe-log": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订单"
                ],
                "summary": "提交订单后收集订阅消息",
                "parameters": [
                    {
                        "description": " ",
                        "name": "dac.OrderSubscribeLogRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dac.OrderSubscribeLogRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dac.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/dac.Response"
                        }
                    }
                }
            }
        },
        "/order-api/order/verify-codes": {
            "get": {
                "consumes": [
                    "text/plain",
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "虚拟订单核销"
                ],
                "summary": "根据第三方订单号查询核销码信息-v6.0",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "当前多少页 从1开始 不传默认为1 ",
                        "name": "pageIndex",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页多少条数据 不传默认为15",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "第三方（美团 京东 饿了么）订单号",
                        "name": "orderSn",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "商品名称（支持模糊搜索）",
                        "name": "productName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "核销码状态 -1 所有 0未核销, 1已核销, 2已退款 不传默认为0 ",
                        "name": "verifyStatus",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.GetVerifyCodeByOldOrderSnRes"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.GetVerifyCodeByOldOrderSnRes"
                        }
                    }
                }
            }
        },
        "/order-api/refund/apply": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "售后单"
                ],
                "summary": "申请售后单",
                "parameters": [
                    {
                        "description": " ",
                        "name": "apply",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.RefundOrderApplyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.RefundOrderApplyResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.RefundOrderApplyResponse"
                        }
                    }
                }
            }
        },
        "/order-api/refund/cancel": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "售后单"
                ],
                "summary": "撤销申请售后单",
                "parameters": [
                    {
                        "description": " ",
                        "name": "cancel",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.RefundOrderCancelRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/refund/pay": {
            "get": {
                "responses": {}
            }
        },
        "/order-api/subscribe-message/push": {
            "post": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅消息"
                ],
                "summary": "订阅消息推送",
                "parameters": [
                    {
                        "type": "string",
                        "description": "OpenId",
                        "name": "openId",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "订单号",
                        "name": "orderSn",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "模板ID",
                        "name": "templateId",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "推送类型",
                        "name": "pushType",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "备注",
                        "name": "remarks",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "退款ID",
                        "name": "refundId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "退款类型",
                        "name": "refundType",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "退款金额",
                        "name": "refundAmount",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "退款时间",
                        "name": "refundTime",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "退款订单号",
                        "name": "refundSn",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "开始时间",
                        "name": "startTime",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "结束时间",
                        "name": "endTime",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "是否虚拟订单",
                        "name": "isVirtual",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/subscribe-message/send": {
            "post": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅消息"
                ],
                "summary": "推送订阅消息（多条件）",
                "parameters": [
                    {
                        "description": " ",
                        "name": "models.NotifyRequest",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.PushTemplateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/CalcPromotionMoney": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "根据购物车商品计算优惠信息以及运费",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/mk.PromotionCalcRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/mk.PromotionCalcResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/ConfirmUpetDj": {
            "put": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "修改订单状态，确认收货",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjConfirmRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjConfirmResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/GetDeliveryMoney": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "宠物SAAS"
                ],
                "summary": "计算运费",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/mk.PromotionCalcRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/mk.PromotionCalcResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/PrintWithOrderId": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "根据门店配置是否自动打印配置推送给前端管理员",
                "parameters": [
                    {
                        "description": "订单号码",
                        "name": "orderId",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/QuerySection": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "加载省市区列表",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.SectionQueryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.SectionQueryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/QueryUpetDjOrderDetail": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "查询阿闻到家订单详情-v6.0",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderDetailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderDetailResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/QueryUpetDjOrderIsAutoPrint": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "根据订单号，查询门店信息中当前门店的订单是否需要自动打单",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderIsAutoPrintQueryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderIsAutoPrintQueryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/QueryUpetDjOrderList": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "查询阿闻到家订单-v6.0",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderQueryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderQueryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/QueryUpetDjOrderListByModile": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "查询阿闻到家订单",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderQueryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.UpetDjOrderQueryResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/QueryUpetDjShopDelivery": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "查询店铺的配送配置",
                "parameters": [
                    {
                        "description": "数据请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dac.ShopDeliveryServiceDetailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dac.ShopDeliveryServiceDetailResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/pickup-station/nearest": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "社区团购最近的站点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "门店财务编码",
                        "name": "finance_code",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "地址纬度",
                        "name": "lat",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "地址经度",
                        "name": "lng",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dac.AWStationNearestResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/pickup-station/state": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "社区团购站点状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "门店财务编码",
                        "name": "finance_code",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "站点id",
                        "name": "station_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dac.AWStationStateResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/upetDj/pickup-stations": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "阿闻到家"
                ],
                "summary": "社区团购附近站点查询",
                "parameters": [
                    {
                        "type": "string",
                        "description": "门店财务编码",
                        "name": "finance_code",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键字",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "地址纬度",
                        "name": "lat",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "地址经度",
                        "name": "lng",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "当前页码",
                        "name": "page_index",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dac.AWStationsResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.BadRequestResponse"
                        }
                    }
                }
            }
        },
        "/order-api/user/token": {
            "get": {
                "consumes": [
                    "text/plain"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "SCRM用户相关"
                ],
                "summary": "SCRM用户token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户手机号",
                        "name": "user_mobile",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        },
        "/order/order/get": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "虚拟订单核销"
                ],
                "summary": "查询需要核销的订单信息 -- 只支持核销码查询接口",
                "parameters": [
                    {
                        "type": "string",
                        "description": "核销码",
                        "name": "writtenoffcode",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.OldOrderDetail"
                        }
                    },
                    "400": {
                        "description": "核销码不能为空",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/order/order/virtual-order-write-off-codes": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "虚拟订单核销"
                ],
                "summary": "获取用户未核销记录",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "当前页",
                        "name": "page_index",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页记录数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "SCRM会员编码",
                        "name": "scrm_user_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "类型 不传默认全部，1、储值卡",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "订单号",
                        "name": "vr_order_sn",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/oc.QueryMallVirtualOrderWriteOffCodesResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/oc.QueryMallVirtualOrderWriteOffCodesResponse"
                        }
                    }
                }
            }
        },
        "/order/order/written-off": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "虚拟订单核销"
                ],
                "summary": "核销虚拟信息",
                "parameters": [
                    {
                        "description": " ",
                        "name": "WriteOffVirtualOrder",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.WriteOffReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.BaseResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dac.AWStationNearestResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/dac.AWStationNearestResponse_Nearest"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dac.AWStationNearestResponse_Nearest": {
            "type": "object",
            "properties": {
                "distance": {
                    "description": "距离，自带单位，如km",
                    "type": "string"
                },
                "expected_desc": {
                    "description": "预计送达时间描述，如成团当日",
                    "type": "string"
                },
                "expected_time": {
                    "description": "预计送达时间",
                    "type": "string"
                },
                "id": {
                    "description": "站点id",
                    "type": "integer"
                },
                "name": {
                    "description": "站点名称",
                    "type": "string"
                },
                "paid_count": {
                    "description": "已支付订单计数",
                    "type": "integer"
                },
                "progress_notice": {
                    "description": "进度提醒 已支付x单，16:00前还需成交y单才可成团",
                    "type": "string"
                },
                "remain": {
                    "description": "还需数量",
                    "type": "integer"
                }
            }
        },
        "dac.AWStationStateResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/dac.AWStationStateResponse_State"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dac.AWStationStateResponse_State": {
            "type": "object",
            "properties": {
                "expected_desc": {
                    "description": "预计送达时间描述，如成团当日",
                    "type": "string"
                },
                "expected_time": {
                    "description": "预计送达时间",
                    "type": "string"
                },
                "id": {
                    "description": "站点id",
                    "type": "integer"
                },
                "name": {
                    "description": "站点名称",
                    "type": "string"
                },
                "progress_notice": {
                    "description": "进度提醒 已支付x单，16:00前还需成交y单才可成团",
                    "type": "string"
                }
            }
        },
        "dac.AWStationsResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dac.AWStationsResponse_Station"
                    }
                },
                "message": {
                    "type": "string"
                },
                "total": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "dac.AWStationsResponse_Station": {
            "type": "object",
            "properties": {
                "address": {
                    "description": "站点地址",
                    "type": "string"
                },
                "distance": {
                    "description": "距离，自带单位，如km",
                    "type": "string"
                },
                "id": {
                    "description": "站点id",
                    "type": "integer"
                },
                "name": {
                    "description": "站点名称",
                    "type": "string"
                }
            }
        },
        "dac.DeliveryDistance": {
            "type": "object",
            "properties": {
                "Fee": {
                    "description": "价格，分",
                    "type": "integer"
                },
                "begin": {
                    "description": "起始距离",
                    "type": "number"
                },
                "channel_Id": {
                    "description": "渠道id",
                    "type": "integer"
                },
                "end": {
                    "description": "结束距离）",
                    "type": "number"
                },
                "finance_Code": {
                    "description": "财务ID",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "dac.DeliverySpecialtimefee": {
            "type": "object",
            "properties": {
                "base_fee": {
                    "description": "起送价，分",
                    "type": "integer"
                },
                "begin_hour": {
                    "description": "起始时间，小时",
                    "type": "integer"
                },
                "begin_minute": {
                    "description": "起始时间，分",
                    "type": "integer"
                },
                "channel_Id": {
                    "description": "渠道id",
                    "type": "integer"
                },
                "delivery_fee": {
                    "description": "配送费，分",
                    "type": "integer"
                },
                "end_hour": {
                    "description": "结束时间，小时",
                    "type": "integer"
                },
                "end_minute": {
                    "description": "结束时间，分",
                    "type": "integer"
                },
                "finance_Code": {
                    "description": "财务ID",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "dac.DeliveryWeight": {
            "type": "object",
            "properties": {
                "Fee": {
                    "description": "价格，分",
                    "type": "integer"
                },
                "begin": {
                    "description": "起始重量",
                    "type": "number"
                },
                "channel_Id": {
                    "description": "渠道id",
                    "type": "integer"
                },
                "end": {
                    "description": "结束重量",
                    "type": "number"
                },
                "finance_Code": {
                    "description": "财务ID",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "weight": {
                    "description": "超出重量",
                    "type": "number"
                }
            }
        },
        "dac.OrderLinkStoreReq": {
            "type": "object",
            "properties": {
                "order_amount": {
                    "description": "订单金额，单位分",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                },
                "product_type": {
                    "description": "商品类型，1实物、2虚拟、3到家",
                    "type": "integer"
                },
                "scene": {
                    "description": "微信场景值",
                    "type": "integer"
                },
                "scrm_id": {
                    "description": "用户id，前端不需要传",
                    "type": "string"
                },
                "steps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dac.OrderLinkStoreReq_Step"
                    }
                }
            }
        },
        "dac.OrderLinkStoreReq_Step": {
            "type": "object",
            "properties": {
                "path": {
                    "description": "路径",
                    "type": "string"
                },
                "query": {
                    "description": "参数",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                }
            }
        },
        "dac.OrderStepLogRequest": {
            "type": "object",
            "properties": {
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                },
                "step": {
                    "description": "步骤json字符串",
                    "type": "string"
                },
                "type": {
                    "description": "1-微信下单，2-scrm用",
                    "type": "string"
                }
            }
        },
        "dac.OrderSubscribeLogRequest": {
            "type": "object",
            "properties": {
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                },
                "status": {
                    "description": "用户订阅状态，默认0，1-允许，2-拒绝",
                    "type": "integer"
                }
            }
        },
        "dac.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "dac.ShopDeliveryServiceDetail": {
            "type": "object",
            "properties": {
                "Business_closetime": {
                    "description": "营业结束时间",
                    "type": "integer"
                },
                "Business_opentime": {
                    "description": "营业开始时间",
                    "type": "integer"
                },
                "Businessdate": {
                    "description": "营业日期",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "basefee": {
                    "description": "基础起送价,分",
                    "type": "integer"
                },
                "channel_Id": {
                    "description": "渠道id",
                    "type": "integer"
                },
                "chargetype": {
                    "description": "收费模式：0按距离收取配送费",
                    "type": "integer"
                },
                "deliveryDistance": {
                    "description": "动态配送费用,距离加价",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dac.DeliveryDistance"
                    }
                },
                "deliverySpecialtimefee": {
                    "description": "特殊时段配送费用",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dac.DeliverySpecialtimefee"
                    }
                },
                "deliveryWeight": {
                    "description": "动态配送费用,重量加价",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dac.DeliveryWeight"
                    }
                },
                "deliveryarea": {
                    "description": "配送区域,经纬度坐标点",
                    "type": "string"
                },
                "deliveryfee": {
                    "description": "基础配送价，分",
                    "type": "integer"
                },
                "enable_pickup": {
                    "description": "当前店铺是否开启了社区配送",
                    "type": "integer"
                },
                "finance_Code": {
                    "description": "财务编码",
                    "type": "string"
                },
                "id": {
                    "description": "id，新增传0",
                    "type": "integer"
                },
                "image": {
                    "description": "店铺头像",
                    "type": "string"
                },
                "zilong_id": {
                    "description": "子龙门店id",
                    "type": "string"
                }
            }
        },
        "dac.ShopDeliveryServiceDetailRequest": {
            "type": "object",
            "properties": {
                "channel_Id": {
                    "description": "渠道ID，必填",
                    "type": "integer"
                },
                "finance_Code": {
                    "description": "财务编码",
                    "type": "string"
                }
            }
        },
        "dac.ShopDeliveryServiceDetailResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/dac.ShopDeliveryServiceDetail"
                },
                "error": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "mk.Code": {
            "type": "integer",
            "enum": [
                0,
                200,
                400,
                201,
                404,
                403,
                300,
                401,
                402
            ],
            "x-enum-varnames": [
                "Code_default",
                "Code_success",
                "Code_serverException",
                "Code_userNotAuthority",
                "Code_grpcConnectionError",
                "Code_parameterError",
                "Code_businessError",
                "Code_saveDbException",
                "Code_queryDbException"
            ]
        },
        "mk.PromotionCalcDto": {
            "type": "object",
            "properties": {
                "promotionFee": {
                    "description": "金额",
                    "type": "integer"
                },
                "promotionId": {
                    "description": "促销活动",
                    "type": "integer"
                },
                "promotionTitle": {
                    "description": "名称",
                    "type": "string"
                },
                "promotionType": {
                    "description": "类型 1满减、2限时折扣、4会员价",
                    "type": "integer"
                }
            }
        },
        "mk.PromotionCalcProductDto": {
            "type": "object",
            "properties": {
                "count": {
                    "description": "数量",
                    "type": "integer"
                },
                "discountCount": {
                    "description": "参与折扣的数量",
                    "type": "integer"
                },
                "discountPrice": {
                    "description": "折扣后商品的价格",
                    "type": "integer"
                },
                "onlyVipDiscountPrice": {
                    "description": "仅vip优惠价格，用于超出限购恢复原价，同时用于标识会员价",
                    "type": "integer"
                },
                "price": {
                    "description": "单价",
                    "type": "integer"
                },
                "promotionId": {
                    "description": "促销活动Id",
                    "type": "integer"
                },
                "promotionType": {
                    "description": "促销活动Id",
                    "type": "integer"
                },
                "skuId": {
                    "description": "skuId",
                    "type": "string"
                },
                "sumMoney": {
                    "description": "总金额",
                    "type": "integer"
                }
            }
        },
        "mk.PromotionCalcRequest": {
            "type": "object",
            "properties": {
                "channelId": {
                    "description": "渠道Id",
                    "type": "integer"
                },
                "destinationX": {
                    "description": "目的地坐标X 不传递或传递0，不计算运费",
                    "type": "number"
                },
                "destinationY": {
                    "description": "目的地坐标Y 不传递或传递0，不计算运费",
                    "type": "number"
                },
                "isNewUser": {
                    "description": "是否是新用户",
                    "type": "boolean"
                },
                "promotionProduct": {
                    "description": "相关商品",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/mk.PromotionCalcProductDto"
                    }
                },
                "shopId": {
                    "description": "用户所属店铺ID",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户id",
                    "type": "string"
                }
            }
        },
        "mk.PromotionCalcResponse": {
            "type": "object",
            "properties": {
                "actualMoneyByMinUnit": {
                    "description": "实付金额（商品的实收总金额,不包含运费） 以分为单位",
                    "type": "integer"
                },
                "calcList": {
                    "description": "优惠信息定义明细",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/mk.PromotionCalcDto"
                    }
                },
                "code": {
                    "description": "代码 非 200 取 message 错误信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/mk.Code"
                        }
                    ]
                },
                "message": {
                    "description": "错误信息",
                    "type": "string"
                },
                "promotionProduct": {
                    "description": "相关商品",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/mk.PromotionCalcProductDto"
                    }
                },
                "promotionReduceList": {
                    "description": "符合条件的满减信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/mk.PromotionReduceDto"
                    }
                },
                "reduceDelivery": {
                    "description": "符合条件的满减运费信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/mk.PromotionReduceDeliveryDto"
                        }
                    ]
                },
                "reduceMoneyByMinUnit": {
                    "description": "优惠金额（参与商品优惠的金额，不包含运费优惠） 以分为单位",
                    "type": "integer"
                },
                "timeDiscount": {
                    "description": "符合条件的限时优惠信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/mk.PromotionTimeDiscountDto"
                    }
                },
                "timeDiscountCalcProductCount": {
                    "description": "已经参与限时折扣的商品种类数量",
                    "type": "integer"
                },
                "timeDiscountProductCount": {
                    "description": "能参与限时折扣商品种类数量",
                    "type": "integer"
                },
                "totalMoneyByMinUnit": {
                    "description": "总金额(商品总金额,不包含运费) 以分为单位",
                    "type": "integer"
                },
                "totalWeight": {
                    "description": "总重量",
                    "type": "integer"
                },
                "upetActualDjMoneyByMinUnit": {
                    "description": "总运费金额实收,以分为单位",
                    "type": "integer"
                },
                "upetDjMoneyByMinUnit": {
                    "description": "总运费金额,以分为单位",
                    "type": "integer"
                }
            }
        },
        "mk.PromotionReduceDeliveryDto": {
            "type": "object",
            "properties": {
                "ReachMoney": {
                    "description": "满足减免最小金额",
                    "type": "number"
                },
                "ReduceDeliveryType": {
                    "description": "0 普通阶梯递减 1 最高阶梯免配送费",
                    "type": "integer"
                },
                "ReduceMoney": {
                    "description": "减免金额",
                    "type": "number"
                },
                "promotionId": {
                    "description": "促销活动Id",
                    "type": "integer"
                }
            }
        },
        "mk.PromotionReduceDto": {
            "type": "object",
            "properties": {
                "promotionId": {
                    "description": "促销活动Id",
                    "type": "integer"
                },
                "reachMoney": {
                    "description": "最小金额",
                    "type": "number"
                },
                "reduceMoney": {
                    "description": "减免金额",
                    "type": "number"
                }
            }
        },
        "mk.PromotionTimeDiscountDto": {
            "type": "object",
            "properties": {
                "ConfigBuyCount": {
                    "description": "配置的活动购买数量",
                    "type": "integer"
                },
                "DiscountValue": {
                    "description": "为 0 时代表折扣 为 1 代表固定价格 (统一传浮点数)",
                    "type": "number"
                },
                "DisountType": {
                    "description": "折扣类型  0 按折扣 固定活动价格",
                    "type": "integer"
                },
                "LimitCountByOrder": {
                    "description": "单限购 0 不限制, 非0  限制多少数量",
                    "type": "integer"
                },
                "LimitCountByStock": {
                    "description": "当日限购 0 不限制,非0 限时库存数量",
                    "type": "integer"
                },
                "UserType": {
                    "description": "用户类型 0 全部 1 新客户",
                    "type": "integer"
                },
                "VipDiscount": {
                    "description": "会员额外折扣",
                    "type": "number"
                },
                "promotionId": {
                    "description": "促销活动Id",
                    "type": "integer"
                }
            }
        },
        "models.ApplyList": {
            "type": "object",
            "properties": {
                "can_refund": {
                    "description": "能否退款，0否1是",
                    "type": "integer"
                },
                "freight": {
                    "description": "总运费",
                    "type": "string"
                },
                "goods_total": {
                    "description": "商品总金额（未加运费，未加包装费，服务费,减优惠金额）",
                    "type": "string"
                },
                "has_refund_record": {
                    "description": "是否有退款记录，0否1是",
                    "type": "integer"
                },
                "is_out_date": {
                    "description": "是否超过售后时间，0否1是",
                    "type": "integer"
                },
                "is_virtual": {
                    "description": "是否虚拟订单",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "订单编号",
                    "type": "string"
                },
                "order_status_child": {
                    "description": "子状态： 20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;",
                    "type": "integer"
                },
                "product": {
                    "description": "订单商品信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Product"
                    }
                },
                "shop_id": {
                    "description": "店铺id",
                    "type": "string"
                },
                "shop_name": {
                    "description": "店铺名称",
                    "type": "string"
                },
                "total": {
                    "description": "总金额（实际付款金额。加运费，加包装费，减优惠金额）",
                    "type": "string"
                }
            }
        },
        "models.ApplyListResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.ApplyList"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.BaseResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.CannotSumbitProduct": {
            "type": "object",
            "properties": {
                "sku_id": {
                    "description": "商品skuid",
                    "type": "string"
                },
                "status": {
                    "description": "1:无货 2:失效 3:下架",
                    "type": "integer"
                }
            }
        },
        "models.CompanyCategoryData": {
            "type": "object",
            "properties": {
                "applyAge": {
                    "description": "适用年龄(1-幼年;2-成年;3-老年)",
                    "type": "integer"
                },
                "cardType": {
                    "description": "套餐类型",
                    "type": "integer"
                },
                "cardTypeCapiton": {
                    "description": "套餐类型说明",
                    "type": "string"
                },
                "categoryAbbreviation": {
                    "description": "简拼",
                    "type": "string"
                },
                "categoryCode": {
                    "description": "套餐编码",
                    "type": "string"
                },
                "categoryName": {
                    "description": "套餐名称",
                    "type": "string"
                },
                "categoryStatus": {
                    "description": "套餐状态 （1-待售；2-在售；3-停售）",
                    "type": "integer"
                },
                "categoryStatusCaption": {
                    "description": "套餐状态说明",
                    "type": "string"
                },
                "costMoney": {
                    "description": "金额",
                    "type": "number"
                },
                "createName": {
                    "description": "创建人",
                    "type": "string"
                },
                "createTime": {
                    "description": "创建时间",
                    "type": "string"
                },
                "discountRate": {
                    "description": "折扣率",
                    "type": "number"
                },
                "effectTime": {
                    "description": "套餐在售期间（生效时间）",
                    "type": "string"
                },
                "id": {
                    "description": "id",
                    "type": "integer"
                },
                "itemNO": {
                    "description": "货号",
                    "type": "string"
                },
                "kindof": {
                    "description": "宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)",
                    "type": "integer"
                },
                "loseEffectTime": {
                    "description": "套餐在售期间（失效时间）",
                    "type": "string"
                },
                "originalPrice": {
                    "description": "原价",
                    "type": "number"
                },
                "priceType": {
                    "description": "价格类型 （1 一口价；2 折扣）",
                    "type": "integer"
                },
                "priceTypeCapiton": {
                    "description": "价格类型说明",
                    "type": "string"
                },
                "renewal": {
                    "description": "是否支持续费",
                    "type": "integer"
                },
                "structCode": {
                    "description": "所属组织",
                    "type": "string"
                },
                "structName": {
                    "description": "所属组织名称",
                    "type": "string"
                }
            }
        },
        "models.CompanyCategoryReq": {
            "type": "object",
            "properties": {
                "applyAge": {
                    "description": "适用年龄(1-幼年;2-成年;3-老年)",
                    "type": "integer"
                },
                "cardType": {
                    "description": "套餐类别",
                    "type": "integer"
                },
                "categoryCode": {
                    "description": "套餐编码",
                    "type": "string"
                },
                "kingof": {
                    "description": "宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)",
                    "type": "integer"
                },
                "pageIndex": {
                    "description": "适用医院\nEppCode []string ` + "`" + `json:\"eppCode\"` + "`" + `\n页码",
                    "type": "integer"
                },
                "pageSize": {
                    "description": "每页条数",
                    "type": "integer"
                },
                "payType": {
                    "description": "支付方式(1-月付;2-季付;3-年付)",
                    "type": "integer"
                }
            }
        },
        "models.DYPayNotifyRequest": {
            "type": "object",
            "properties": {
                "orderNo": {
                    "description": "订单号",
                    "type": "string"
                },
                "payAmount": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "payStatus": {
                    "description": "支付状态",
                    "type": "integer"
                },
                "payTime": {
                    "description": "支付时间",
                    "type": "string"
                },
                "tradeNo": {
                    "description": "支付中心订单号",
                    "type": "string"
                }
            }
        },
        "models.EnsureCardsBatchDetails": {
            "type": "object",
            "properties": {
                "batchCode": {
                    "description": "批次号",
                    "type": "string"
                },
                "batchDetailCode": {
                    "description": "批次明细号",
                    "type": "string"
                },
                "categoryCode": {
                    "description": "类别编码",
                    "type": "string"
                },
                "categoryDetail": {
                    "description": "类别",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.EnsureCategoryDetail"
                        }
                    ]
                },
                "categoryDetailCode": {
                    "description": "类别明细编码",
                    "type": "string"
                },
                "chargeTimes": {
                    "description": "总次数",
                    "type": "integer"
                },
                "corpusBalance": {
                    "description": "余额",
                    "type": "number"
                },
                "costMoney": {
                    "description": "总价",
                    "type": "number"
                },
                "discountLevelVoList": {
                    "description": "预售包含的目录和产品",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.EnsureDiscountLevelVoList"
                    }
                },
                "ensureCode": {
                    "description": "卡号",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "leftTimes": {
                    "description": "剩余次数",
                    "type": "integer"
                }
            }
        },
        "models.EnsureCardsList": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.EnsureCardsListResult"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.EnsureCardsListCriteria": {
            "type": "object",
            "properties": {
                "cardType": {
                    "description": "EnsureCode   string   ` + "`" + `json:\"ensureCode\"` + "`" + `\nBatchCode    string   ` + "`" + `json:\"batchCode\"` + "`" + `\nCategoryCode string   ` + "`" + `json:\"categoryCode\"` + "`" + `\nStatus       int      ` + "`" + `json:\"status\"` + "`" + `\nCompanyCode string ` + "`" + `json:\"companyCode\"` + "`" + `",
                    "type": "integer"
                },
                "petIds": {
                    "description": "PetId        string   ` + "`" + `json:\"petId\"` + "`" + `",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "userId": {
                    "description": "HospitalCode string   ` + "`" + `json:\"hospitalCode\"` + "`" + `",
                    "type": "string"
                }
            }
        },
        "models.EnsureCardsListReq": {
            "type": "object",
            "properties": {
                "criteria": {
                    "$ref": "#/definitions/models.EnsureCardsListCriteria"
                },
                "source": {
                    "description": "来源 0子龙 1小暖 2瑞鹏",
                    "type": "integer"
                }
            }
        },
        "models.EnsureCardsListResult": {
            "type": "object",
            "properties": {
                "batchCode": {
                    "description": "批次号",
                    "type": "string"
                },
                "batchDetails": {
                    "description": "批次明细",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.EnsureCardsBatchDetails"
                    }
                },
                "batchStatus": {
                    "description": "批次状态0：未生效 1：正常 2：过期 3：退卡",
                    "type": "integer"
                },
                "categoryDiscounts": {
                    "description": "类别折扣",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.EnsureCategoryDiscounts"
                    }
                },
                "ensureCode": {
                    "description": "卡号",
                    "type": "string"
                },
                "expiryDate": {
                    "type": "string"
                },
                "hospitalCode": {
                    "description": "机构编码",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "petId": {
                    "description": "宠物ID",
                    "type": "string"
                },
                "petIdLists": {
                    "description": "宠物列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "startDate": {
                    "type": "string"
                },
                "userId": {
                    "description": "用户ID",
                    "type": "string"
                },
                "userIdLists": {
                    "description": "用户列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.EnsureCategoryDetail": {
            "type": "object",
            "properties": {
                "categoryCode": {
                    "description": "类别编码",
                    "type": "string"
                },
                "categoryDetailCode": {
                    "description": "类别明细编码",
                    "type": "string"
                },
                "corpusMoney": {
                    "description": "金额",
                    "type": "number"
                },
                "corpusTimes": {
                    "description": "购买次数",
                    "type": "integer"
                },
                "createId": {
                    "type": "integer"
                },
                "createName": {
                    "type": "string"
                },
                "createTime": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "lastUpdateTime": {
                    "type": "string"
                },
                "lastUpdateUserId": {
                    "type": "integer"
                },
                "lastUpdateUserName": {
                    "type": "string"
                },
                "originalPrice": {
                    "description": "原价",
                    "type": "number"
                },
                "productName": {
                    "description": "项目名称",
                    "type": "string"
                },
                "unitPrice": {
                    "description": "单价",
                    "type": "number"
                }
            }
        },
        "models.EnsureCategoryDiscounts": {
            "type": "object",
            "properties": {
                "categoryCode": {
                    "type": "string"
                },
                "dictId": {
                    "type": "string"
                },
                "discount": {
                    "type": "string"
                },
                "discountLevelVoList": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.EnsureDiscountLevelVoList"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "operatorId": {
                    "type": "integer"
                },
                "operatorName": {
                    "type": "string"
                },
                "operatorTime": {
                    "type": "string"
                },
                "productTypeName": {
                    "type": "string"
                }
            }
        },
        "models.EnsureDiscountLevelVoList": {
            "type": "object",
            "properties": {
                "productCode": {
                    "type": "string"
                },
                "type": {
                    "type": "integer"
                }
            }
        },
        "models.GetCompanyCategoryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CompanyCategoryData"
                    }
                },
                "message": {
                    "type": "string"
                },
                "pageCount": {
                    "description": "每页条数",
                    "type": "integer"
                },
                "pageIndex": {
                    "description": "页码",
                    "type": "integer"
                },
                "pageSize": {
                    "description": "每页条数",
                    "type": "integer"
                },
                "total": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "models.GetSetMealsRes": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "array",
                        "items": {
                            "$ref": "#/definitions/models.SetMealData"
                        }
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.GetVerifyCodeByOldOrderSnRes": {
            "type": "object",
            "properties": {
                "message": {
                    "description": "提示信息",
                    "type": "string"
                },
                "result": {
                    "description": "核销码结果集",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.VerifyCodes"
                    }
                },
                "statusCode": {
                    "description": "状态码:200正常 异常",
                    "type": "integer"
                },
                "total": {
                    "description": "满足查询条件的总数据条数",
                    "type": "integer"
                }
            }
        },
        "models.MallPushPreSaleIntegralRequest": {
            "type": "object",
            "properties": {
                "order_sn": {
                    "type": "string"
                },
                "pay_price": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "pay_type": {
                    "description": "支付类型 0：加定金积分 1：退定金积分",
                    "type": "integer"
                }
            }
        },
        "models.MallPushPreSaleMessageRequest": {
            "type": "object",
            "properties": {
                "order_sn": {
                    "type": "string"
                },
                "send_message": {
                    "description": "发送尾款消息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.PreSalePay"
                        }
                    ]
                }
            }
        },
        "models.NotifyRequest": {
            "type": "object",
            "properties": {
                "addTime": {
                    "description": "添加时间",
                    "type": "string"
                },
                "channelNo": {
                    "description": "扣款通道返回的流水号",
                    "type": "string"
                },
                "clientIP": {
                    "description": "客户端 IP",
                    "type": "string"
                },
                "discount": {
                    "description": "优惠金额 单位分",
                    "type": "string"
                },
                "extendInfo": {
                    "description": "扩展信息 预留字段，JSON 格式",
                    "type": "string"
                },
                "merchantId": {
                    "description": "商户号",
                    "type": "string"
                },
                "offlineNotifyUrl": {
                    "description": "后台回调地址",
                    "type": "string"
                },
                "orderId": {
                    "description": "订单号",
                    "type": "string"
                },
                "orderTime": {
                    "description": "订单日期格式：YYYYMMDD",
                    "type": "string"
                },
                "outTradeNo": {
                    "description": "商户流水号,仅能用大小写字母与数,字，且在商户系统具有唯一性",
                    "type": "string"
                },
                "payPrice": {
                    "description": "实付金额 单位分",
                    "type": "string"
                },
                "payTime": {
                    "description": "支付时间",
                    "type": "string"
                },
                "payType": {
                    "description": "支付方式  1：微信 JSAPI，2：C扫B，3：B扫C,8:储蓄卡支付",
                    "type": "string"
                },
                "productDesc": {
                    "description": "商品描述",
                    "type": "string"
                },
                "productId": {
                    "description": "商品编号",
                    "type": "string"
                },
                "productName": {
                    "description": "商品名称 最长 32 字节",
                    "type": "string"
                },
                "sign": {
                    "description": "签名",
                    "type": "string"
                },
                "totalPrice": {
                    "description": "订单金额 单位分",
                    "type": "string"
                },
                "tradeNo": {
                    "description": "支付平台流水号",
                    "type": "string"
                }
            }
        },
        "models.NotifyResponse": {
            "type": "object",
            "properties": {
                "result": {
                    "type": "string"
                }
            }
        },
        "models.OldOrderDetail": {
            "type": "object",
            "properties": {
                "address": {
                    "description": "收件地址",
                    "type": "string"
                },
                "belonghospitalid": {
                    "description": "订单归属门店信息",
                    "type": "integer"
                },
                "cancelltime": {
                    "description": "取消时间",
                    "type": "string"
                },
                "channel_id": {
                    "description": "渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店",
                    "type": "integer"
                },
                "createtime": {
                    "description": "订单创建时间",
                    "type": "string"
                },
                "expresscode": {
                    "description": "物流公司编码",
                    "type": "string"
                },
                "expressmoney": {
                    "description": "油费",
                    "type": "integer"
                },
                "expressno": {
                    "description": "物流单号",
                    "type": "string"
                },
                "expressstate": {
                    "description": "物流状态",
                    "type": "integer"
                },
                "goods": {
                    "description": "商品信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OrderDetailGoods"
                    }
                },
                "isevaluate": {
                    "description": "是否有价值",
                    "type": "integer"
                },
                "isneedpost": {
                    "description": "是否需要邮寄",
                    "type": "boolean"
                },
                "isnotify": {
                    "description": "是否通知标记",
                    "type": "integer"
                },
                "ispostupet": {
                    "description": "是否推送优宠",
                    "type": "integer"
                },
                "lasttime": {
                    "description": "最后更新时间",
                    "type": "string"
                },
                "memberid": {
                    "description": "用户id",
                    "type": "string"
                },
                "memberintegrals": {
                    "description": "积分明细，暂时不要",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "membermobile": {
                    "description": "用户手机号",
                    "type": "string"
                },
                "mobile": {
                    "description": "收件人联系电话",
                    "type": "string"
                },
                "orderchildenstate": {
                    "description": "订单子状态",
                    "type": "integer"
                },
                "orderdetail": {
                    "description": "订单详情",
                    "type": "string"
                },
                "orderid": {
                    "description": "订单号",
                    "type": "string"
                },
                "ordermoney": {
                    "description": "订单金额",
                    "type": "string"
                },
                "orderstate": {
                    "description": "订单状态",
                    "type": "integer"
                },
                "ordertype": {
                    "description": "订单类型",
                    "type": "integer"
                },
                "ordertypedetail": {
                    "description": "订单类型小类1-255",
                    "type": "integer"
                },
                "ordertypename": {
                    "description": "订单类型名称",
                    "type": "string"
                },
                "paytime": {
                    "description": "支付时间",
                    "type": "string"
                },
                "payway": {
                    "description": "支付方式",
                    "type": "string"
                },
                "petid": {
                    "description": "宠物id",
                    "type": "string"
                },
                "platform": {
                    "description": "平台名称",
                    "type": "string"
                },
                "platformid": {
                    "description": "平台ID",
                    "type": "integer"
                },
                "recipient": {
                    "description": "收件人",
                    "type": "string"
                },
                "refundtime": {
                    "description": "退款时间",
                    "type": "string"
                },
                "sumquantity": {
                    "type": "integer"
                },
                "useragent": {
                    "description": "用户useragent",
                    "type": "integer"
                }
            }
        },
        "models.Order": {
            "type": "object",
            "properties": {
                "address_id": {
                    "description": "v2.9.10 添加 用户选择的收件以 原商城虚拟订单的address_id字段",
                    "type": "string"
                },
                "buyer_memo": {
                    "description": "买家留言",
                    "type": "string"
                },
                "channel_id": {
                    "description": "渠道ID：1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店,7百度,8H5,9医疗互联网",
                    "type": "integer"
                },
                "consult_order_sn": {
                    "description": "医疗互联网订单号\\处方ID\\推荐ID",
                    "type": "string"
                },
                "device_current_time": {
                    "description": "设备当前时间（竖屏排查问题用）",
                    "type": "string"
                },
                "dis_id": {
                    "description": "v2.9.10 添加 分销id 原商城虚拟订单的dis_id字段",
                    "type": "string"
                },
                "dis_type": {
                    "description": "v2.9.10 添加 1分享连接，2扫码 3 自己扫码自己 5 自主访问下单  商城分销用 原商城订单dis_type字段",
                    "type": "integer"
                },
                "expected_time": {
                    "description": "预计送达时间",
                    "type": "string"
                },
                "extraInfo": {
                    "description": "扩展字段",
                    "type": "object",
                    "properties": {
                        "avatar_url": {
                            "description": "参团头像完整地址",
                            "type": "string"
                        },
                        "dis_member_from": {
                            "description": "店铺分销员id来源 0默认 1",
                            "type": "integer"
                        },
                        "dis_member_id": {
                            "description": "店铺分销员id",
                            "type": "string"
                        },
                        "dis_shop_id": {
                            "description": "分销店铺",
                            "type": "string"
                        },
                        "group_address": {
                            "description": "团购人的地址",
                            "type": "string"
                        },
                        "group_mobile": {
                            "description": "团购人手机号",
                            "type": "string"
                        },
                        "group_name": {
                            "description": "团购人名称",
                            "type": "string"
                        },
                        "nick_name": {
                            "description": "参团昵称",
                            "type": "string"
                        }
                    }
                },
                "first_order": {
                    "description": "v2.9.10 添加 原商城虚拟订单first_order字段",
                    "type": "string"
                },
                "freight": {
                    "description": "总运费",
                    "type": "integer"
                },
                "goods_total": {
                    "description": "商品总金额（未加运费，不加包装费，减优惠金额，美团不减优惠金额）",
                    "type": "integer"
                },
                "invoice": {
                    "description": "发票信息",
                    "type": "string"
                },
                "is_split": {
                    "description": "是否有拆单，0否1是",
                    "type": "integer"
                },
                "is_virtual": {
                    "description": "是否是虚拟订单，0否1是",
                    "type": "integer"
                },
                "latitude": {
                    "description": "收货地址纬度",
                    "type": "number"
                },
                "longitude": {
                    "description": "收货地址经度",
                    "type": "number"
                },
                "old_order_sn": {
                    "description": "外部订单号",
                    "type": "string"
                },
                "open_id": {
                    "description": "v2.9.10 添加 小城原商城虚拟订单的open_id字段",
                    "type": "string"
                },
                "order_pay_type": {
                    "type": "string"
                },
                "order_type": {
                    "description": "订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划订单,7保险订单,8积分订单 9秒杀订单 , 13在线问诊，15社区团购",
                    "type": "integer"
                },
                "org_id": {
                    "description": "组织ID",
                    "type": "integer"
                },
                "pickup_station_id": {
                    "description": "社区团购站点",
                    "type": "integer"
                },
                "power_id": {
                    "description": "助力订单id（电商使用）",
                    "type": "integer"
                },
                "privilege": {
                    "description": "总优惠金额",
                    "type": "integer"
                },
                "receiver_address": {
                    "description": "收件地址",
                    "type": "string"
                },
                "receiver_city": {
                    "description": "收件市",
                    "type": "string"
                },
                "receiver_date_msg": {
                    "description": "v2.9.10 添加 希望送货时间 商城下单用 原商城订单receiver_date_msg字段",
                    "type": "string"
                },
                "receiver_district": {
                    "description": "收件区",
                    "type": "string"
                },
                "receiver_name": {
                    "description": "收件人",
                    "type": "string"
                },
                "receiver_phone": {
                    "description": "收件电话",
                    "type": "string"
                },
                "receiver_state": {
                    "description": "收件省",
                    "type": "string"
                },
                "shop_id": {
                    "description": "商户或门店id",
                    "type": "string"
                },
                "shop_name": {
                    "description": "商户名称",
                    "type": "string"
                },
                "sk_freight_encrypt": {
                    "description": "秒杀运费信息",
                    "type": "string"
                },
                "source": {
                    "description": "v2.9.10 添加 1 小程序(阿闻智慧门店) 2:阿闻宠物(北京那边用) //3阿闻商城(自用) 商城用 原商城虚拟订单的source字段",
                    "type": "integer"
                },
                "tel_phone": {
                    "description": "v2.9.10 添加 收件人座机 原商城订单tel_phone字段",
                    "type": "string"
                },
                "total": {
                    "description": "总金额（付款金额，加上运费，加包装费 减优惠金额）",
                    "type": "integer"
                },
                "total_weight": {
                    "description": "总重量(非必填)",
                    "type": "integer"
                },
                "user_agent": {
                    "description": "渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它 houduanzuo",
                    "type": "integer"
                }
            }
        },
        "models.OrderDetailGoods": {
            "type": "object",
            "properties": {
                "applyhospitalid": {
                    "description": "申请门店id",
                    "type": "string"
                },
                "barcode": {
                    "description": "商品编码",
                    "type": "string"
                },
                "chargeoff": {
                    "description": "核销状态 -- 很重要 2-待核销，3-已核销，1-不需要核销，4-已过期",
                    "type": "integer"
                },
                "chargeoffcode": {
                    "description": "核销码",
                    "type": "string"
                },
                "chargeoffhospitalid": {
                    "description": "核销医院编号ID",
                    "type": "string"
                },
                "chargeoffmemberid": {
                    "description": "核销人信息",
                    "type": "string"
                },
                "chargeoffobject": {
                    "description": "核销对象",
                    "type": "string"
                },
                "chargeoffobjectname": {
                    "description": "核销对象名称",
                    "type": "string"
                },
                "chargeofftime": {
                    "description": "核销时间",
                    "type": "string"
                },
                "createtime": {
                    "description": "创建时间",
                    "type": "string"
                },
                "expiredate": {
                    "description": "过期时间",
                    "type": "string"
                },
                "goodsid": {
                    "description": "商品货号",
                    "type": "string"
                },
                "goodsimage": {
                    "description": "商品图片地址",
                    "type": "string"
                },
                "id": {
                    "description": "子订单号",
                    "type": "string"
                },
                "isneedpush": {
                    "description": "是否推送",
                    "type": "integer"
                },
                "lasttime": {
                    "description": "最后更新时间",
                    "type": "string"
                },
                "markingPrice": {
                    "description": "商品原价",
                    "type": "integer"
                },
                "name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "orderid": {
                    "description": "订单号",
                    "type": "string"
                },
                "quantity": {
                    "description": "商品数量",
                    "type": "integer"
                },
                "sellprice": {
                    "description": "商品售价",
                    "type": "integer"
                },
                "siglegoodsimage": {
                    "description": "商品缩略图",
                    "type": "string"
                },
                "sku": {
                    "description": "skuid",
                    "type": "string"
                },
                "unit": {
                    "description": "商品单位",
                    "type": "string"
                },
                "univalence": {
                    "description": "商品原价",
                    "type": "integer"
                }
            }
        },
        "models.OrderPayDyB2CRequest": {
            "type": "object",
            "properties": {
                "bar_code": {
                    "description": "付款码",
                    "type": "string"
                },
                "location": {
                    "type": "string"
                },
                "merc_id": {
                    "description": "商户号",
                    "type": "string"
                },
                "order_sn": {
                    "description": "订单id",
                    "type": "string"
                },
                "org_id": {
                    "description": "机构号",
                    "type": "string"
                },
                "pay_type": {
                    "description": "支付方式 1：微信 2：支付宝 3: 银联",
                    "type": "integer"
                },
                "source": {
                    "description": "竖屏支付来源 1-商城 0-默认到家",
                    "type": "integer"
                },
                "trm_id": {
                    "description": "终端号",
                    "type": "string"
                },
                "trm_sn": {
                    "description": "机具编号",
                    "type": "string"
                }
            }
        },
        "models.OrderPayInfo": {
            "type": "object"
        },
        "models.OrderPayRequest": {
            "type": "object",
            "properties": {
                "app_id": {
                    "description": "appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家",
                    "type": "integer"
                },
                "openid": {
                    "description": "微信用户标识 JSAPI 支付时必传",
                    "type": "string"
                },
                "orderType": {
                    "description": "订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 99助力订单",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "订单id",
                    "type": "string"
                },
                "transType": {
                    "description": "支付方式  1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）\n13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付 17：标准支付宝小程序支付 18：百度小程序支付",
                    "type": "integer"
                }
            }
        },
        "models.OrderPayResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "create_time": {
                    "description": "下单时间(13位，毫秒)",
                    "type": "integer"
                },
                "data": {
                    "description": "返回数据",
                    "type": "string"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "models.OrderPrescribeReq": {
            "type": "object",
            "properties": {
                "diagnose": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.PrescriptionDiagnose"
                    }
                },
                "finance_code": {
                    "type": "string"
                },
                "hospital_name": {
                    "type": "string"
                },
                "operate_type": {
                    "type": "integer"
                },
                "pet_info": {
                    "$ref": "#/definitions/oc.ConsultMemberPetInfo"
                },
                "pet_weight": {
                    "type": "string"
                },
                "skus": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.OrderPrescribeSkuNum"
                    }
                }
            }
        },
        "models.OrderProductModel": {
            "type": "object",
            "properties": {
                "bar_code": {
                    "description": "商品编码",
                    "type": "string"
                },
                "child_product_list": {
                    "description": "子商品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OrderProductModel"
                    }
                },
                "combine_type": {
                    "description": "组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟",
                    "type": "integer"
                },
                "discount_count": {
                    "description": "参与限时折扣的商品数量",
                    "type": "integer"
                },
                "discount_price": {
                    "description": "折扣后的单价",
                    "type": "integer"
                },
                "group_discount_type": {
                    "description": "组合商品折扣类型(1-按折扣优惠，2-按固定价格优惠)",
                    "type": "integer"
                },
                "group_discount_value": {
                    "description": "组合商品折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）",
                    "type": "integer"
                },
                "image": {
                    "description": "商品图片",
                    "type": "string"
                },
                "is_third_product": {
                    "description": "是否是第三方商品信息 1：是  0：否 默认0",
                    "type": "integer"
                },
                "number": {
                    "description": "数量",
                    "type": "integer"
                },
                "parent_sku": {
                    "description": "组合商品父级sku",
                    "type": "string"
                },
                "pay_price": {
                    "description": "商品均摊后实际支付单价",
                    "type": "integer"
                },
                "payment_total": {
                    "description": "sku实付总金额，discount_price*number",
                    "type": "integer"
                },
                "price": {
                    "description": "单价",
                    "type": "integer"
                },
                "product_id": {
                    "description": "商品id",
                    "type": "string"
                },
                "product_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "product_type": {
                    "description": "商品类型1-实物商品，2-虚拟商品，3-组合商品",
                    "type": "integer"
                },
                "promotion_id": {
                    "description": "促销活动Id",
                    "type": "integer"
                },
                "promotion_type": {
                    "description": "活动类型1-满减商品2限时折扣3-满减运费 11 秒杀",
                    "type": "integer"
                },
                "rec_id": {
                    "description": "电商对应的商品id(电商使用)",
                    "type": "integer"
                },
                "sku": {
                    "description": "sku",
                    "type": "string"
                },
                "source": {
                    "description": "仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）",
                    "type": "integer"
                },
                "specs": {
                    "description": "规格",
                    "type": "string"
                },
                "term_type": {
                    "description": "只有虚拟商品才有值1-有效期至多少2-有效期天数",
                    "type": "integer"
                },
                "term_value": {
                    "description": "如果expire_type=1存时间戳,如果term_type=2存多少天",
                    "type": "integer"
                },
                "use_virtual_stock": {
                    "description": "是否使用了虚拟库存",
                    "type": "integer"
                },
                "vip_price": {
                    "description": "仅vip折扣价格，用于超出限购会员原价",
                    "type": "integer"
                },
                "virtual_invalid_refund": {
                    "description": "是否支持过期退款 1：是  0：否",
                    "type": "integer"
                },
                "warehouse_type": {
                    "description": "药品仓属性",
                    "type": "integer"
                }
            }
        },
        "models.OrderPromotion": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "主键Id",
                    "type": "integer"
                },
                "promotion_fee": {
                    "description": "活动优惠金额",
                    "type": "integer"
                },
                "promotion_id": {
                    "description": "促销活动Id",
                    "type": "integer"
                },
                "promotion_name": {
                    "description": "活动名称",
                    "type": "string"
                },
                "promotion_title": {
                    "description": "促销活动优惠",
                    "type": "string"
                },
                "promotion_type": {
                    "description": "活动类型 活动类型 1 满减活动 2 限时折扣 3 满减运费 11秒杀",
                    "type": "integer"
                }
            }
        },
        "models.OrderSubmitRequest": {
            "type": "object",
            "properties": {
                "order": {
                    "description": "订单",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.Order"
                        }
                    ]
                },
                "order_products": {
                    "description": "商品",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OrderProductModel"
                    }
                },
                "order_promotions": {
                    "description": "参与优惠活动信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OrderPromotion"
                    }
                },
                "pay_info": {
                    "description": "支付信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.OrderPayInfo"
                        }
                    ]
                },
                "warehouse_type": {
                    "description": "药品仓属性",
                    "type": "integer"
                }
            }
        },
        "models.OrderSubmitResponse": {
            "type": "object",
            "properties": {
                "cannot_products": {
                    "description": "无法下单商品",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CannotSumbitProduct"
                    }
                },
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "create_time": {
                    "description": "创建订单时间",
                    "type": "integer"
                },
                "doctor_code": {
                    "description": "在线问诊订单医生Code，快速咨询为空",
                    "type": "string"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                },
                "order_id": {
                    "description": "提交订单成功后的订单id",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "提交订单成功后，返回订单ID",
                    "type": "string"
                },
                "pay_sn": {
                    "description": "提交订单成功后的支付单号",
                    "type": "string"
                }
            }
        },
        "models.PayNotifyResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.PayRequest": {
            "type": "object",
            "properties": {
                "app_id": {
                    "description": "1：阿闻，2：子龙，3：R1，4：互联网",
                    "type": "integer"
                },
                "order_no": {
                    "description": "订单号",
                    "type": "string"
                },
                "order_type": {
                    "description": "订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 99助力订单",
                    "type": "integer"
                }
            }
        },
        "models.Product": {
            "type": "object",
            "properties": {
                "image": {
                    "type": "string"
                },
                "number": {
                    "description": "购买数量",
                    "type": "integer"
                },
                "order_product_id": {
                    "type": "integer"
                },
                "parent_sku_id": {
                    "description": "父商品sku",
                    "type": "string"
                },
                "pay_price": {
                    "description": "实际支付单价（美团）",
                    "type": "string"
                },
                "price": {
                    "description": "商品单价",
                    "type": "string"
                },
                "product_id": {
                    "type": "integer"
                },
                "product_name": {
                    "type": "string"
                },
                "promotion_id": {
                    "description": "折扣活动id",
                    "type": "integer"
                },
                "refund_number": {
                    "description": "已退数量",
                    "type": "integer"
                },
                "sku_id": {
                    "type": "integer"
                },
                "specs": {
                    "type": "string"
                },
                "surplus_number": {
                    "description": "可退数量",
                    "type": "integer"
                },
                "surplus_refund_amount": {
                    "description": "可退金额",
                    "type": "integer"
                },
                "used_number": {
                    "description": "虚拟商品已核销数量",
                    "type": "integer"
                }
            }
        },
        "models.PushOmsRequest": {
            "type": "object",
            "properties": {
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                },
                "pay_sn": {
                    "description": "交易号",
                    "type": "string"
                }
            }
        },
        "models.RefundAmount": {
            "type": "object",
            "properties": {
                "costAmount": {
                    "description": "总金额",
                    "type": "number"
                },
                "refundAmount": {
                    "description": "应退金额",
                    "type": "number"
                },
                "usedOriginalAmount": {
                    "description": "已使用项目原价总和",
                    "type": "number"
                }
            }
        },
        "models.RefundAmountReq": {
            "type": "object",
            "properties": {
                "batchCode": {
                    "description": "批次号",
                    "type": "string"
                },
                "ensureCode": {
                    "description": "卡号",
                    "type": "string"
                },
                "petId": {
                    "description": "宠物编码",
                    "type": "string"
                },
                "userId": {
                    "description": "用户id",
                    "type": "string"
                }
            }
        },
        "models.RefundDetail": {
            "type": "object",
            "properties": {
                "can_revoke": {
                    "description": "能否撤销，0否1是",
                    "type": "integer"
                },
                "express_name": {
                    "description": "退款物流名称",
                    "type": "string"
                },
                "express_no": {
                    "description": "退款物流号",
                    "type": "string"
                },
                "need_express": {
                    "description": "是否需要快递，0否1是",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "订单编号",
                    "type": "string"
                },
                "refund_account": {
                    "description": "退款账户",
                    "type": "string"
                },
                "refund_amount": {
                    "description": "退款金额",
                    "type": "string"
                },
                "refund_logs": {
                    "description": "退款流程",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.RefundLog"
                    }
                },
                "refund_product": {
                    "description": "退款信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Product"
                    }
                },
                "refund_reason": {
                    "description": "退款原因",
                    "type": "string"
                },
                "refundtype": {
                    "description": "售后类型: 1为退款,2为退货，默认为1",
                    "type": "integer"
                },
                "remaining_time": {
                    "description": "剩余秒数",
                    "type": "integer"
                },
                "return_address": {
                    "description": "退货地址",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ReturnAddress"
                        }
                    ]
                },
                "status": {
                    "description": "售后状态",
                    "type": "integer"
                },
                "status_text": {
                    "description": "售后状态描述",
                    "type": "string"
                }
            }
        },
        "models.RefundDetailResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/models.RefundDetail"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.RefundHealthAmountResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/models.RefundAmount"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.RefundHealthPlanCardReq": {
            "type": "object",
            "properties": {
                "batchCode": {
                    "description": "批次号",
                    "type": "string"
                },
                "ensureCode": {
                    "description": "卡号",
                    "type": "string"
                },
                "petId": {
                    "description": "宠物编码",
                    "type": "string"
                },
                "refundAmount": {
                    "description": "退款金额",
                    "type": "integer"
                }
            }
        },
        "models.RefundList": {
            "type": "object",
            "properties": {
                "can_revoke": {
                    "description": "能否撤销，0否1是",
                    "type": "integer"
                },
                "need_express": {
                    "description": "是否需要快递，0否1是",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "订单编号",
                    "type": "string"
                },
                "product": {
                    "description": "订单商品信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Product"
                    }
                },
                "refund_sn": {
                    "description": "退款编号",
                    "type": "string"
                },
                "refundtype": {
                    "description": "申请类型: 1为退款,2为退货，默认为1",
                    "type": "integer"
                },
                "status": {
                    "description": "售后状态:1等待商家审核 2已撤销 3退货退款成功 4等待买家退货 5买家已退货",
                    "type": "integer"
                },
                "status_text": {
                    "description": "售后状态描述",
                    "type": "string"
                }
            }
        },
        "models.RefundListResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.RefundList"
                    }
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.RefundLog": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "time": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "models.ReturnAddress": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                }
            }
        },
        "models.SetMealData": {
            "type": "object",
            "properties": {
                "categoryCode": {
                    "description": "套餐编码",
                    "type": "string"
                },
                "categoryDetailCode": {
                    "description": "套餐明细编码",
                    "type": "string"
                },
                "corpusTimes": {
                    "description": "数量",
                    "type": "integer"
                },
                "productName": {
                    "description": "项目名",
                    "type": "string"
                }
            }
        },
        "models.SetMealReq": {
            "type": "object",
            "properties": {
                "applyAge": {
                    "description": "适用年龄(1-幼年;2-成年;3-老年)",
                    "type": "integer"
                },
                "kindOf": {
                    "description": "宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)",
                    "type": "integer"
                }
            }
        },
        "models.StandardPayRequest": {
            "type": "object",
            "properties": {
                "app_id": {
                    "type": "integer"
                },
                "bar_code": {
                    "type": "string"
                },
                "client_ip": {
                    "type": "string"
                },
                "discount": {
                    "type": "integer"
                },
                "extend_info": {
                    "type": "string"
                },
                "merchant_id": {
                    "type": "string"
                },
                "notify_url": {
                    "type": "string"
                },
                "open_id": {
                    "type": "string"
                },
                "order_name": {
                    "type": "string"
                },
                "order_no": {
                    "type": "string"
                },
                "order_pay_type": {
                    "type": "string"
                },
                "pay_amount": {
                    "type": "integer"
                },
                "pay_total": {
                    "type": "integer"
                },
                "product_desc": {
                    "type": "string"
                },
                "product_id": {
                    "type": "string"
                },
                "sign": {
                    "type": "string"
                },
                "sub_app_id": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "integer"
                },
                "trans_type": {
                    "type": "integer"
                },
                "trm_id": {
                    "type": "string"
                },
                "trm_sn": {
                    "type": "string"
                },
                "valid_time": {
                    "type": "integer"
                }
            }
        },
        "models.StandardPayResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/models.StandardPayRequest"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.VerifyCodes": {
            "type": "object",
            "properties": {
                "createTime": {
                    "description": "订单下单时间",
                    "type": "string"
                },
                "image": {
                    "description": "商品图片",
                    "type": "string"
                },
                "markingPrice": {
                    "description": "市场价（单价 单位：分）",
                    "type": "integer"
                },
                "memberId": {
                    "description": "订单所属会员id（该订单是哪个会员买的）",
                    "type": "string"
                },
                "number": {
                    "description": "商品购买数量",
                    "type": "integer"
                },
                "orderStatus": {
                    "description": "订单状态 已取消,10未付款,20已付款,30已完成",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "所属阿闻订单号(子订单)",
                    "type": "string"
                },
                "payMode": {
                    "description": "订单支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支",
                    "type": "integer"
                },
                "payPrice": {
                    "description": "商品均摊后实际支付单价",
                    "type": "integer"
                },
                "payTime": {
                    "description": "订单支付时间",
                    "type": "string"
                },
                "paymentTotal": {
                    "description": "购买金额（单位分）",
                    "type": "integer"
                },
                "privilege": {
                    "description": "优惠金额（暂无数值）",
                    "type": "integer"
                },
                "productName": {
                    "description": "商品名称",
                    "type": "string"
                },
                "skuId": {
                    "description": "商品sku id",
                    "type": "string"
                },
                "thirdSkuId": {
                    "description": "第三方的SkuId(货号)",
                    "type": "string"
                },
                "total": {
                    "description": "订单实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额)单位分",
                    "type": "integer"
                },
                "verifyCode": {
                    "description": "核销码",
                    "type": "string"
                },
                "verifyCodeExpiryDate": {
                    "description": "核销码有效期",
                    "type": "string"
                },
                "verifyMemberId": {
                    "description": "核销人的用户id（该订单是哪个会员核销的）",
                    "type": "string"
                },
                "verifyShop": {
                    "description": "核销门店的财务编码",
                    "type": "string"
                },
                "verifyStatus": {
                    "description": "核销状态 0未核销, 1已核销, 2已退款 不传默认为未核销",
                    "type": "integer"
                },
                "verifyTime": {
                    "description": "核销时间",
                    "type": "string"
                }
            }
        },
        "models.WriteOffReq": {
            "type": "object",
            "properties": {
                "member_id": {
                    "type": "string"
                },
                "source": {
                    "type": "integer"
                },
                "store_id": {
                    "type": "string"
                },
                "user_agent": {
                    "type": "integer"
                },
                "verify_code": {
                    "type": "string"
                }
            }
        },
        "oc.AwenOrderCancleRequest": {
            "type": "object",
            "properties": {
                "cancel_reason": {
                    "description": "取消原因",
                    "type": "string"
                },
                "order_id": {
                    "description": "订单ID",
                    "type": "string"
                }
            }
        },
        "oc.AwenOrderPayInfoData": {
            "type": "object",
            "properties": {
                "add_time": {
                    "description": "交易时间",
                    "type": "string"
                },
                "order_id": {
                    "description": "交易订单id",
                    "type": "string"
                },
                "pay_price": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "pay_time": {
                    "description": "支付时间",
                    "type": "string"
                },
                "status": {
                    "description": "订单状态   0-交易中，1-交易完成，2-交易失败",
                    "type": "integer"
                },
                "trade_no": {
                    "description": "交易流水号",
                    "type": "string"
                }
            }
        },
        "oc.AwenOrderPayQueryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "description": "支付信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.AwenOrderPayInfoData"
                        }
                    ]
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.BadRequestResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "代码 非 200 取 message 错误信息",
                    "type": "integer"
                },
                "message": {
                    "description": "错误消息",
                    "type": "string"
                }
            }
        },
        "oc.BaseResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.CardBaseResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.CardEquityReceiveReq": {
            "type": "object",
            "properties": {
                "create_time": {
                    "description": "创建时间",
                    "type": "string"
                },
                "equity_id": {
                    "description": "权益id，type、equity_id二选一",
                    "type": "integer"
                },
                "id": {
                    "description": "权益值 优惠券id",
                    "type": "string"
                },
                "is_month": {
                    "description": "0开卡发券， 1月度领券",
                    "type": "integer"
                },
                "order_sn": {
                    "description": "会员卡权益领取 卡订单号",
                    "type": "string"
                },
                "scrm_id": {
                    "description": "前端不用传，用户id",
                    "type": "string"
                },
                "sign_id": {
                    "description": "家庭医生服务包 签约id",
                    "type": "integer"
                },
                "type": {
                    "description": "权益类型：1商城优惠券，2子龙门店券，8打折卡，与type、equity_id二选一",
                    "type": "integer"
                }
            }
        },
        "oc.CardNewByCodeReq": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "兑换码",
                    "type": "string"
                },
                "scrm_id": {
                    "description": "前端不用传，用户id",
                    "type": "string"
                },
                "user_agent": {
                    "description": "前端不用传，从请求头解析",
                    "type": "integer"
                },
                "user_name": {
                    "description": "前端不用传，用户名",
                    "type": "string"
                }
            }
        },
        "oc.CardNewReq": {
            "type": "object",
            "properties": {
                "card_id": {
                    "description": "卡id",
                    "type": "integer"
                },
                "dis_id": {
                    "description": "分销id",
                    "type": "integer"
                },
                "dis_type": {
                    "description": "分销类型 1-链接 2-扫码",
                    "type": "integer"
                },
                "scrm_id": {
                    "description": "前端不用传，用户id",
                    "type": "string"
                },
                "source": {
                    "description": "来源，0默认，1兑换码激活，2门店开卡",
                    "type": "integer"
                },
                "user_agent": {
                    "description": "前端不用传，从请求头解析",
                    "type": "integer"
                },
                "user_name": {
                    "description": "前端不用传，用户名",
                    "type": "string"
                }
            }
        },
        "oc.CardNewRes": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "deadline": {
                    "description": "支付截止时间戳",
                    "type": "integer"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                },
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                }
            }
        },
        "oc.CardServicePackActivityReq": {
            "type": "object",
            "properties": {
                "member_id": {
                    "description": "前端不用传，用户id",
                    "type": "string"
                },
                "pet_age": {
                    "description": "宠物年龄，记录宠物当时的年龄",
                    "type": "string"
                },
                "pet_avatar": {
                    "description": "宠物头像",
                    "type": "string"
                },
                "pet_birthday": {
                    "description": "生日例如：2021-10-01 00:00:00",
                    "type": "string"
                },
                "pet_id": {
                    "description": "宠物ID",
                    "type": "string"
                },
                "pet_kind_of": {
                    "description": "宠物种类大分类",
                    "type": "string"
                },
                "pet_name": {
                    "description": "宠物名字",
                    "type": "string"
                },
                "pet_sex": {
                    "description": "性别：1GG,2MM",
                    "type": "integer"
                },
                "pet_variety": {
                    "description": "种类",
                    "type": "string"
                },
                "sign_id": {
                    "description": "签约id",
                    "type": "integer"
                }
            }
        },
        "oc.Company": {
            "type": "object",
            "properties": {
                "corpAccount": {
                    "description": "银行卡号",
                    "type": "string"
                },
                "corpAddress": {
                    "description": "企业地址",
                    "type": "string"
                },
                "corpBank": {
                    "description": "开户银行",
                    "type": "string"
                },
                "corpName": {
                    "description": "公司名称",
                    "type": "string"
                },
                "corpTaxNo": {
                    "description": "税号",
                    "type": "string"
                },
                "corpTelephone": {
                    "description": "企业电话",
                    "type": "string"
                },
                "speedCode": {
                    "description": "公司编码",
                    "type": "string"
                }
            }
        },
        "oc.CompanyList": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "公司编码",
                    "type": "string"
                },
                "name": {
                    "description": "公司名称",
                    "type": "string"
                }
            }
        },
        "oc.ConsultMemberPetInfo": {
            "type": "object",
            "properties": {
                "member_avatar": {
                    "description": "用户头像",
                    "type": "string"
                },
                "member_id": {
                    "description": "用户id",
                    "type": "string"
                },
                "member_mobile": {
                    "description": "用户手机号",
                    "type": "string"
                },
                "member_name": {
                    "description": "用户名称",
                    "type": "string"
                },
                "pet_age": {
                    "description": "宠物年龄",
                    "type": "string"
                },
                "pet_avatar": {
                    "description": "宠物头像",
                    "type": "string"
                },
                "pet_birthday": {
                    "description": "宠物生日",
                    "type": "string"
                },
                "pet_id": {
                    "description": "宠物id",
                    "type": "string"
                },
                "pet_kindof": {
                    "description": "宠物分类",
                    "type": "string"
                },
                "pet_kindof_code": {
                    "description": "宠物分类类code",
                    "type": "string"
                },
                "pet_name": {
                    "description": "宠物名称",
                    "type": "string"
                },
                "pet_neutering": {
                    "description": "是否绝育",
                    "type": "integer"
                },
                "pet_sex": {
                    "description": "宠物性别",
                    "type": "integer"
                },
                "pet_variety": {
                    "description": "宠物种类",
                    "type": "string"
                },
                "pet_variety_code": {
                    "description": "宠物种类code",
                    "type": "string"
                }
            }
        },
        "oc.CreateInvoiceRequest": {
            "type": "object",
            "properties": {
                "bank_name": {
                    "description": "开户银行",
                    "type": "string"
                },
                "bank_number": {
                    "description": "银行卡号",
                    "type": "string"
                },
                "channel": {
                    "description": "前端不需要传",
                    "type": "integer"
                },
                "company_address": {
                    "description": "企业地址",
                    "type": "string"
                },
                "company_code": {
                    "description": "前端不需要传",
                    "type": "string"
                },
                "company_name": {
                    "description": "企业名称",
                    "type": "string"
                },
                "email": {
                    "description": "电子邮箱",
                    "type": "string"
                },
                "identify_number": {
                    "description": "纳税人识别号",
                    "type": "string"
                },
                "invoice_tt": {
                    "description": "发票类型 1个人、2企业",
                    "type": "integer"
                },
                "mobile": {
                    "description": "手机号码/企业电话",
                    "type": "string"
                },
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                },
                "pay_time": {
                    "description": "支付时间",
                    "type": "integer"
                },
                "scrm_id": {
                    "description": "前端不需要传，仅用于标记用户",
                    "type": "string"
                }
            }
        },
        "oc.ExpressCompany": {
            "type": "object",
            "properties": {
                "company": {
                    "description": "物流公司名称",
                    "type": "string"
                },
                "id": {
                    "description": "id",
                    "type": "string"
                }
            }
        },
        "oc.ExpressCompanyListResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "dataList": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.ExpressCompany"
                    }
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.ExpressInfo": {
            "type": "object",
            "properties": {
                "datetime": {
                    "description": "时间点",
                    "type": "string"
                },
                "info": {
                    "description": "物流信息",
                    "type": "string"
                },
                "status": {
                    "description": "状态",
                    "type": "string"
                }
            }
        },
        "oc.ExpressInfoResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "dataList": {
                    "description": "物流信息列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.ExpressInfo"
                    }
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "express_company": {
                    "description": "物流公司",
                    "type": "string"
                },
                "express_no": {
                    "description": "物流号",
                    "type": "string"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                },
                "order_no": {
                    "description": "订单id",
                    "type": "string"
                }
            }
        },
        "oc.ExpressInfoUpdateRequest": {
            "type": "object",
            "properties": {
                "express_code": {
                    "description": "物流公司代码，正向订单必填",
                    "type": "string"
                },
                "express_company_id": {
                    "description": "物流公司id 售后单必填",
                    "type": "integer"
                },
                "express_info": {
                    "description": "物流信息",
                    "type": "string"
                },
                "express_no": {
                    "description": "物流号(或快递单号)",
                    "type": "string"
                },
                "order_express_id": {
                    "description": "快递单记录id 正向单必填",
                    "type": "integer"
                },
                "order_id": {
                    "description": "订单号 正向订单必填",
                    "type": "string"
                },
                "refund_sn": {
                    "description": "服务单号  售后单必填",
                    "type": "string"
                },
                "update_order_type": {
                    "description": "更新订单类型 0售后单 1正向单",
                    "type": "integer"
                }
            }
        },
        "oc.ExternalResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "description": "json 格式数据",
                    "type": "string"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "external_code": {
                    "description": "外部接口返回错误码（例如美配，美团）",
                    "type": "string"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.HealthSubOrderRequest": {
            "type": "object",
            "properties": {
                "category_code": {
                    "description": "套餐编码",
                    "type": "string"
                },
                "category_name": {
                    "description": "套餐名称",
                    "type": "string"
                },
                "ensure_code": {
                    "description": "卡号",
                    "type": "string"
                },
                "member_name": {
                    "description": "会员名称",
                    "type": "string"
                },
                "member_tel": {
                    "description": "member_tel",
                    "type": "string"
                },
                "pay_money": {
                    "description": "支付金额",
                    "type": "integer"
                },
                "scrm_pet_id": {
                    "description": "scrm 宠物Id",
                    "type": "string"
                },
                "scrm_user_id": {
                    "description": "scrm用户Id",
                    "type": "string"
                }
            }
        },
        "oc.HealthSubOrderResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                },
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceCompanyInfoResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/oc.Company"
                },
                "message": {
                    "description": "错误信息",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceDetailData": {
            "type": "object",
            "properties": {
                "apply": {
                    "description": "申请发票信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.CreateInvoiceRequest"
                        }
                    ]
                },
                "created_at": {
                    "description": "申请时间",
                    "type": "string"
                },
                "fail_reason": {
                    "description": "失败原因，当开票失败（status = 3）时返回",
                    "type": "string"
                },
                "invoices": {
                    "description": "发票链接，多张用逗号分割",
                    "type": "string"
                },
                "status": {
                    "description": "开票状态，0未开票、1开票成功、2开票中、3开票失败",
                    "type": "integer"
                }
            }
        },
        "oc.InvoiceDetailResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/oc.InvoiceDetailData"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceSendEmailRequest": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "order_sn": {
                    "description": "订单号",
                    "type": "string"
                },
                "scrm_id": {
                    "description": "前端不需要传，仅用于rpc标记用户",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceStatusResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "invoice_status": {
                    "description": "0未开票、1开票成功、2开票中、3开票失败、8开票关闭、9开票过期",
                    "type": "integer"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceTitleAddRequest": {
            "type": "object",
            "properties": {
                "buyer_name": {
                    "description": "企业名称/个人抬头",
                    "type": "string"
                },
                "buyer_tax_num": {
                    "description": "企业识别号",
                    "type": "string"
                },
                "customer_phone": {
                    "description": "手机号",
                    "type": "string"
                },
                "notify_email": {
                    "description": "接收邮件地址",
                    "type": "string"
                },
                "scrm_id": {
                    "type": "string"
                }
            }
        },
        "oc.InvoiceTitleAddResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceTitleEditRequest": {
            "type": "object",
            "properties": {
                "buyer_name": {
                    "description": "企业名称/个人抬头",
                    "type": "string"
                },
                "buyer_tax_num": {
                    "description": "企业识别号",
                    "type": "string"
                },
                "customer_id": {
                    "description": "客户id",
                    "type": "integer"
                },
                "customer_phone": {
                    "description": "手机号",
                    "type": "string"
                },
                "notify_email": {
                    "description": "接收邮件地址",
                    "type": "string"
                },
                "scrm_id": {
                    "type": "string"
                }
            }
        },
        "oc.InvoiceTitleEditResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceTitleListRequest": {
            "type": "object",
            "properties": {
                "scrm_id": {
                    "type": "string"
                }
            }
        },
        "oc.InvoiceTitleListResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.InvoiceTitleListResponse_InvoiceTitleData"
                    }
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                }
            }
        },
        "oc.InvoiceTitleListResponse_InvoiceTitleData": {
            "type": "object",
            "properties": {
                "buyer_name": {
                    "type": "string"
                },
                "buyer_tax_num": {
                    "type": "string"
                },
                "customer_id": {
                    "type": "integer"
                },
                "customer_phone": {
                    "type": "string"
                },
                "notify_email": {
                    "type": "string"
                },
                "update_time": {
                    "type": "string"
                }
            }
        },
        "oc.OrderPrescribeCheckReq": {
            "type": "object",
            "properties": {
                "finance_code": {
                    "description": "门店财务编码",
                    "type": "string"
                },
                "scrm_id": {
                    "description": "前端不需要传",
                    "type": "string"
                },
                "skus": {
                    "description": "药品信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.OrderPrescribeSkuNum"
                    }
                }
            }
        },
        "oc.OrderPrescribeCheckRes": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码 200成功 非200失败",
                    "type": "integer"
                },
                "data": {
                    "$ref": "#/definitions/oc.OrderPrescribeCheckRes_Data"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string"
                }
            }
        },
        "oc.OrderPrescribeCheckRes_Data": {
            "type": "object",
            "properties": {
                "consult_order_sn": {
                    "description": "处方单号",
                    "type": "string"
                },
                "skus": {
                    "description": "药品信息，如果存在药品且没有处方单号，则表示可以开处方",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.OrderPrescribeSkuNum"
                    }
                }
            }
        },
        "oc.OrderPrescribeRes": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码 200成功 非200失败",
                    "type": "integer"
                },
                "consult_order_sn": {
                    "description": "处方单号",
                    "type": "string"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string"
                }
            }
        },
        "oc.OrderPrescribeSkuNum": {
            "type": "object",
            "properties": {
                "num": {
                    "description": "数量",
                    "type": "integer"
                },
                "sku_id": {
                    "description": "sku_id",
                    "type": "integer"
                }
            }
        },
        "oc.PreSalePay": {
            "type": "object",
            "properties": {
                "endTime": {
                    "description": "尾款支付结束时间",
                    "type": "string"
                },
                "isVirtual": {
                    "description": "是否虚拟订单 0 否 1是",
                    "type": "integer"
                },
                "remarks": {
                    "description": "温馨提醒",
                    "type": "string"
                },
                "startTime": {
                    "description": "尾款支付开始时间",
                    "type": "string"
                }
            }
        },
        "oc.PrescriptionDiagnose": {
            "type": "object",
            "properties": {
                "diagnose_content": {
                    "description": "诊断内容",
                    "type": "string"
                },
                "disease_code": {
                    "description": "病种编号",
                    "type": "string"
                },
                "is_sure": {
                    "description": "是否拟：1确定 2不确定，写死1",
                    "type": "integer"
                },
                "position": {
                    "description": "位置：0-无 1-左、2-右、3-上、4-下、5-单侧、6-双侧，写死0",
                    "type": "integer"
                }
            }
        },
        "oc.PushTemplateRequest": {
            "type": "object",
            "properties": {
                "openId": {
                    "description": "用户ID",
                    "type": "string"
                },
                "orderSn": {
                    "description": "订单编号",
                    "type": "string"
                },
                "org_id": {
                    "description": "主体:1-阿闻，2-极宠家，3-福码购",
                    "type": "integer"
                },
                "preSalePay": {
                    "description": "预售尾款支付通知",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.PreSalePay"
                        }
                    ]
                },
                "pushType": {
                    "description": "类型(1=\u003e发送退款成功通知, 2=\u003e发送退款失败通知, 3=\u003e发送退款状态通知, 4=\u003e推送尾款支付提醒通知)",
                    "type": "integer"
                },
                "refundFail": {
                    "description": "发送退款失败通知",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.RefundFail"
                        }
                    ]
                },
                "refundStatus": {
                    "description": "发送退款状态通知",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.RefundStatus"
                        }
                    ]
                },
                "refundSuccess": {
                    "description": "发送退款成功通知",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.RefundSuccess"
                        }
                    ]
                },
                "remarks": {
                    "description": "备注",
                    "type": "string"
                },
                "templateId": {
                    "description": "模板ID",
                    "type": "string"
                }
            }
        },
        "oc.QueryInvoiceCompanyResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.CompanyList"
                    }
                },
                "message": {
                    "description": "错误信息",
                    "type": "string"
                }
            }
        },
        "oc.QueryMallVirtualOrderWriteOffCodesResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码 200成功 非200失败",
                    "type": "integer"
                },
                "data": {
                    "description": "列表数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.QueryMallVirtualOrderWriteOffCodesResponse_Data"
                    }
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "message": {
                    "description": "错误描述",
                    "type": "string"
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer"
                }
            }
        },
        "oc.QueryMallVirtualOrderWriteOffCodesResponse_Data": {
            "type": "object",
            "properties": {
                "goods_id": {
                    "description": "sku_id",
                    "type": "integer"
                },
                "goods_name": {
                    "description": "商品名称",
                    "type": "string"
                },
                "goods_num": {
                    "description": "商品数量",
                    "type": "integer"
                },
                "goods_price": {
                    "description": "商品价格",
                    "type": "number"
                },
                "order_sn": {
                    "description": "订单编号",
                    "type": "string"
                },
                "pay_price": {
                    "description": "实付金额",
                    "type": "number"
                },
                "payment_time": {
                    "description": "支付时间(秒)",
                    "type": "integer"
                },
                "vr_code": {
                    "description": "兑换码",
                    "type": "string"
                },
                "vr_indate": {
                    "description": "过期时间戳(秒)",
                    "type": "integer"
                }
            }
        },
        "oc.QuerySignIdRes": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "状态码",
                    "type": "integer"
                },
                "message": {
                    "description": "消息",
                    "type": "string"
                },
                "sign_id": {
                    "description": "签约id",
                    "type": "integer"
                }
            }
        },
        "oc.RefundFail": {
            "type": "object",
            "properties": {
                "refundId": {
                    "description": "退款ID",
                    "type": "string"
                },
                "refundSn": {
                    "description": "退款订单",
                    "type": "string"
                },
                "refundType": {
                    "description": "退款类型",
                    "type": "string"
                },
                "status": {
                    "description": "状态",
                    "type": "string"
                }
            }
        },
        "oc.RefundInvoiceRequest": {
            "type": "object",
            "properties": {
                "channel": {
                    "type": "integer"
                },
                "refund_sn": {
                    "type": "string"
                }
            }
        },
        "oc.RefundOrderApplyRequest": {
            "type": "object",
            "properties": {
                "activity_pt_amount": {
                    "description": "平台承担的活动优惠金额",
                    "type": "number"
                },
                "applyOpUserType": {
                    "description": "推送当前仅退款或退货退款流程的发起方，是用户还是商家；仅适用于支持退货退款的商家。\n1-用户\n2-商家\n3-客服\n4-BD\n5-系统\n6-开放平台",
                    "type": "string"
                },
                "channel_id": {
                    "description": "来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商 6门店",
                    "type": "integer"
                },
                "delivery_price": {
                    "description": "本次退款的配送费",
                    "type": "number"
                },
                "external_order_id": {
                    "description": "外部订单号 Id",
                    "type": "string"
                },
                "full_refund": {
                    "description": "1整单退款 2部分退款",
                    "type": "integer"
                },
                "is_cancal_order": {
                    "description": "京东到家 专属 1：取消订单 2正常售后订单",
                    "type": "integer"
                },
                "old_refund_sn": {
                    "description": "渠道退款单号",
                    "type": "string"
                },
                "operation_type": {
                    "description": "操作描述",
                    "type": "string"
                },
                "operation_user": {
                    "description": "操作用户",
                    "type": "string"
                },
                "order_from": {
                    "description": "渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家 (废弃))",
                    "type": "integer"
                },
                "order_id": {
                    "description": "订单号    必镇",
                    "type": "string"
                },
                "pictures": {
                    "description": "用户申请退款时上传的退款图片，多个图片url以英文逗号隔开",
                    "type": "string"
                },
                "reason": {
                    "description": "因***原因部分退款 必镇",
                    "type": "string"
                },
                "refund_amount": {
                    "description": "退款总金额",
                    "type": "number"
                },
                "refund_code": {
                    "description": "京东到家 专属 售后原因id（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，502:未在时效内送达）",
                    "type": "string"
                },
                "refund_order_goods_data": {
                    "description": "部分退款商品sku数据集合的json格式数组",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.RefundOrderGoodsData"
                    }
                },
                "refund_order_sn": {
                    "description": "售后单号",
                    "type": "string"
                },
                "refund_remark": {
                    "description": "售后单备注",
                    "type": "string"
                },
                "refund_type": {
                    "description": "退款类型1为退款,2为退货  目前不支持退货",
                    "type": "integer"
                },
                "res_type": {
                    "description": "申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。\n支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。",
                    "type": "string"
                },
                "service_type": {
                    "description": "区分是否已开通退货退款售后业务。 未开通的场景： 0-退款流程或申诉流程 已开通场景： 1-仅退款流程 2-退款退货流程",
                    "type": "string"
                },
                "shop_id": {
                    "description": "门店id(财务编码)",
                    "type": "string"
                }
            }
        },
        "oc.RefundOrderApplyResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "code",
                    "type": "integer"
                },
                "create_time": {
                    "description": "下单时间(13位，毫秒)",
                    "type": "integer"
                },
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "message": {
                    "description": "消息提示",
                    "type": "string"
                },
                "refund_order_sn": {
                    "description": "退款退货单号",
                    "type": "string"
                }
            }
        },
        "oc.RefundOrderCancelRequest": {
            "type": "object",
            "properties": {
                "operation_type": {
                    "description": "操作类型",
                    "type": "string"
                },
                "operation_user": {
                    "description": "操作用户",
                    "type": "string"
                },
                "reason": {
                    "description": "原因",
                    "type": "string"
                },
                "refund_order_sn": {
                    "type": "string"
                },
                "res_type": {
                    "description": "申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。\n支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。",
                    "type": "string"
                }
            }
        },
        "oc.RefundOrderGoodsData": {
            "type": "object",
            "properties": {
                "app_food_code": {
                    "description": "APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。",
                    "type": "string"
                },
                "goods_name": {
                    "description": "退款商品名称",
                    "type": "string"
                },
                "order_product_id": {
                    "description": "订单商品表主键id",
                    "type": "integer"
                },
                "parent_sku_id": {
                    "description": "订单商品的父sku 组合商品的子商品该字段有值",
                    "type": "string"
                },
                "platform_sku_id": {
                    "description": "订单商品的父sku 组合商品的子商品该字段有值",
                    "type": "integer"
                },
                "promotion_type": {
                    "description": "京东到家标识 商品促销类型（1203满赠，6买赠，1正品）",
                    "type": "integer"
                },
                "quantity": {
                    "description": "数量",
                    "type": "integer"
                },
                "refund_amount": {
                    "description": "总金额",
                    "type": "string"
                },
                "refund_price": {
                    "description": "商品单价  如本次部分退款是按件部分退，则此金额为单件商品sku的退款金额",
                    "type": "number"
                },
                "refund_reality_price": {
                    "description": "实际支付单价 当前商品sku参加商品类活动优惠后的金额（单价），单位是元。",
                    "type": "number"
                },
                "sku_id": {
                    "description": "商品唯一标识",
                    "type": "string"
                },
                "spec": {
                    "description": "商品sku的规格名称",
                    "type": "string"
                },
                "sub_biz_order_id": {
                    "description": "饿了么专属字段，子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段",
                    "type": "string"
                }
            }
        },
        "oc.RefundStatus": {
            "type": "object",
            "properties": {
                "refundId": {
                    "description": "退款ID",
                    "type": "string"
                },
                "refundType": {
                    "description": "退款类型",
                    "type": "string"
                },
                "status": {
                    "description": "状态",
                    "type": "string"
                }
            }
        },
        "oc.RefundSuccess": {
            "type": "object",
            "properties": {
                "refundAmount": {
                    "description": "退款金额",
                    "type": "string"
                },
                "refundId": {
                    "description": "退款ID",
                    "type": "string"
                },
                "refundTime": {
                    "description": "退款时间",
                    "type": "string"
                },
                "refundType": {
                    "description": "退款类型",
                    "type": "string"
                }
            }
        },
        "oc.RiderLocationRequest": {
            "type": "object",
            "properties": {
                "delivery_id": {
                    "description": "配送活动标识",
                    "type": "integer"
                },
                "delivery_service_code": {
                    "description": "配送服务代码，详情见合同\n飞速达: 4002\n快速达: 4011\n及时达: 4012\n集中送: 4013\n自由达: 4014",
                    "type": "integer"
                },
                "mt_peisong_id": {
                    "description": "美团配送内部订单id，最长不超过32个字符",
                    "type": "string"
                }
            }
        },
        "oc.SectionDto": {
            "type": "object",
            "properties": {
                "childSectionDto": {
                    "description": "子区域",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.SectionDto"
                    }
                },
                "id": {
                    "description": "区域Id",
                    "type": "integer"
                },
                "parentId": {
                    "description": "上级部门Id",
                    "type": "integer"
                },
                "sectionName": {
                    "description": "名称",
                    "type": "string"
                }
            }
        },
        "oc.SectionQueryRequest": {
            "type": "object",
            "properties": {
                "isChild": {
                    "description": "是否加载子区域",
                    "type": "boolean"
                },
                "parentId": {
                    "description": "上级ID",
                    "type": "integer"
                }
            }
        },
        "oc.SectionQueryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "代码 非 200 取 message 错误信息",
                    "type": "integer"
                },
                "data": {
                    "description": "省市列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.SectionDto"
                    }
                }
            }
        },
        "oc.UpetDjConfirmRequest": {
            "type": "object",
            "properties": {
                "orderId": {
                    "description": "订单Id",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjConfirmResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "代码 非 200 取 message 错误信息",
                    "type": "integer"
                },
                "message": {
                    "description": "错误信息",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjDeliverNodesDto": {
            "type": "object",
            "properties": {
                "createTime": {
                    "description": "节点日期",
                    "type": "string"
                },
                "message": {
                    "description": "节点说明",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjDeliveryDto": {
            "type": "object",
            "properties": {
                "deliveryId": {
                    "description": "配送标识",
                    "type": "integer"
                },
                "deliveryIdStr": {
                    "description": "配送标识字符串类型",
                    "type": "string"
                },
                "deliveryOrderId": {
                    "description": "配送单号",
                    "type": "string"
                },
                "deliveryServiceCode": {
                    "description": "配送服务代码",
                    "type": "string"
                },
                "deliveryTime": {
                    "description": "送达时间",
                    "type": "string"
                },
                "deliveryType": {
                    "description": "配送方式 1 快递 2 外卖 3 自提 4 同城",
                    "type": "integer"
                },
                "deliveryTypeName": {
                    "description": "配送方式名称",
                    "type": "string"
                },
                "express": {
                    "description": "订单发货记录",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjDeliveryDto_OrderExpress"
                    }
                },
                "freight": {
                    "description": "配送费",
                    "type": "number"
                },
                "latitude": {
                    "description": "收货地址维度",
                    "type": "number"
                },
                "longitude": {
                    "description": "收货地址经度",
                    "type": "number"
                },
                "nodes": {
                    "description": "配送节点信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjDeliverNodesDto"
                    }
                },
                "pickupStationAddress": {
                    "description": "提货点地址",
                    "type": "string"
                },
                "pickupStationName": {
                    "description": "提货点名称",
                    "type": "string"
                },
                "receiveraddress": {
                    "description": "收货地址",
                    "type": "string"
                },
                "receivercity": {
                    "description": "收货市",
                    "type": "string"
                },
                "receiverdistrict": {
                    "description": "收货区",
                    "type": "string"
                },
                "receivermobile": {
                    "description": "收货人手机号",
                    "type": "string"
                },
                "receivername": {
                    "description": "收货人名称",
                    "type": "string"
                },
                "receiverphone": {
                    "description": "收货电话",
                    "type": "string"
                },
                "receiverstate": {
                    "description": "收货省",
                    "type": "string"
                },
                "shopLatitude": {
                    "description": "店铺维度",
                    "type": "number"
                },
                "shopLongitude": {
                    "description": "店铺经度",
                    "type": "number"
                }
            }
        },
        "oc.UpetDjDeliveryDto_OrderExpress": {
            "type": "object",
            "properties": {
                "express_name": {
                    "description": "快递名称",
                    "type": "string"
                },
                "express_no": {
                    "description": "快递单号",
                    "type": "string"
                },
                "num": {
                    "description": "商品数量",
                    "type": "integer"
                }
            }
        },
        "oc.UpetDjOrderDetailRequest": {
            "type": "object",
            "properties": {
                "member_id": {
                    "type": "string"
                },
                "orderId": {
                    "description": "订单Id",
                    "type": "string"
                },
                "orderSn": {
                    "description": "订单号码 orderId 与 orderSn任选其一",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjOrderDetailResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "代码 非 200 取 message 错误信息",
                    "type": "integer"
                },
                "deliveryInfo": {
                    "description": "配送信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.UpetDjDeliveryDto"
                        }
                    ]
                },
                "message": {
                    "description": "错误信息",
                    "type": "string"
                },
                "orderListInfo": {
                    "description": "订单信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.UpetDjOrderDto"
                        }
                    ]
                },
                "refundInfo": {
                    "description": "退款信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.UpetDjRefundDto"
                        }
                    ]
                },
                "refundProductList": {
                    "description": "退款商品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjRefundProductDto"
                    }
                },
                "remainSeconds": {
                    "description": "剩余秒数",
                    "type": "integer"
                }
            }
        },
        "oc.UpetDjOrderDto": {
            "type": "object",
            "properties": {
                "business_times": {
                    "description": "店铺营业时间段",
                    "type": "string"
                },
                "channel_id": {
                    "description": "渠道id（datacenter.platform_channel表）,订单来源 1-阿闻到家 2-美团 3-饿了么 4-京东到家 5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗",
                    "type": "integer"
                },
                "childOrderList": {
                    "description": "阿闻订单子订单列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjOrderDto"
                    }
                },
                "consult_order_sn": {
                    "description": "处方单号",
                    "type": "string"
                },
                "createDateTime": {
                    "description": "下单日期",
                    "type": "string"
                },
                "enable_cancel": {
                    "description": "是否可以取消订单",
                    "type": "boolean"
                },
                "group_info": {
                    "$ref": "#/definitions/oc.UpetDjOrderDto_GroupInfo"
                },
                "is_apply_btn": {
                    "description": "是否显示退款按钮",
                    "type": "boolean"
                },
                "is_pay": {
                    "type": "integer"
                },
                "is_virtual": {
                    "description": "是否是虚拟订单，0否1是",
                    "type": "integer"
                },
                "no_display_after_payment": {
                    "description": "阿闻宠团团这个门店（财务编码：QZC00300）的所有订单，在订单“已支付”以后就不显示“申请退款”和“取消订单”的按钮，如果要退款就只能让客服在后台进行退款\n0默认显示 1不显示",
                    "type": "integer"
                },
                "orderId": {
                    "description": "订单Id",
                    "type": "string"
                },
                "orderNo": {
                    "description": "订单号码",
                    "type": "string"
                },
                "parent_order_sn": {
                    "description": "订单号码",
                    "type": "string"
                },
                "payMode": {
                    "description": "支付方式 0 待支付 1支付宝  2微信 3美团 4其他",
                    "type": "integer"
                },
                "pickup_code": {
                    "description": "取货码",
                    "type": "string"
                },
                "privilege": {
                    "description": "优惠金额",
                    "type": "number"
                },
                "productList": {
                    "description": "阿闻订单商品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjOrderProductDto"
                    }
                },
                "remarks": {
                    "description": "订单备注",
                    "type": "string"
                },
                "returnNo": {
                    "description": "最近一次售后记录",
                    "type": "string"
                },
                "returnState": {
                    "description": "订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败",
                    "type": "integer"
                },
                "returnWay": {
                    "description": "有售后记录 0 无 11 整单退款 12 整单退货 21 部分退款 22 部分退货",
                    "type": "integer"
                },
                "shopId": {
                    "description": "店铺Id",
                    "type": "string"
                },
                "shopMobile": {
                    "description": "店铺联系电话",
                    "type": "string"
                },
                "shopName": {
                    "description": "店铺名字",
                    "type": "string"
                },
                "shop_address": {
                    "description": "店铺地址",
                    "type": "string"
                },
                "state": {
                    "description": "订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单 24 骑手已接单 25 骑手配送中 26 商家配送中 27 商家手动已接单 28 备货完成, 待买家自提 29 商家已接单, 备货中 30 已完成, 31拆单中, 32拆单失败, 33未核销, 34待骑手接单，50已发货",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.UpetDjOrderState"
                        }
                    ]
                },
                "stock_up_time": {
                    "description": "店铺备货时长",
                    "type": "integer"
                },
                "totalMoney": {
                    "description": "订单总金额",
                    "type": "number"
                }
            }
        },
        "oc.UpetDjOrderDto_GroupInfo": {
            "type": "object",
            "properties": {
                "deliver_days": {
                    "description": "最晚N日内送达",
                    "type": "string"
                },
                "final_take_type": {
                    "description": "团长代收状态 0不代收 1代收",
                    "type": "integer"
                },
                "group_address": {
                    "description": "团员信息 地址",
                    "type": "string"
                },
                "group_id": {
                    "description": "团ID",
                    "type": "integer"
                },
                "group_leader": {
                    "description": "是否团长 1是 0否",
                    "type": "integer"
                },
                "group_mobile": {
                    "description": "团员信息 手机",
                    "type": "string"
                },
                "group_name": {
                    "description": "团员信息 名字",
                    "type": "string"
                },
                "group_status": {
                    "description": "社区团购活动状态 0开团中 1拼团成功 2拼团失败",
                    "type": "integer"
                },
                "receiver_address": {
                    "description": "收件地址",
                    "type": "string"
                },
                "receiver_city": {
                    "description": "收件市",
                    "type": "string"
                },
                "receiver_district": {
                    "description": "收件区",
                    "type": "string"
                },
                "receiver_mobile": {
                    "description": "收件手机",
                    "type": "string"
                },
                "receiver_name": {
                    "description": "收件人",
                    "type": "string"
                },
                "receiver_state": {
                    "description": "收件省",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjOrderIsAutoPrintQueryRequest": {
            "type": "object",
            "properties": {
                "orderSn": {
                    "description": "订单号码",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjOrderIsAutoPrintQueryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "代码 非 200 取 message 错误信息",
                    "type": "integer"
                },
                "isAutoPrint": {
                    "description": "是否自动打印",
                    "type": "boolean"
                },
                "message": {
                    "description": "错误信息",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjOrderProductDto": {
            "type": "object",
            "properties": {
                "childProductList": {
                    "description": "子商品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjOrderProductDto"
                    }
                },
                "is_prescribed_drug": {
                    "description": "是否处方药",
                    "type": "integer"
                },
                "parentProductId": {
                    "description": "上级productid",
                    "type": "string"
                },
                "parentSkuId": {
                    "description": "上级skuid",
                    "type": "string"
                },
                "productActaulMoney": {
                    "description": "总实收",
                    "type": "number"
                },
                "productActualPrice": {
                    "description": "商品实际单价",
                    "type": "number"
                },
                "productCategoryId": {
                    "description": "分类",
                    "type": "string"
                },
                "productCount": {
                    "description": "数量",
                    "type": "integer"
                },
                "productId": {
                    "description": "商品Id",
                    "type": "string"
                },
                "productName": {
                    "description": "商品名称",
                    "type": "string"
                },
                "productPic": {
                    "description": "商品Logo",
                    "type": "string"
                },
                "productPrice": {
                    "description": "商品店铺单价",
                    "type": "number"
                },
                "productSpecifica": {
                    "description": "规格",
                    "type": "string"
                },
                "productType": {
                    "description": "商品类别1-实物商品2-虚拟商品3-组合商品",
                    "type": "integer"
                },
                "promotionId": {
                    "description": "参与限时折扣的活动id",
                    "type": "integer"
                },
                "skuId": {
                    "description": "skuid",
                    "type": "string"
                },
                "verifyCodeList": {
                    "description": "核销码列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjProductVerifyCodeDto"
                    }
                }
            }
        },
        "oc.UpetDjOrderQueryRequest": {
            "type": "object",
            "properties": {
                "memberId": {
                    "description": "登录会员Id",
                    "type": "string"
                },
                "orderSn": {
                    "description": "订单号",
                    "type": "string"
                },
                "pageIndex": {
                    "description": "页索引",
                    "type": "integer"
                },
                "pageSize": {
                    "description": "页大小",
                    "type": "integer"
                },
                "shopId": {
                    "description": "店铺Id",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "state": {
                    "description": "订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单 24 骑手已接单 25 骑手配送中 26 商家配送中 30 已完成",
                    "allOf": [
                        {
                            "$ref": "#/definitions/oc.UpetDjOrderState"
                        }
                    ]
                }
            }
        },
        "oc.UpetDjOrderQueryResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "代码 非 200 取 message 错误信息",
                    "type": "integer"
                },
                "data": {
                    "description": "当前页数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/oc.UpetDjOrderDto"
                    }
                },
                "hasMore": {
                    "description": "是否可以加载更多",
                    "type": "boolean"
                },
                "total": {
                    "description": "总订单数据",
                    "type": "integer"
                }
            }
        },
        "oc.UpetDjOrderState": {
            "type": "integer",
            "enum": [
                0,
                10,
                11,
                20,
                21,
                22,
                23,
                24,
                25,
                26,
                27,
                28,
                29,
                30,
                31,
                32,
                33,
                34,
                41,
                42,
                43,
                46,
                47,
                48,
                49,
                50,
                51
            ],
            "x-enum-varnames": [
                "UpetDjOrderState_all",
                "UpetDjOrderState_unPay",
                "UpetDjOrderState_unPayCancel",
                "UpetDjOrderState_payed",
                "UpetDjOrderState_payedCancel",
                "UpetDjOrderState_payedWaitShopReceive",
                "UpetDjOrderState_payedShopReceived",
                "UpetDjOrderState_deliveried",
                "UpetDjOrderState_delivering",
                "UpetDjOrderState_shipping",
                "UpetDjOrderState_payedShopReceivedManual",
                "UpetDjOrderState_buyerSelfCollection",
                "UpetDjOrderState_payedShopReceivedPicking",
                "UpetDjOrderState_finished",
                "UpetDjOrderState_splitIng",
                "UpetDjOrderState_splitFail",
                "UpetDjOrderState_unVerify",
                "UpetDjOrderState_waitDeliveried",
                "UpetDjOrderState_refunding",
                "UpetDjOrderState_refundClosed",
                "UpetDjOrderState_refundSuccess",
                "UpetDjOrderState_refundFirstPass",
                "UpetDjOrderState_refundFinalPass",
                "UpetDjOrderState_refundFail",
                "UpetDjOrderState_refundUndo",
                "UpetDjOrderState_expressWaitingDelivery",
                "UpetDjOrderState_expressShipped"
            ]
        },
        "oc.UpetDjProductVerifyCodeDto": {
            "type": "object",
            "properties": {
                "createTime": {
                    "description": "创建时间",
                    "type": "string"
                },
                "id": {
                    "description": "核销Id",
                    "type": "integer"
                },
                "orderSn": {
                    "description": "所属子订单号",
                    "type": "string"
                },
                "productId": {
                    "description": "商品Id",
                    "type": "string"
                },
                "updateTime": {
                    "description": "最后更新时间",
                    "type": "string"
                },
                "verifyCode": {
                    "description": "核销码",
                    "type": "string"
                },
                "verifyCodeExpiryDate": {
                    "description": "核销码有效期",
                    "type": "string"
                },
                "verifyShop": {
                    "description": "核销地点财务编码",
                    "type": "string"
                },
                "verifyStatus": {
                    "description": "核销码状态 0未核销，1已核销，2已退款",
                    "type": "integer"
                },
                "verifyTime": {
                    "description": "核销时间",
                    "type": "string"
                }
            }
        },
        "oc.UpetDjRefundDto": {
            "type": "object",
            "properties": {
                "refundMoney": {
                    "description": "总退款金额",
                    "type": "number"
                },
                "refundNo": {
                    "description": "最近退款单号",
                    "type": "string"
                },
                "refundState": {
                    "description": "退款单状态 1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败",
                    "type": "integer"
                }
            }
        },
        "oc.UpetDjRefundProductDto": {
            "type": "object",
            "properties": {
                "productId": {
                    "description": "退款商品Id",
                    "type": "string"
                },
                "refundCount": {
                    "description": "最终退款数量",
                    "type": "integer"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "**********:7040",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "项目接口文档",
	Description:      "这里是描述",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
