package route

import (
	"order-api/controller"

	"github.com/labstack/echo/v4"
)

func OrderRoute(e *echo.Group) {
	g := e.Group("/order")

	//订单支付
	g.POST("/pay", controller.AwenOrderPay)
	//订单标准支付
	g.POST("/pay/standard", controller.StandardPay)
	// 订单支付状态查询
	g.GET("/pay-query", controller.AwenOrderPayQuery)
	//临时提供修改完成订单的接口
	g.POST("/OrderPayCompleteTemporary", controller.OrderPayCompleteTemporary)
	//订单支付(电银B2C)
	g.POST("/paydyb2c", controller.OrderPayDyB2C)
	//订单提交
	//g.POST("/submit", controller.AwenOrderSubmit)
	//new订单提交
	g.POST("/docommit", controller.OrderDoSubmit)
	//订单取消
	g.POST("/cancle", controller.AwenOrderCancle)
	//订单支付成功回调
	g.POST("/offlinenotify", controller.AwenOfflineNotify)
	//电银支付回调
	g.POST("/paynotify", controller.DYPayNotify)
	//电银支付回调
	g.POST("/pinpaynotify", controller.PinPayNotify)

	//物流公司列表
	g.GET("/expresscompanylist", controller.ExpressCompanyList)
	//物流信息更新
	g.POST("/expressinfoupdate", controller.ExpressInfoUpdate)
	//物流路由对接
	g.GET("/expressinfo", controller.ExpressInfo)
	//获取美团骑手位置
	g.GET("/rider-location/get", controller.GetOrderRiderLocation)

	//预售订单推送消息和积分
	g.POST("/pre-sale/push-message", controller.MallPushPreSaleMessage)
	//预售订单推送消息和积分
	g.POST("/pre-sale/push-integral", controller.MallPushPreSaleIntegral)

	//通过第三方订单号获取核销码
	g.GET("/verify-codes", controller.GetVerifyCodeByOldOrderSn)
	//重推OMS
	g.POST("/re-push-oms", controller.RePushOms)

	// 下单后，一系列链路值数据统计
	g.POST("/step-log", controller.OrderStepLog)
	// 提交订单按钮后，收集订阅消息
	g.POST("/subscribe-log", controller.OrderSubscribeLog)
	// 新链路值保存
	g.POST("/link", controller.OrderLinkStore)
	// 检查是否开过处方
	g.POST("/prescribe-check", controller.OrderPrescribeCheck)
	// 开处方
	g.POST("/prescribe", controller.OrderPrescribe)
}
