package main

import (
	"flag"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	_ "order-api/docs"
	"order-api/route"
	"os"
	"strings"
	"time"

	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
)

var env string

func init() {
	sh, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = sh

	env = strings.ToLower(os.Getenv("ASPNETCORE_ENVIRONMENT"))
	if env == "" {
		env = "staging"
	}
}

// @title 项目接口文档
// @version 1.0
// @description 这里是描述
// @host **********:7040
func main() {
	//日志命令行参数化处理，可以启用禁用控制台日志等，defer确认在程序退出时将所有缓冲日志写入es
	defer glog.Flush()
	flag.Parse()

	go func() { http.ListenAndServe(":31110", nil) }()

	//初始化路由
	e := route.InitRoute()

	//开启debug模式
	if env == "staging" || env == "uat" {
		//e.Debug = true
	}

	//获取项目ip端口
	addr := getAppAddr()

	//if env != "staging" {
	//	apm.DefaultTracer.SetCaptureBody(apm.CaptureBodyTransactions)
	//	e.Use(apmechov4.Middleware())
	//}

	glog.Info("启动端口:" + addr)
	e.Logger.Fatal(e.Start(addr))
}

func getAppAddr() string {
	addr := ":7040" //默认启动端口
	baseURL, err := config.Get("BaseUrl")
	if err == nil && len(baseURL) > 0 {
		items := strings.Split(baseURL, ":")
		addr = fmt.Sprintf(":%v", items[len(items)-1])
	}

	return addr
}
