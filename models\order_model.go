package models

import "order-api/proto/oc"

type NotifyRequest struct {
	//支付平台流水号
	TradeNo string `json:"tradeNo,omitempty"`
	//商户流水号,仅能用大小写字母与数,字，且在商户系统具有唯一性
	OutTradeNo string `json:"outTradeNo"`
	//订单日期格式：YYYYMMDD
	OrderTime string `json:"orderTime"`
	//实付金额 单位分
	PayPrice string `json:"payPrice"`
	//订单金额 单位分
	TotalPrice string `json:"totalPrice"`
	//优惠金额 单位分
	Discount string `json:"discount"`
	//添加时间
	AddTime string `json:"addTime"`
	//支付时间
	PayTime string `json:"payTime"`
	//商品编号
	ProductId string `json:"productId"`
	//商品名称 最长 32 字节
	ProductName string `json:"productName"`
	//商品描述
	ProductDesc string `json:"productDesc"`
	//后台回调地址
	OfflineNotifyUrl string `json:"offlineNotifyUrl"`
	//客户端 IP
	ClientIP string `json:"clientIP"`
	//商户号
	MerchantId string `json:"merchantId"`
	//扩展信息 预留字段，JSON 格式
	ExtendInfo string `json:"extendInfo"`
	//扣款通道返回的流水号
	ChannelNo string `json:"channelNo"`
	//签名
	Sign string `json:"sign"`
	//支付方式  1：微信 JSAPI，2：C扫B，3：B扫C,8:储蓄卡支付
	PayType string `json:"payType"`
	//订单号
	OrderId string `json:"orderId"`
}
type NotifyResponse struct {
	Result string `json:"result,omitempty"`
}

type DYPayNotifyRequest struct {
	//订单号
	OrderNo string `json:orderNo`
	//支付状态
	PayStatus int32 `json:payStatus`
	//支付时间
	PayTime string `json:payTime`
	//支付中心订单号
	TradeNo string `json:tradeNo`
	//支付金额
	PayAmount int32 `json:payAmount`
}
type PayNotifyResponse struct {
	Code    int32  `json:code`
	Message string `json:message`
}

type OrderPayRequest struct {
	//订单id
	Order_sn string
	//微信用户标识 JSAPI 支付时必传
	Openid string
	//支付方式  1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
	// 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付 17：标准支付宝小程序支付 18：百度小程序支付
	TransType int32 `json:"transType"`
	//订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 99助力订单
	OrderType int32 `json:"orderType"`
	// appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家
	AppId int32 `json:"app_id"`
}

type OrderPayDyB2CRequest struct {
	//订单id
	OrderSn string `json:"order_sn"`
	//付款码
	BarCode string `json:"bar_code"`
	//支付方式 1：微信 2：支付宝 3: 银联
	PayType int32 `json:"pay_type"`
	//商户号
	MerchantId string `json:"merc_id"`
	//机构号
	OrgId string `json:"org_id"`
	//机具编号
	TrmSn string `json:"trm_sn"`
	//终端号
	TrmId string `json:"trm_id"`
	//竖屏支付来源 1-商城 0-默认到家
	Source   int32 `json:"source"`
	Location string
}

type OrderPayResponse struct {
	//状态码
	Code int32 `json:"code"`
	//消息
	Message string `json:"message"`
	//错误信息
	Error string `json:"error"`
	//返回数据
	Data string `json:"data"`
	//下单时间(13位，毫秒)
	CreateTime int64 `json:"create_time"`
}

type ProductSnapshotResponse struct {
	Product SnapsProduct      `json:"product"`
	SkuInfo []SnapshotSkuInfo `json:"sku_info"`
}
type SnapsProduct struct {
	Pic         string `json:"pic"`
	ProductType int32  `json:"product_type"`
	GroupType   int32  `json:"group_type"`
}
type SnapshotSkuInfo struct {
	SkuId         int32           `json:"sku_id"`
	MarketPrice   int32           `json:"market_price"`
	BarCode       string          `json:"bar_code"`
	SnapsSkuv     []SnapshotSkuv  `json:"skuv"`
	SnapsThirdSku []SnapsSkuThird `json:"sku_third"`
	SkuGroup      []*SkuGroup     `json:"sku_group"`
}
type SkuGroup struct {
	SkuId          int32  `json:"sku_id"`
	GroupProductId int32  `json:"group_product_id"`
	GroupSkuId     int32  `json:"group_sku_id"`
	Count          int32  `json:"count"`
	ProductName    string `json:"product_name"`
	DiscountType   int32  `json:"discount_type"`
	DiscountValue  int32  `json:"discount_value"`
	MarketPrice    int32  `json:"market_price"`
	ProductType    int32  `json:"product_type"`
}
type SnapsSkuThird struct {
	ThirdSkuId string `json:"third_sku_id"`
	ErpId      int32  `json:"erp_id"`
}
type SnapshotSkuv struct {
	Pic       string `json:"pic"`
	SpecName  string `json:"spec_name"`
	SpecValue string `json:"spec_value_value"`
}

type RiderLocationResponse struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}
type RiderLocation struct {
	Lat int32 `json:"lat"`
	Lng int32 `json:"lng"`
}

type MallPushPreSaleMessageRequest struct {
	OrderSn string `json:"order_sn"`
	//发送尾款消息
	SendMessage *oc.PreSalePay `json:"send_message"`
}
type MallPushPreSaleIntegralRequest struct {
	OrderSn string `json:"order_sn"`
	//支付类型 0：加定金积分 1：退定金积分
	PayType int32 `json:"pay_type"`
	//支付金额
	PayPrice int32 `json:"pay_price"`
}

type PushOmsRequest struct {
	//订单号
	OrderSn string `json:"order_sn"`
	//交易号
	PaySn string `json:"pay_sn"`
}
