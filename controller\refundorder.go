package controller

import (
	"order-api/models"
	"order-api/proto/oc"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	r "github.com/tricobbler/echo-tool/httpError"
	kit "github.com/tricobbler/rp-kit"
)

// @Summary 申请售后单
// @Tags 售后单
// @Accept json
// @Produce json
// @Param apply body oc.RefundOrderApplyRequest true " "
// @Success 200 {object} oc.RefundOrderApplyResponse
// @Failure 400 {object} oc.RefundOrderApplyResponse
// @Router /order-api/refund/apply [post]
func Apply(c echo.Context) error {
	model := new(oc.RefundOrderApplyRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	model.ResType = "等待处理中"
	model.ApplyOpUserType = "1"
	if model.OrderFrom <= 0 {
		model.OrderFrom = 4 //渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家)
	}
	if model.ChannelId <= 0 {
		model.ChannelId = 1 //来源渠道id（datacenter.platform_channel表）1阿闻到家 2美团 3饿了么 4京东到家 5阿闻电商 6门店
	}
	model.OperationType = "用户申请售后单"
	model.ExternalOrderId = model.OrderId
	//refund_remark := model.RefundRemark
	//if len(refund_remark) > 0 {
	//	dacClient := dac.GetDataCenterClient()
	//	defer dacClient.Close()
	//	res, err := dacClient.RPC.ContentSecurity(dacClient.Ctx, &dac.ContentSecurityRequest{
	//		Type:    1,
	//		Content: refund_remark,
	//	})
	//	if err != nil {
	//		return r.NewHTTPError(400, err.Error())
	//	}
	//	if res.Code != 200 {
	//		return r.NewHTTPError(400, res.Message)
	//	}
	//}
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	grpcRes, err := ocClient.ROC.RefundOrderApply(ocClient.Ctx, model)
	if err != nil {
		glog.Error("调用RefundOrderApply失败，", err, "，参数：", kit.JsonEncode(model))
		return r.NewHTTPError(400, err.Error())
	}
	//推送退款订单到腾讯有数
	//go AddReturnOrderToTencent(model.OrderId)

	return c.JSON(200, grpcRes)
}

// @Summary 撤销申请售后单
// @Tags 售后单
// @Accept json
// @Produce json
// @Param cancel body oc.RefundOrderCancelRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/refund/cancel [post]
func Cancel(c echo.Context) error {
	model := new(oc.RefundOrderCancelRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}
	model.OperationUser = memberInfo.Name
	model.ResType = "用户取消售后申请"
	model.OperationType = "用户取消售后申请"

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()
	grpcRes, err := ocClient.ROC.RefundOrderCancel(ocClient.Ctx, model)
	if err != nil {
		glog.Error("调用RefundOrderCancel失败，", err, "，参数：", kit.JsonEncode(model))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, grpcRes)
}

//退款支付重新调用
// @Router /order-api/refund/pay [get]
func Pay(c echo.Context) error {
	in := &oc.RefundOrderPayRequest{
		RefundOrderSn: c.QueryParam("refund_order_sn"),
		ResType:       "商家发起退款",
		OperationType: "商家发起退款",
		OperationUser: "454545",
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	grpcRes, err := ocClient.ROC.RefundOrderPay(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用RefundOrderPay失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, grpcRes)
}
