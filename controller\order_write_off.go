package controller

import (
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-api/models"
	"order-api/proto/oc"
	"strings"
)

//todo 核销模块的返回结构不要动，为了保持C#的一样的逻辑，这样对接方就不用修改了。

// @Summary 查询需要核销的订单信息 -- 只支持核销码查询接口
// @Tags 虚拟订单核销
// @Accept json
// @Produce json
// @Param writtenoffcode query string false "核销码"
// @Success 200 {object} models.OldOrderDetail
// @Failure 400 {string} string "核销码不能为空"
// @Router /order/order/get [get]
func GetVirtualOrderDetail(c echo.Context) error {
	writtenOffCode := c.QueryParam("writtenoffcode")
	if strings.Trim(writtenOffCode, " ") == "" {
		return c.String(400, "核销码不能为空")
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.GetVirtualOrderDetailRequest{
		WrittenOffCode: writtenOffCode,
	}
	glog.Info("核销码详情查询请求查询参数", kit.JsonEncode(in))
	orderRes, err := ocClient.RPC.GetVirtualOrderDetail(ocClient.Ctx, in)
	glog.Info("核销码详情查询请求查询返回结果", kit.JsonEncode(orderRes))
	if err != nil {
		glog.Error("调用OrderPayCompleteTemporary失败，", err, "，参数：", kit.JsonEncode(in))
		return c.String(400, err.Error())
	}
	oldOrderDetail := models.OldOrderDetail{}
	oldOrderGoods := models.OrderDetailGoods{}
	_ = json.Unmarshal([]byte(orderRes.OldOrderDetail), &oldOrderDetail)
	_ = json.Unmarshal([]byte(orderRes.OldOrderDetail), &oldOrderGoods)

	//因为北京那边核销查询接口要求我们把核销的建议价放到Sellprice里面，和电商一致
	//商城 Sellprice = MarkingPrice
	//本地生活 Sellprice = payPrice
	/*	if oldOrderDetail.ChannelId > 0 && oldOrderDetail.ChannelId == 5 {
		oldOrderGoods.Sellprice = oldOrderGoods.MarkingPrice
	}*/
	//oldOrderGoods.Sellprice = oldOrderGoods.Sellprice
	oldOrderDetail.Goods = append(oldOrderDetail.Goods, oldOrderGoods)
	if oldOrderDetail.Orderid == "" {
		return c.String(400, "核销码不存在")
	}
	return c.JSON(200, oldOrderDetail)
}

// @Summary 核销虚拟信息
// @Tags 虚拟订单核销
// @Accept json
// @Produce json
// @Param WriteOffVirtualOrder body models.WriteOffReq true " "
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order/order/written-off [post]
func WriteOffVirtualOrder(c echo.Context) error {
	model := new(models.WriteOffReq)
	if err := c.Bind(model); err != nil {
		return c.String(400, err.Error())
	}
	if strings.Trim(model.VerifyCode, " ") == "" || strings.Trim(model.StoreId, " ") == "" {
		return c.String(400, "参数错误")
	}
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.WrittenOffVirtualOrderRequest{
		VerifyCode: model.VerifyCode,
		StoreId:    model.StoreId,
		MemberId:   model.MemberId,
		Source:     model.Source,
		UserAgent:  model.UserAgent,
	}
	writeoffRes, err := ocClient.RPC.WrittenOffVirtualOrder(ocClient.Ctx, in)
	glog.Info(fmt.Sprintf("虚拟订单核销参数：%v,返回结果：%v,错误信息：%v", in, kit.JsonEncode(writeoffRes), err))
	if err != nil {
		glog.Error("调用WrittenOffVirtualOrder失败，", err, "，参数：", kit.JsonEncode(in))
		return c.String(400, err.Error())
	}
	if writeoffRes.Code == 200 {
		return c.String(200, "")
	}

	return c.String(400, writeoffRes.Message)

}

// @Summary 获取用户未核销记录
// @Tags 虚拟订单核销
// @Accept json
// @Produce json
// @Param model query oc.QueryMallVirtualOrderWriteOffCodesRequest true " "
// @Success 200 {object} oc.QueryMallVirtualOrderWriteOffCodesResponse
// @Failure 400 {object} oc.QueryMallVirtualOrderWriteOffCodesResponse
// @Router /order/order/virtual-order-write-off-codes [get]
func SearchVirtualOrderCodesByScrmUserId(c echo.Context) error {
	in := &oc.QueryMallVirtualOrderWriteOffCodesRequest{
		ScrmUserId: c.QueryParam("scrm_user_id"),
		VrOrderSn:  c.QueryParam("vr_order_sn"),
		PageIndex:  cast.ToInt32(c.QueryParam("page_index")),
		PageSize:   cast.ToInt32(c.QueryParam("page_size")),
		Type:       cast.ToInt32(c.QueryParam("type")),
	}
	ocClient := oc.GetOrderServiceClient()
	if rsp, err := ocClient.RPC.QueryMallVirtualOrderWriteOffCodes(ocClient.Ctx, in); err != nil {
		glog.Error("QueryMallVirtualOrderWriteOffCodes 入参：", kit.JsonEncode(in), "，错误：", err.Error())
		return c.JSON(400, &oc.QueryMallVirtualOrderWriteOffCodesResponse{Code: 400, Message: "查询核销记录错误"})
	} else {
		return c.JSON(int(rsp.Code), rsp)
	}
}
