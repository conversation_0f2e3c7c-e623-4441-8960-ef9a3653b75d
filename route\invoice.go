package route

import (
	"github.com/labstack/echo/v4"
	"order-api/controller"
)

func InvoiceRoute(e *echo.Group) {
	g := e.Group("/invoice")

	// 发票相关
	g.GET("/companies", controller.QueryInvoiceCompany)
	g.GET("/company-info", controller.InvoiceCompanyInfo)
	g.POST("/apply", controller.InvoiceApply)
	g.GET("/status", controller.InvoiceStatus)
	g.GET("/detail", controller.InvoiceDetail)
	g.POST("/send-email", controller.InvoiceSendEmail)
	g.POST("/refund", controller.RefundInvoice)

	// 发票抬头相关
	t := e.Group("/invoice-title")
	t.POST("/add", controller.InvoiceTitleAdd)
	t.POST("/edit", controller.InvoiceTitleEdit)
	t.POST("/list", controller.InvoiceTitleList)
}
