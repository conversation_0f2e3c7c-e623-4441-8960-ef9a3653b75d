package controller

import (
	"context"
	"net/http"
	"reflect"

	"github.com/spf13/cast"

	"order-api/models"
	"order-api/proto/dac"
	"order-api/proto/mk"
	"order-api/proto/oc"
	"order-api/utils"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	r "github.com/tricobbler/echo-tool/httpError"
	kit "github.com/tricobbler/rp-kit"
)

// @Summary 加载省市区列表
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body oc.SectionQueryRequest true "数据请求参数"
// @Success 200 {object} oc.SectionQueryResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/QuerySection [get]
func QuerySection(c echo.Context) error {
	// 定义请求响应
	var request = new(oc.SectionQueryRequest)
	// 调用Grpc封装方法
	return ProcessGrpcRequest(c, request, func(ocClient *Client) (interface{}, error) {
		return ocClient.UpetDJ.QuerySectionList(ocClient.Ctx, request)
	})
}

// @Summary 根据购物车商品计算优惠信息以及运费
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body mk.PromotionCalcRequest true "数据请求参数"
// @Success 200 {object} mk.PromotionCalcResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/CalcPromotionMoney [post]
func CalcPromotionMoney(c echo.Context) error {
	var request = new(mk.PromotionCalcRequest)
	if err := c.Bind(request); err != nil {
		glog.Error("参数错误，", err)
		return r.NewHTTPError(400, err.Error())
	}
	if len(request.PromotionProduct) == 0 {
		return r.NewHTTPError(400, "请传递商品信息")
	}

	// saas-v1.0  如果主体是宠物saas 中间件那边会把线下门店端的用户id赋值给scrm_id
	request.UserId = cast.ToString(c.Get("scrm_id"))
	// grpc
	var client = mk.GetMarketingCenterClient()
	defer client.Close()

	// 查询远端代理
	res, err := client.RPC.CalcPromotionMoney(client.Ctx, request)
	if err != nil {
		glog.Error("调用CalcPromotionMoney失败，", err, "，参数：", kit.JsonEncode(request))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(http.StatusOK, res)
}

// @Summary 计算运费
// @Tags 宠物SAAS
// @Produce  json
// @Accept  json
// @Param request body mk.PromotionCalcRequest true "数据请求参数"
// @Success 200 {object} mk.PromotionCalcResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/GetDeliveryMoney [post]
func GetDeliveryMoney(c echo.Context) error {
	var request = new(mk.GetDeliveryMoneyRequest)
	if err := c.Bind(request); err != nil {
		glog.Error("参数错误，", err)
		return r.NewHTTPError(400, err.Error())
	}

	var client = mk.GetMarketingCenterClient()
	defer client.Close()

	// 查询远端代理
	res, err := client.RPC.GetDeliveryMoney(client.Ctx, request)
	if err != nil {
		glog.Error("调用CalcPromotionMoney失败，", err, "，参数：", kit.JsonEncode(request))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(http.StatusOK, res)
}

// @Summary 修改订单状态，确认收货
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body oc.UpetDjConfirmRequest true "数据请求参数"
// @Success 200 {object} oc.UpetDjConfirmResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/ConfirmUpetDj [put]
func ConfirmUpetDj(c echo.Context) error {
	// 新建请求响应实体
	var request = new(oc.UpetDjConfirmRequest)
	// 调用Grpc封装方法
	return ProcessGrpcRequest(c, request, func(ocClient *Client) (interface{}, error) {
		return ocClient.UpetDJ.ConfirmUpetDj(ocClient.Ctx, request)
	})
}

// QueryUpetDjOrderList
// @Summary 查询阿闻到家订单-v6.0
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body oc.UpetDjOrderQueryRequest true "数据请求参数"
// @Success 200 {object} oc.UpetDjOrderQueryResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/QueryUpetDjOrderList [get]
func QueryUpetDjOrderList(c echo.Context) error {
	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	// 查询登录用户
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	// 新建请求响应实体
	var request = new(oc.UpetDjOrderQueryRequest)
	shopId := cast.ToString(c.Get("shop_id"))

	glog.Info("获取订单列表， 入参：", kit.JsonEncode(request))
	// 调用Grpc封装方法
	return ProcessGrpcRequest(c, request, func(ocClient *Client) (interface{}, error) {
		if len(shopId) > 0 {
			request.ShopId = []string{shopId}
		}
		request.MemberId = memberInfo.Id
		request.ChannelId = cast.ToInt32(c.Request().Header.Get("channel_id"))
		return ocClient.UpetDJ.QueryUpetDjOrderList(ocClient.Ctx, request)
	})
}

// @Summary 查询阿闻到家订单
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body oc.UpetDjOrderQueryRequest true "数据请求参数"
// @Success 200 {object} oc.UpetDjOrderQueryResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/QueryUpetDjOrderListByModile [get]
func QueryUpetDjOrderListByModile(c echo.Context) error {
	// 新建请求响应实体
	var request = new(oc.UpetDjOrderQueryRequest)

	mobile := c.QueryParam("mobile")
	orderSn := c.QueryParam("orderSn")
	memberInfo, err := utils.GetMemberInfoByMobile(mobile)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	//memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	//if !ok || memberInfo == nil {
	//	return r.NewHTTPError(400, "用户不存在")
	//}

	// 调用Grpc封装方法
	return ProcessGrpcRequest(c, request, func(ocClient *Client) (interface{}, error) {
		request.MemberId = memberInfo.Id
		request.OrderSn = orderSn
		return ocClient.UpetDJ.QueryUpetDjOrderList(ocClient.Ctx, request)
	})
}

// @Summary 查询阿闻到家订单详情-v6.0
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body oc.UpetDjOrderDetailRequest true "数据请求参数"
// @Success 200 {object} oc.UpetDjOrderDetailResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/QueryUpetDjOrderDetail [get]
func QueryUpetDjOrderDetail(c echo.Context) error {
	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}
	var request = new(oc.UpetDjOrderDetailRequest)
	return ProcessGrpcRequest(c, request, func(ocClient *Client) (interface{}, error) {
		request.MemberId = memberInfo.Id
		return ocClient.UpetDJ.QueryUpetDjOrderDetail(ocClient.Ctx, request)
	})
}

// @Summary 根据订单号，查询门店信息中当前门店的订单是否需要自动打单
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body oc.UpetDjOrderIsAutoPrintQueryRequest true "数据请求参数"
// @Success 200 {object} oc.UpetDjOrderIsAutoPrintQueryResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/QueryUpetDjOrderIsAutoPrint [get]
func QueryUpetDjOrderIsAutoPrint(c echo.Context) error {
	var request = new(oc.UpetDjOrderIsAutoPrintQueryRequest)
	return ProcessGrpcRequest(c, request, func(ocClient *Client) (interface{}, error) {
		return ocClient.UpetDJ.QueryUpetDjOrderIsAutoPrint(ocClient.Ctx, request)
	})
}

// @Summary 查询店铺的配送配置
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param request body dac.ShopDeliveryServiceDetailRequest true "数据请求参数"
// @Success 200 {object} dac.ShopDeliveryServiceDetailResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/QueryUpetDjShopDelivery [get]
func QueryUpetDjShopDelivery(c echo.Context) error {
	//构造请求
	var request = new(dac.ShopDeliveryServiceDetailRequest)
	if err := c.Bind(request); err != nil {
		glog.Error("参数错误，", err)
		return r.NewHTTPError(http.StatusBadRequest, "参数绑定失败")
	}

	//调用数据中心方法打印
	dacClient := dac.GetDataCenterClient()
	defer dacClient.Close()

	// 查询数据
	grpcRes, err := dacClient.RPC.ShopDeliveryServiceDetail(dacClient.Ctx, request)
	if err != nil {
		glog.Error("调用ShopDeliveryServiceDetail失败，", err, "，参数：", kit.JsonEncode(request))
		return r.NewHTTPError(http.StatusBadRequest, err.Error())
	}
	if grpcRes.Code != 200 {
		return r.NewHTTPError(http.StatusBadRequest, grpcRes.Message)
	}

	return c.JSON(http.StatusOK, grpcRes)
}

// @Summary 根据门店配置是否自动打印配置推送给前端管理员
// @Tags 阿闻到家
// @Produce  json
// @Accept  json
// @Param orderId body string true "订单号码"
// @Success 200 {object} oc.BadRequestResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/PrintWithOrderId [get]
func PrintWithOrderId(c echo.Context) error {
	var request = new(oc.UpetDjOrderIsAutoPrintQueryRequest)
	var baseResponse = new(oc.BadRequestResponse)
	ProcessGrpcRequest(c, request, func(ocClient *Client) (interface{}, error) {
		res, err := ocClient.UpetDJ.QueryUpetDjOrderIsAutoPrint(ocClient.Ctx, request)
		if err == nil && res.IsAutoPrint {
			//查询单据明细信息
			in := &oc.UpetDjOrderDetailRequest{
				OrderSn: request.OrderSn,
			}
			detailResponse, err := ocClient.UpetDJ.QueryUpetDjOrderDetail(ocClient.Ctx, in)
			if detailResponse.OrderListInfo != nil && err == nil {
				//调用数据中心方法打印
				var dacClient = dac.GetDataCenterClient()
				defer dacClient.Close()

				in := &dac.MessageSendWithShopIdRequest{
					IsOnlyAuthorityUser: true,
					ShopId:              detailResponse.OrderListInfo.ShopId,
					Msg:                 "需要打印单据",
				}
				if _, err = dacClient.RPC.MessageSendWithShopId(context.Background(), in); err != nil {
					glog.Error("调用MessageSendWithShopId失败，", err, "，参数：", kit.JsonEncode(in))
				}
			}
		} else {
			baseResponse.Code = int32(mk.Code_businessError)
			baseResponse.Message = "未开启自动打印"
		}
		return baseResponse, err
	})
	return nil
}

// @Tags 阿闻到家
// @Summary 社区团购附近站点查询
// @Produce json
// @Accept json
// @Param model query dac.AWStationsReq true " "
// @Success 200 {object} dac.AWStationsResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/pickup-stations [get]
func PickupStations(c echo.Context) error {
	req := &dac.AWStationsReq{
		Lng:         c.QueryParam("lng"),
		Lat:         c.QueryParam("lat"),
		FinanceCode: c.QueryParam("finance_code"),
		Keyword:     c.QueryParam("keyword"),
		PageSize:    cast.ToInt32(c.QueryParam("page_size")),
		PageIndex:   cast.ToInt32(c.QueryParam("page_index")),
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.AWStations(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.BadRequestResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Tags 阿闻到家
// @Summary 社区团购站点状态
// @Produce json
// @Accept json
// @Param model query dac.AWStationStateReq true " "
// @Success 200 {object} dac.AWStationStateResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/pickup-station/state [get]
func PickupStationState(c echo.Context) error {
	req := &dac.AWStationStateReq{
		FinanceCode: c.QueryParam("finance_code"),
		StationId:   cast.ToInt32(c.QueryParam("station_id")),
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.AWStationState(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.BadRequestResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Tags 阿闻到家
// @Summary 社区团购最近的站点
// @Produce json
// @Accept json
// @Param model query dac.AWStationNearestReq true " "
// @Success 200 {object} dac.AWStationNearestResponse
// @Failure 400 {object} oc.BadRequestResponse
// @Router /order-api/upetDj/pickup-station/nearest [get]
func PickupStationNearest(c echo.Context) error {
	req := &dac.AWStationNearestReq{
		Lng:         c.QueryParam("lng"),
		Lat:         c.QueryParam("lat"),
		FinanceCode: c.QueryParam("finance_code"),
	}

	client := dac.GetDataCenterClient()
	if out, err := client.PU.AWStationNearest(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.BadRequestResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// grpc 代理方法定义
type GrpcFuncCall func(client *Client) (interface{}, error)

// 处理Http到Grpc的请求
func ProcessGrpcRequest(c echo.Context, request interface{}, specialGrpcFun GrpcFuncCall) error {
	// 400 错误响应
	var badRequestResponse = new(oc.BadRequestResponse)
	badRequestResponse.Code = 400
	//获取请求参数

	err := c.Bind(request)
	if err != nil {
		return r.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 获取grpc
	client := GetOrderServiceClient()
	defer client.Close()

	// 调用具体的Grpc方法
	response, err := specialGrpcFun(client)
	if err != nil {
		glog.Error("调用grpc方法失败，", err, "，参数：", kit.JsonEncode(request))
		return r.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	var responseType = reflect.ValueOf(response)
	if responseType.Kind() == reflect.Ptr {
		responseType = responseType.Elem()
	}
	//查询code 属性
	codePrototy := responseType.FieldByName("Code")
	if codePrototy.Kind() != reflect.Invalid {
		grpcCode := codePrototy.Int()
		// 解析错误代码或业务逻辑代码
		if grpcCode == 400 {
			badRequestResponse.Code = 400
			// 是否有Message 信息
			messageFiled := responseType.FieldByName("Message")
			if messageFiled.Kind() != reflect.Invalid {
				badRequestResponse.Message = messageFiled.String()
			} else {
				badRequestResponse.Message = "业务错误"
			}
			return r.NewHTTPError(http.StatusBadRequest, badRequestResponse.Message)
		}
	}

	return c.JSON(http.StatusOK, response)
}
