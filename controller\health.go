package controller

import (
	"context"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	r "github.com/tricobbler/echo-tool/httpError"
	kit "github.com/tricobbler/rp-kit"
	"order-api/models"
	"order-api/proto/oc"
	"order-api/utils"
)

// @Summary 获取健康管理订阅套餐信息
// @Tags 健康管理
// @Accept json
// @Produce json
// @Param HealthSubOrderRequest body models.CompanyCategoryReq true " "
// @Success 200 {object} models.GetCompanyCategoryResponse
// @Failure 400 {object} models.GetCompanyCategoryResponse
// @Router /order-api/health/company-category [post]
func GetCompanyCategory(c echo.Context) error {
	model := models.CompanyCategoryReq{}
	if err := c.Bind(&model); err != nil {
		glog.Error("解析参数错误，", err)
		return r.NewHTTPError(400, "解析参数错误")
	}
	glog.Info("获取健康管理订阅套餐信息：参数:", kit.JsonEncode(model))
	req := models.GetCompanyCategoryListReq{Criteria: model}

	data, err := utils.GetCompanyCategoryList(req)
	if err != nil {
		glog.Error("调用GetCompanyCategoryList失败，", err, "，参数：", kit.JsonEncode(model))
		return r.NewHTTPError(400, err.Error())
	}
	if !data.Success {
		return r.NewHTTPError(400, data.Message)
	}

	if data.Result.Total <= 0 || len(data.Result.Result) <= 0 {
		res := models.GetCompanyCategoryResponse{
			BaseResponse: models.BaseResponse{
				Code:    200,
				Message: "找不到套餐信息",
			},
		}
		return c.JSON(200, res)
		//return r.NewHTTPError(400, "找不到套餐信息。")
	}

	data.Result.Result[0].CostMoney = data.Result.Result[0].CostMoney * 100
	data.Result.Result[0].OriginalPrice = data.Result.Result[0].OriginalPrice * 100
	out := models.GetCompanyCategoryResponse{
		BaseResponse: models.BaseResponse{
			Code:    200,
			Message: "ok",
		},
		Total:     data.Result.Total,
		PageIndex: data.Result.PageIndex,
		PageSize:  data.Result.PageSize,
		PageCount: data.Result.PageCount,
		Data:      &data.Result.Result,
	}
	return c.JSON(200, out)
}

// @Summary 获取健康管理订阅套餐信息（带详情）
// @Tags 健康管理
// @Accept json
// @Produce json
// @Param HealthSubOrderRequest body models.SetMealReq true " "
// @Success 200 {object} models.GetSetMealsRes
// @Failure 400 {object} models.GetSetMealsRes
// @Router /order-api/health/set-meal [post]
func GetSetMeals(c echo.Context) error {
	model := models.SetMealReq{}
	if err := c.Bind(&model); err != nil {
		glog.Error("解析参数错误，", err)
		return r.NewHTTPError(400, "解析参数错误")
	}
	glog.Info("获取健康管理订阅套餐信息：参数:", kit.JsonEncode(model))
	req := models.GetSetMealsReq{Criteria: model}
	res, err := utils.GetSetMeals(req)
	if err != nil {
		glog.Error("调用GetSetMeals失败，", err, "，参数：", kit.JsonEncode(model))
		return r.NewHTTPError(400, err.Error())
	}
	if !res.Success {
		return r.NewHTTPError(400, res.Message)
	}
	out := models.GetSetMealsRes{
		BaseResponse: models.BaseResponse{
			Code:    200,
			Message: "ok",
		},
		Data: &res.Result,
	}
	return c.JSON(200, out)
}

// @Summary 健康管理订阅订单提交
// @Tags 健康管理
// @Accept json
// @Produce json
// @Param HealthSubOrderRequest body oc.HealthSubOrderRequest true " "
// @Success 200 {object} oc.HealthSubOrderResponse
// @Failure 400 {object} oc.HealthSubOrderResponse
// @Router /order-api/health/order-submit [post]
func HealthSubOrder(c echo.Context) error {
	model := new(oc.HealthSubOrderRequest)
	if err := c.Bind(model); err != nil {
		glog.Error("解析参数错误，", err)
		return r.NewHTTPError(400, "解析参数错误")
	}
	glog.Info("健康管理订阅订单：订单参数:", kit.JsonEncode(model))
	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	model.ScrmUserId = memberInfo.ScrmUserId
	model.MemberTel = memberInfo.ScrmUserMobile
	model.MemberName = memberInfo.ScrmUserName

	if len(model.ScrmUserId) <= 0 {
		return r.NewHTTPError(400, "用户不存在")
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()
	//金额验证：总金额=商品总价+配送费+服务费+包装费-优惠金额-配送费优惠
	glog.Info("健康管理订阅订单：统一下单：", kit.JsonEncode(model))

	grpcRes, err := ocClient.Cart.HealthSubOrder(kit.SetTimeoutCtx(utils.AppendToOutgoingContextLoginUserInfo(context.Background(), c)), model)
	if err != nil {
		glog.Error("调用HealthSubOrder失败，", err, "，参数：", kit.JsonEncode(model))
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, grpcRes)
}

// @Summary 健康管理-退卡
// @Tags 健康管理
// @Accept json
// @Produce json
// @Param RefundHealthPlanCardReq body models.RefundHealthPlanCardReq true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/health/refund-card [post]
func RefundHealthPlanCard(c echo.Context) error {
	model := new(models.RefundHealthPlanCardReq)
	if err := c.Bind(model); err != nil {
		glog.Error("解析参数错误，", err)
		return r.NewHTTPError(400, "解析参数错误")
	}

	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	//grpc调datacenter插入order数据
	request := oc.HealthPlanRefundOrderRequest{
		UserId:       memberInfo.ScrmUserId,
		PetId:        model.PetId,
		EnsureCode:   model.EnsureCode,
		BatchCode:    model.BatchCode,
		RefundAmount: model.RefundAmount,
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	glog.Info("健康管理-退卡：参数:", kit.JsonEncode(request))

	grpcRes, err := ocClient.ROC.HealthPlanRefundOrder(kit.SetTimeoutCtx(utils.AppendToOutgoingContextLoginUserInfo(context.Background(), c)), &request)
	if err != nil {
		glog.Error("调用RefundHealthPlanCard失败，", err, "，参数：", kit.JsonEncode(request))
		return r.NewHTTPError(400, err.Error())
	}

	if grpcRes.Code != 200 {
		return r.NewHTTPError(400, grpcRes.Message)
	}

	//调JAVA接口退卡
	req := &models.RefundPlanCardReq{
		UserId:       memberInfo.ScrmUserId,
		PetId:        model.PetId,
		EnsureCode:   model.EnsureCode,
		BatchCode:    model.BatchCode,
		RefundAmount: float64(model.RefundAmount), //model.RefundAmount,
	}

	data := utils.RefundHealthPlanCard(*req)
	if !data.Success || data.StatusCode != 200 {
		return r.NewHTTPError(400, data.Message)
	}

	return c.JSON(200, grpcRes)
}

// @Summary 健康管理- 退款预算
// @Tags 健康管理
// @Accept json
// @Produce json
// @Param RefundHealthPlanCardRequest body models.RefundAmountReq true " "
// @Success 200 {object} models.RefundHealthAmountResponse
// @Failure 400 {object} models.RefundHealthAmountResponse
// @Router /order-api/health/refund-amount [post]
func RefundHealthAmount(c echo.Context) error {
	model := models.RefundAmountReq{}
	if err := c.Bind(&model); err != nil {
		glog.Error("解析参数错误，", err)
		return r.NewHTTPError(400, "解析参数错误")
	}
	glog.Info("健康管理退款预算：订单参数:", kit.JsonEncode(model))

	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}
	model.UserId = memberInfo.ScrmUserId
	res := utils.RefundAmount(model)
	if !res.Success {
		return r.NewHTTPError(400, res.Message)
	}

	if res.Result.CostAmount > 0 {
		res.Result.CostAmount = res.Result.CostAmount * 100
		res.Result.RefundAmount = res.Result.RefundAmount * 100
		res.Result.UsedOriginalAmount = res.Result.UsedOriginalAmount * 100
	}

	out := models.RefundHealthAmountResponse{
		BaseResponse: models.BaseResponse{
			Code:    200,
			Message: "ok",
		},
		Data: &res.Result,
	}
	return c.JSON(200, out)

}

// @Summary 健康管理- 卡权益查询
// @Tags 健康管理
// @Accept json
// @Produce json
// @Param EnsureCardsListReq body models.EnsureCardsListReq true " "
// @Success 200 {object} models.EnsureCardsList
// @Failure 400 {object} models.EnsureCardsList
// @Router /order-api/health/cards-list [post]
func EnsureCardsList(c echo.Context) error {
	model := models.EnsureCardsListReq{}
	if err := c.Bind(&model); err != nil {
		glog.Error("解析参数错误，", err)
		return r.NewHTTPError(400, "解析参数错误")
	}
	glog.Info("健康管理卡权益查询：参数:", kit.JsonEncode(model))

	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}
	model.Criteria.UserId = memberInfo.ScrmUserId

	res := utils.EnsureCardsList(model)
	if !res.Success {
		return r.NewHTTPError(400, res.Message)
	}
	//过滤掉batchStatus!=1的数据
	/*result := []models.EnsureCardsListResult{}
	if len(res.Result) > 0 {
		for _, v := range res.Result {
			if v.BatchStatus == 1{
				result = append(result, v)
			}
		}
	}*/
	out := models.EnsureCardsList{
		BaseResponse: models.BaseResponse{
			Code:    200,
			Message: "ok",
		},
		Data: res.Result,
	}
	return c.JSON(200, out)

}

//@Router /order-api/health/get [get]
func GetMetadata(c echo.Context) error {
	//grpc调datacenter插入order数据
	request := oc.HealthPlanRefundOrderRequest{
		UserId:       "memberInfo.ScrmUserId",
		PetId:        "model.PetId",
		EnsureCode:   "model.EnsureCode",
		BatchCode:    "model.BatchCode",
		RefundAmount: 1,
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	glog.Info("健康管理-退卡：参数:", kit.JsonEncode(request))

	//md := metadata.Pairs("timestamp", "123123123123")
	//ctx := metadata.NewOutgoingContext(context.Background(), md)
	grpcRes, err := ocClient.ROC.HealthPlanRefundOrder(kit.SetTimeoutCtx(utils.AppendToOutgoingContextLoginUserInfo(context.Background(), c)), &request)
	if err != nil {
		glog.Error("调用RefundHealthPlanCard失败，", err, "，参数：", kit.JsonEncode(request))
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, grpcRes)
}
