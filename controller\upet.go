package controller

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"github.com/limitedlee/microservice/common/config"
	"github.com/spf13/cast"
	"order-api/models"
	"strings"
	"time"
)

//UPetOrderCheck 商城订单参数检测
func UPetOrderCheck(model *models.OrderSubmitRequest) (bool, string, error) {
	//实物
	if model.Order.IsVirtual == 0 {
		//检测邮寄地址
		if model.Order.AddressId == "" && model.Order.ReceiverAddress == "" {
			return false, "请选择或填写收件地址", nil
		}

		// 秒杀订单验证运费逻辑
		if model.Order.OrderType == 12 {
			if err := secKillCheckFreight(model); err != nil {
				return false, err.Error(), nil
			}
		}
	}
	return true, "", nil
}

// DecryptFreight 秒杀加密运费
type DecryptFreight struct {
	UserID      int32 `json:"user_id"`
	AddressID   int32 `json:"address_id"`
	Freight     int32 `json:"freight"`
	CanDelivery bool  `json:"can_delivery"`
	GoodsID     int32 `json:"goods_id"`
	Timestamp   int64 `json:"timestamp"`
}

// 秒杀运费解密验证
func secKillCheckFreight(in *models.OrderSubmitRequest) (err error) {
	if len(in.Order.SkFreightEncrypt) < 32 {
		return errors.New("运费信息缺少或者格式不正确")
	}

	fe := in.Order.SkFreightEncrypt

	hash := md5.New()
	hash.Write([]byte(fe[32:] + config.GetString("express_key")))
	sign := hex.EncodeToString(hash.Sum(nil))
	// 签名不正确
	if strings.ToUpper(sign) != strings.ToUpper(fe[0:32]) {
		return errors.New("运费信息校验失败")
	}

	freightBytes, err := base64.StdEncoding.DecodeString(fe[32:])
	if err != nil {
		return
	}
	df := new(DecryptFreight)
	if err = json.Unmarshal(freightBytes, df); err != nil {
		return
	}

	if time.Now().Add(-15*time.Minute).Unix() > df.Timestamp {
		return errors.New("运费信息失效，请刷新重试")
	}
	if !df.CanDelivery {
		return errors.New("商品配送未覆盖该地区")
	}
	if cast.ToString(df.AddressID) != in.Order.AddressId {
		return errors.New("运费信息校验失败，地址不匹配")
	}

	if len(in.OrderProducts) == 0 || in.OrderProducts[0].Sku != cast.ToString(df.GoodsID) {
		return errors.New("运费信息校验失败，商品信息不匹配")
	}

	in.Order.Freight = df.Freight // 运费

	return
}
