package controller

import (
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"github.com/tricobbler/echo-tool/validate"
	"net/http"
	"order-api/models"
	"order-api/proto/oc"
)

// @Summary 订阅消息推送
// @Tags 订阅消息
// @Accept plain
// @Produce json
// @Param openId query string true "OpenId"
// @Param orderSn query string true "订单号"
// @Param templateId query string true "模板ID"
// @Param pushType query int true "推送类型"
// @Param remarks query string false "备注"
// @Param refundId query string false "退款ID"
// @Param refundType query string false "退款类型"
// @Param refundAmount query string false "退款金额"
// @Param refundTime query string false "退款时间"
// @Param refundSn query string false "退款订单号"
// @Param status query string false "状态"
// @Param startTime query string false "开始时间"
// @Param endTime query string false "结束时间"
// @Param isVirtual query int false "是否虚拟订单"
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order-api/subscribe-message/push [post]
func PushTemplate(c echo.Context) error {
	type params struct {
		OpenId       string `json:"openId" form:"openId" query:"openId" validate:"required" label:"openId"`
		OrderSn      string `json:"orderSn" form:"orderSn" query:"orderSn" validate:"required" label:"orderSn"`
		TemplateId   string `json:"templateId" form:"templateId" query:"templateId" validate:"required" label:"templateId"`
		PushType     int32  `json:"pushType" form:"pushType" query:"pushType" validate:"required" label:"pushType"`
		Remarks      string `json:"remarks" form:"remarks" query:"remarks" label:"remarks"`
		RefundId     string `json:"refundId" form:"refundId" query:"refundId" label:"refundId"`
		RefundType   string `json:"refundType" form:"refundType" query:"refundType" label:"refundType"`
		RefundAmount string `json:"refundAmount" form:"refundAmount" query:"refundAmount" label:"refundAmount"`
		RefundTime   string `json:"refundTime" form:"refundTime" query:"refundTime" label:"refundTime"`
		RefundSn     string `json:"refundSn" form:"refundSn" query:"refundSn" label:"refundSn"`
		Status       string `json:"status" form:"status" query:"status" label:"status"`
		StartTime    string `json:"startTime" form:"startTime" query:"startTime" label:"startTime"`
		EndTime      string `json:"endTime" form:"endTime" query:"endTime" label:"endTime"`
		IsVirtual    int32  `json:"isVirtual" form:"isVirtual" query:"isVirtual" label:"isVirtual"`
	}

	var (
		out    oc.PushTemplateResponse
		rpcRes *oc.PushTemplateResponse //RPC请求返回结果
		err    error
	)
	//绑定参数
	request := new(params)
	if err := c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}

	if err = c.Validate(request); err != nil {
		err := validate.Translate(err.(validator.ValidationErrors))
		out.Message = "参数错误"
		out.Error = err.One()
		return c.JSON(http.StatusBadRequest, out)
	}

	rpcRequestParam := new(oc.PushTemplateRequest)
	rpcRequestParam.OpenId = request.OpenId
	rpcRequestParam.OrderSn = request.OrderSn
	rpcRequestParam.TemplateId = request.TemplateId
	rpcRequestParam.PushType = request.PushType
	rpcRequestParam.Remarks = request.Remarks

	refundSuccess := new(oc.RefundSuccess)
	refundSuccess.RefundId = request.RefundId
	refundSuccess.RefundType = request.RefundType
	refundSuccess.RefundAmount = request.RefundAmount
	refundSuccess.RefundTime = request.RefundTime
	rpcRequestParam.RefundSuccess = refundSuccess

	refundFail := new(oc.RefundFail)
	refundFail.RefundId = request.RefundId
	refundFail.RefundType = request.RefundType
	refundFail.RefundSn = request.RefundSn
	refundFail.Status = request.Status
	refundFail.RefundAmount = request.RefundAmount
	rpcRequestParam.RefundFail = refundFail

	refundStatus := new(oc.RefundStatus)
	refundStatus.RefundId = request.RefundId
	refundStatus.Status = request.Status
	refundStatus.RefundType = request.RefundType
	rpcRequestParam.RefundStatus = refundStatus

	if rpcRequestParam.PushType == 4 {
		preSalePay := new(oc.PreSalePay)
		preSalePay.StartTime = request.StartTime
		preSalePay.EndTime = request.EndTime
		preSalePay.Remarks = request.Remarks
		preSalePay.IsVirtual = request.IsVirtual
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	rpcRequestParam.OrgId = cast.ToInt32(orgId)

	client := GetOrderServiceClient()
	defer client.Close()
	if rpcRes, err = client.SM.PushTemplate(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("PushTemplate rpc error:", err)
		out.Message = "推送失败"
		out.Error = err.Error()
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("PushTemplate rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = http.StatusOK
	out.Message = "推送成功"

	return c.JSON(http.StatusOK, out)
}

// @Summary 推送订阅消息（多条件）
// @Tags 订阅消息
// @Accept plain
// @Produce json
// @Param models.NotifyRequest body oc.PushTemplateRequest true " "
// @Success 200 {object} models.BaseResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order-api/subscribe-message/send [post]
func SendSubscribeMessage(c echo.Context) error {
	out := new(models.BaseResponse)
	model := new(oc.PushTemplateRequest)
	if err := c.Bind(model); err != nil {
		out.Message = "参数解析失败"
		return c.JSON(http.StatusBadRequest, out)
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	model.OrgId = cast.ToInt32(orgId)

	client := GetOrderServiceClient()
	defer client.Close()
	rpcRes, err := client.SM.PushTemplate(client.Ctx, model)
	if err != nil {
		glog.Error("PushTemplate rpc error:", err)
		out.Message = "推送失败"
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("PushTemplate rpc fail:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = http.StatusOK
	out.Message = "推送成功"

	return c.JSON(http.StatusOK, out)
}
