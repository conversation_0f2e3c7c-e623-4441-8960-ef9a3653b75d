package controller

//商城活动相关处理
import (
	"errors"
	"order-api/models"
	"order-api/proto/ac"
	"order-api/utils"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
)

//活动检测结果
// V1.1.2.3.4
type ActivityCheckRes struct {
	CheckStockRes    bool //库存检测结果
	CheckActivityRes bool //活动检测结果
	CheckProductRes  bool //商品检测结果
}

//UPetActivity
//接口 里面顶对活动的所有方法  具体的活动类型去进行实现
type UPetActivity interface {
	GetActivityProduct() error                                     //获取商品
	GetActivity() error                                            //获取活动
	CheckActivity() (bool, string, error)                          //活动检测
	CheckProduct(*models.OrderSubmitRequest) (bool, string, error) //商品检测
	CheckStock() (bool, string, error)                             //库存检测
	CheckFail(*ActivityCheckRes)                                   //检测失败处理
}

//SecKill 秒杀结构体 用于处理秒杀相关的逻辑 当前之后查询价格 如有其他的逻辑 可以往里面增加参数
type SecKill struct {
	Sku         int64                               //订单sku
	PromotionId int32                               //活动id
	Number      int32                               //购买数量
	Product     *ac.GetSeckillProductDetailResponse //活动商品信息
	Activity    *ac.Promotion                       //活动信息
}

//GetActivityProduct 查询秒杀商品信息
func (sk *SecKill) GetActivityProduct() error {
	var (
		rpcRequestParam ac.GetSeckillProductDetailRequest   //rpc请求详情的参数
		rpcRes          *ac.GetSeckillProductDetailResponse //rpc获取详情的结果
		err             error
	)
	rpcRequestParam.SkuId = sk.Sku
	rpcRequestParam.PromotionId = sk.PromotionId

	glog.Info("GetSeckillProductDetail rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.SK.GetSeckillProductDetail(client.Ctx, &rpcRequestParam); err != nil {
		glog.Error("GetSeckillDetail rpc error:", err, kit.JsonEncode(rpcRequestParam))
		return err
	}
	sk.Product = rpcRes
	return nil
}

//GetActivity 查询秒杀活动信息
func (sk *SecKill) GetActivity() error {
	var (
		err    error
		rpcRes *ac.Promotion //rpc返回结果
	)
	rpcRequestParam := new(ac.GetSeckillDetailRequest)
	rpcRequestParam.Id = sk.PromotionId

	glog.Info("GetGroupBuyProductDetail rpc rpcRequestParam:", kit.JsonEncode(rpcRequestParam))
	client := ac.GetActivityCenterClient()
	if rpcRes, err = client.SK.GetSeckillDetail(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetSeckillDetail rpc error:", err, kit.JsonEncode(rpcRequestParam))
		return err
	}
	sk.Activity = rpcRes
	return nil
}

//CheckActivity 秒杀活动信息检测
func (sk *SecKill) CheckActivity() (bool, string, error) {
	err := sk.GetActivity()
	if err != nil {
		return false, "未获取到有效的秒杀活动信息", errors.New("获取秒杀活动信息失败" + err.Error())
	}
	if sk.Activity == nil {
		return false, "未获取到有效的秒杀活动信息", errors.New("获取秒杀活动信息失败")
	}
	if sk.Activity.Status != 2 {
		if sk.Activity.Status == 1 {
			return false, "秒杀活动未开始", nil
		}
		if sk.Activity.Status == 3 {
			return false, "秒杀活动已结束", nil
		}
		return false, "未获取到有效的秒杀活动信息", nil
	}

	if sk.Activity.SeckillOrderLimit > 0 && sk.Activity.SeckillOrderLimit < sk.Number {
		return false, "购买量超出了秒杀限购量", nil
	}

	return true, "", nil
}

//CheckProduct
//秒杀商品检测
//商品检测通过之后 需要对原请求数据的价格根据活动价格做处理
func (sk *SecKill) CheckProduct(model *models.OrderSubmitRequest) (bool, string, error) {
	err := sk.GetActivityProduct()
	if err != nil {
		return false, "未获取到秒杀活动商品信息", errors.New("获取秒杀活动商品信息失败" + err.Error())
	}
	if sk.Product == nil || sk.Product.Data == nil {
		return false, "未获取到秒杀活动商品信息", errors.New("未获取到秒杀活动商品信息")
	}
	//if sk.Product.Data.IsSelected == 0 {
	//	return false, "该活动商品已被删除", nil
	//}
	if sk.Product.Data.UpDownState == 0 {
		return false, "该秒杀活动商品已下架", nil
	}
	//对原请求数据的价格根据活动价格做处理

	//单价 = 秒杀价格
	model.OrderProducts[0].Price = sk.Product.Data.SeckillPrice
	//均摊后的实际支付价格 ，秒杀均摊后依然为秒杀价格
	model.OrderProducts[0].PayPrice = sk.Product.Data.SeckillPrice
	//商品金额（减了优惠） = 秒杀价格 * 商品个数
	model.Order.GoodsTotal = sk.Product.Data.SeckillPrice * model.OrderProducts[0].Number
	//付款金额（减了优惠） = 秒杀价格*商品个数
	model.Order.Total = sk.Product.Data.SeckillPrice*model.OrderProducts[0].Number + model.Order.Freight
	return true, "", nil
}

// CheckStock 检测秒杀活动的库存是否满足
//1:检测活动商品的虚拟库存是否满足，如果虚拟库存满足 则直接减扣虚拟库存，实际库存的判断放在order-center进行
//benchmark 有虚拟库存情况下23ms/op   无虚拟内存或者虚拟库存为9ms/op
//@version v2.9.10
func (sk *SecKill) CheckStock() (bool, string, error) {
	strSecKillId := cast.ToString(sk.PromotionId)
	strSku := cast.ToString(sk.Sku)
	//电商仓
	redisConn := utils.GetRedisConn()
	keys := []string{"stock:seckill:" + strSecKillId + ":" + strSku}

	scriptStr := `
	local number = tonumber(ARGV[1])
	local sec_kill_stock_key = KEYS[1]
	-- 判断虚拟库存
	local sec_kill_stock_value = tonumber(redis.call('GET',sec_kill_stock_key))
	if sec_kill_stock_value == nil then 
		return 4
	--售罄
	elseif (sec_kill_stock_value ==0) then
		return 3
	--不足
	elseif (sec_kill_stock_value < number) then
		return 2
	end
	-- 扣减虚拟库存
	redis.call('INCRBY',sec_kill_stock_key,-number)
	return 1
	`
	result := redisConn.Eval(scriptStr, keys, sk.Number)
	resultVal := result.Val()

	if resultVal == nil {
		return false, "活动库存查询失败", errors.New("秒杀活动库存查询失败，lua脚本有报错，错误信息：" + result.Err().Error())
	}
	res, ok := resultVal.(int64)
	if !ok {
		return false, "活动库存查询失败", errors.New("秒杀活动库存查询结果处理失败")
	}
	//不存在值 则可能是 redis自然删除 或者 手动删除 或者redis挂了
	//此时检测活动信息 如果足活动有效 则是redis问题 提示库存不  如果活动无效提示活动已结束
	if res == 4 {
		checkActivityRes, checkMsg, err := sk.CheckActivity()
		//如果活动 检测失败 则直接 返回活动的错误信息
		if err != nil || !checkActivityRes {
			return false, checkMsg, err
		} else { //活动时有效的 则提示库存不足
			return false, "秒杀商品库存不足", nil
		}
	}

	if res == 2 {
		return false, "秒杀商品库存不足", nil
	}
	if res == 3 {
		return false, "秒杀商品已售罄", nil
	}
	return true, "", nil
}

//CheckFail 秒杀检测失败处理：回滚虚拟库存
func (sk *SecKill) CheckFail(CheckRes *ActivityCheckRes) {
	//若果活动与商品检测失败 则释放活动库存 如果释放失败不会返回错误 没有意义
	if CheckRes.CheckActivityRes == false || CheckRes.CheckProductRes == false {
		FreedSecKillStock(cast.ToString(sk.Sku), sk.PromotionId, sk.Number)
	}
}

//UPetActivityCheck 活动检测
func UPetActivityCheck(model *models.OrderSubmitRequest) (bool, string, error) {
	var (
		activity         UPetActivity
		checkMsg         string
		err              error
		checkStockRes    = true
		checkActivityRes = true
		checkProductRes  = true
	)
	if model.Order.OrderType == 12 {
		activity = &SecKill{
			Sku:         cast.ToInt64(model.OrderProducts[0].Sku),
			PromotionId: model.OrderProducts[0].PromotionId,
			Number:      model.OrderProducts[0].Number,
		}
	} else {
		return true, "", nil
	}

	defer func() {
		//检测失败的处理
		//秒杀：回滚虚拟库存
		checkRes := &ActivityCheckRes{
			CheckStockRes:    checkStockRes,
			CheckActivityRes: checkActivityRes,
			CheckProductRes:  checkProductRes,
		}
		activity.CheckFail(checkRes)
	}()

	//库存判断
	checkStockRes, checkMsg, err = activity.CheckStock()
	if err != nil || !checkStockRes {
		return false, checkMsg, err
	}
	//活动判断
	checkActivityRes, checkMsg, err = activity.CheckActivity()
	if err != nil || !checkActivityRes {
		return false, checkMsg, err
	}
	//商品判断
	checkProductRes, checkMsg, err = activity.CheckProduct(model)
	if err != nil || !checkProductRes {
		return false, checkMsg, err
	}
	return true, "", nil

}
