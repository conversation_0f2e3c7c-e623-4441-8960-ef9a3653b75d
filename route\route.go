package route

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	echoSwagger "github.com/swaggo/echo-swagger"
	r "github.com/tricobbler/echo-tool/httpError"
	rpMiddleware "github.com/tricobbler/echo-tool/middleware"
	"github.com/tricobbler/echo-tool/validate"
	myMiddleware "order-api/middleware"
)

func InitRoute() *echo.Echo {
	e := echo.New()

	//添加swagger文档地址
	e.GET("/swagger/*", echoSwagger.WrapHandler)

	//错误中间件
	e.Use(rpMiddleware.MyRecover(middleware.RecoverConfig{
		Skipper:           middleware.DefaultSkipper,
		StackSize:         4 << 10, // 4 KB
		DisableStackAll:   false,
		DisablePrintStack: false,
	}))

	//解决跨域
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{echo.GET, echo.POST},
		AllowCredentials: true,
	}))

	//校验渠道id和来源，并写入context
	e.Use(myMiddleware.CheckChannel())

	//返回错误信息处理，屏蔽内部服务调用错误
	e.Use(rpMiddleware.MyErrorHandle())

	//设置自定义错误响应
	e.HTTPErrorHandler = r.HttpErrorHandler

	//自定义参数验证插件
	e.Validator = validate.NewCustomValidator()

	return Route(e)
}

//路由
func Route(e *echo.Echo) *echo.Echo {
	//添加项目名
	g := e.Group("order-api")
	//订单售后路由
	AftersaleRoute(g)
	//订单路由
	OrderRoute(g)
	//OMS路由
	OmsRoute(g)
	// 本地生活服务订单路由
	UpetDjRoute(g)

	// 售后单路由
	RefundOrderRoute(g)

	//C#版本的核销逻辑
	_g := e.Group("order")
	WriteOffOrderRoute(_g)

	//scrm用户路由
	UserRoute(g)

	//健康管理
	HealthRoute(g)

	//订阅消息
	SubscribeMessageRoute(g)

	// 发票
	InvoiceRoute(g)
	//数字藏品
	DigitalRoute(g)

	CardRoute(g)
	return e
}
