package controller

import (
	"order-api/proto/ac"
	"testing"
)

//v1.1.2.3.4.5.6
func TestSecKill_CheckStock(t *testing.T) {
	type fields struct {
		Sku         int64
		PromotionId int32
		Number      int32
		Product     *ac.GetSeckillProductDetailResponse
		Activity    *ac.Promotion
	}
	tests := []struct {
		name    string
		fields  fields
		want    bool
		want1   string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				Sku:         1000059001,
				PromotionId: 269,
				Number:      5,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sk := &SecKill{
				Sku:         tt.fields.Sku,
				PromotionId: tt.fields.PromotionId,
				Number:      tt.fields.Number,
				Product:     tt.fields.Product,
				Activity:    tt.fields.Activity,
			}
			got, got1, err := sk.CheckStock()
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckStock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckStock() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("CheckStock() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestSecKill_GetActivity(t *testing.T) {
	type fields struct {
		Sku         int64
		PromotionId int32
		Product     *ac.GetSeckillProductDetailResponse
		Activity    *ac.Promotion
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				PromotionId: 11,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sk := &SecKill{
				Sku:         tt.fields.Sku,
				PromotionId: tt.fields.PromotionId,
				Product:     tt.fields.Product,
				Activity:    tt.fields.Activity,
			}
			if err := sk.GetActivity(); (err != nil) != tt.wantErr {
				t.Errorf("GetActivity() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSecKill_GetActivityProduct(t *testing.T) {
	type fields struct {
		Sku         int64
		PromotionId int32
		Product     *ac.GetSeckillProductDetailResponse
		Activity    *ac.Promotion
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				Sku:         107463,
				PromotionId: 11,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sk := &SecKill{
				Sku:         tt.fields.Sku,
				PromotionId: tt.fields.PromotionId,
				Product:     tt.fields.Product,
				Activity:    tt.fields.Activity,
			}
			if err := sk.GetActivityProduct(); (err != nil) != tt.wantErr {
				t.Errorf("GetActivityProduct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
