package utils

import (
	"crypto/md5"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"net"
	"order-api/models"
	"sort"
	"strings"
)

func MD5(s string) string {
	data := []byte(s)
	has := md5.Sum(data)
	md5str := fmt.Sprintf("%x", has) //将[]byte转成16进制

	return md5str
}

func PaySign(data *models.StandardPayRequest, secret string) (string, error) {
	var (
		err  error
		sign string
		list = make(map[string]string)
	)
	list = map[string]string{
		"app_id":     cast.ToString(data.AppId),
		"notify_url": data.NotifyURL,
		"order_name": data.OrderName,
		"order_no":   data.OrderNo,
		"pay_amount": cast.ToString(data.PayAmount),
		"pay_total":  cast.ToString(data.PayTotal),
		"timestamp":  cast.ToString(data.Timestamp),
		"sign":       data.Sign,
	}
	// 签名
	if sign, err = MakeSignForStandardPay(list, secret); err != nil {
		return sign, err
	}
	return sign, nil
}

// 标注支付接口签名生成,payCenter服务中也有
func MakeSignForStandardPay(data map[string]string, secret string) (string, error) {
	// A、参数校验，app_id,timestamp,secret
	appId := data["app_id"]
	timestamp := data["timestamp"]
	if cast.ToInt(appId) < 1 || cast.ToInt(appId) > 4 { // 1：阿闻，2：子龙，3：R1，4：互联网
		return "", errors.New("app_id错误")
	}
	if len(timestamp) != 13 {
		return "", errors.New("时间戳错误")
	}

	//B、字典排序处理，定义一个slice，用来对key进行排序
	s := make([]string, len(data))
	for k := range data {
		s = append(s, k)
	}
	sort.Strings(s)

	//C、参数拼成字符串
	str := ""
	for _, v := range s {
		if data[v] == "" || v == "sign" {
			continue
		}
		if str != "" {
			str += "&"
		}
		str += v + "=" + data[v]
	}
	if str == "" {
		return "", errors.New("签名失败")
	}

	// D、进行加密
	str += "&secret=" + secret
	sign := strings.ToUpper(MD5(str))
	return sign, nil
}

//获取当前Ip
func GetClientIp() string {
	ipAddress := ""
	netInterfaces, err := net.Interfaces()
	if err != nil {
		return ipAddress
	}
	for i := 0; i < len(netInterfaces); i++ {
		if (netInterfaces[i].Flags & net.FlagUp) != 0 {
			address, _ := netInterfaces[i].Addrs()

			for _, address := range address {
				if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
					if ipNet.IP.To4() != nil {
						ipAddress = ipNet.IP.String()
						return ipAddress
					}
				}
			}
		}
	}
	return ipAddress

}
