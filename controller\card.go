package controller

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	"order-api/models"
	"order-api/proto/oc"
)

// @Summary 会员卡/服务包 开卡
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body oc.CardNewReq true " "
// @Success 200 {object} oc.CardNewRes
// @Failure 400 {object} oc.CardNewRes
// @Router /order-api/card/new [POST]
func CardNew(c echo.Context) error {
	req := new(oc.CardNewReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.CardNewRes{Code: 400, Message: err.Error()})
	}
	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	req.ScrmId = memberInfo.ScrmUserId
	req.UserName = memberInfo.ScrmUserName
	req.UserAgent = cast.ToInt32(c.Request().Header.Get("user_agent"))

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.New(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardNewRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 会员卡通过卡密开卡
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body oc.CardNewByCodeReq true " "
// @Success 200 {object} oc.CardBaseResponse
// @Failure 400 {object} oc.CardBaseResponse
// @Router /order-api/card/new-by-code [POST]
func CardNewByCode(c echo.Context) error {
	req := new(oc.CardNewByCodeReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	}
	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	req.ScrmId = memberInfo.ScrmUserId
	req.UserName = memberInfo.ScrmUserName
	req.UserAgent = cast.ToInt32(c.Request().Header.Get("user_agent"))

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.NewByCode(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 服务包激活
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body oc.CardServicePackActivityReq true " "
// @Success 200 {object} oc.CardBaseResponse
// @Failure 400 {object} oc.CardBaseResponse
// @Router /order-api/card/service-pack-activity [POST]
func CardServicePackActivity(c echo.Context) error {
	req := new(oc.CardServicePackActivityReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	}

	req.MemberId = cast.ToString(c.Get("scrm_id"))
	if req.MemberId == "" {
		return r.NewHTTPError(400, "用户不存在")
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.ServicePackActivity(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 权益领取
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model body oc.CardEquityReceiveReq true " "
// @Success 200 {object} oc.CardBaseResponse
// @Failure 400 {object} oc.CardBaseResponse
// @Router /order-api/card/equity-receive [POST]
func CardEquityReceive(c echo.Context) error {
	req := new(oc.CardEquityReceiveReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	}

	req.ScrmId = cast.ToString(c.Get("scrm_id"))
	if req.ScrmId == "" {
		return r.NewHTTPError(400, "用户不存在")
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.EquityReceive(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 查询签约ID
// @Tags 会员卡/服务包
// @Accept json
// @Produce json
// @Param model query oc.QuerySignIdReq true " "
// @Success 200 {object} oc.QuerySignIdRes
// @Failure 400 {object} oc.QuerySignIdRes
// @Router /order-api/card/query-sign-id [GET]
func CardQuerySignId(c echo.Context) error {
	req := &oc.QuerySignIdReq{
		OrderSn: c.QueryParam("order_sn"),
		ScrmId:  cast.ToString(c.Get("scrm_id")),
	}

	if req.ScrmId == "" {
		return r.NewHTTPError(400, "用户不存在")
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.Card.QuerySignId(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.CardBaseResponse{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
