package route

import (
	"github.com/labstack/echo/v4"
	"order-api/controller"
)

func HealthRoute(e *echo.Group) {
	g := e.Group("/health")

	g.POST("/company-category", controller.GetCompanyCategory)
	//健康管理订阅订单提交
	g.POST("/order-submit", controller.HealthSubOrder)
	//健康管理-退卡
	g.POST("/refund-card", controller.RefundHealthPlanCard)

	//健康管理-退款预算
	g.POST("/refund-amount", controller.RefundHealthAmount)

	//健康管理- 获取套餐信息接口（带项目详情）
	g.POST("/set-meal", controller.GetSetMeals)

	//健康管理- 卡权益查询
	g.POST("/cards-list", controller.EnsureCardsList)

	g.GET("/get", controller.GetMetadata)
}
