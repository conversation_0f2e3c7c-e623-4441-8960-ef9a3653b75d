package utils

import (
	"encoding/json"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	"order-api/models"
)

const (
	newHealthPlanCardUrl      = "/scrm-organization-api/ensureCards/mini/newHealthPlanCard"
	refundAmountUrl           = "/scrm-organization-api/ensureCards/mini/computeHealthPlanCardRefundAmount"
	getCompanyCategoryListUrl = "/scrm-organization-api/scrmcompany/ensurecards/category/outside/queryCompanyCategoryList"
	refundHealthPlanCard      = "/scrm-organization-api/ensureCards/mini/refundHealthPlanCard"
	ensureCardsList           = "/scrm-organization-api/ensureCards/batch/findList"
	getSetMealsUrl            = "/scrm-organization-api/ensureCards/categoryDetail/outside/findList"
	reNewHealthPlanCardUrl    = "/scrm-organization-api/ensureCards/mini/renewHealthPlanCard"
	getCardBatchCodeUrl       = "/scrm-organization-api/ensureCards/mini/queryHealthPlanCardRecordsPay"

	//卡类型 健康管理默认为7 所有环境多一样
	cardType = 7
	//健康管理来源
	source = 5
)

//开卡专用
func getPayType() int {
	payType := config.GetString("health-pay-type")
	return cast.ToInt(payType)
}

//健康订阅 开卡专用 机构编码
func getHealthHospitalCode() string {
	code := config.GetString("health-hospital-code")
	return code
}

func getScrmBaseUrl() string {
	baseUrl := config.GetString("bj-scrm-url")
	return baseUrl
}

//https://yapi.rp-field.com/project/101/interface/api/30253
//宠物健康计划 - 开卡
func NewHealthPlanCard(req models.NewHealthPlanCardReq) models.NewHealthPlanCardResponse {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + newHealthPlanCardUrl
	req.HospitalCode = getHealthHospitalCode()
	req.RecordsPays[0].PayType = getPayType()
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口NewHealthPlanCard", dataJson, err)
	}
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口NewHealthPlanCard", dataJson, err)
	}
	response := models.NewHealthPlanCardResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口NewHealthPlanCard", dataJson, err)
	}
	if !response.Success {
		glog.Error("调用SCRM接口NewHealthPlanCard", dataJson, response)
	}
	return response
}

//https://yapi.rp-field.com/project/101/interface/api/30289
//宠物健康计划 - 退款预算
func RefundAmount(req models.RefundAmountReq) models.RefundAmountResponse {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + refundAmountUrl
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口RefundAmount", err)
	}
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口RefundAmount", err)
	}
	response := models.RefundAmountResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口RefundAmount", err)
	}
	return response
}

//https://yapi.rp-field.com/project/101/interface/api/30298
//宠物健康计划 - 退卡
func RefundHealthPlanCard(req models.RefundPlanCardReq) models.RefundHealthPlanCardResponse {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + refundHealthPlanCard
	recordsPays := models.RecordsPay{
		PayType:  getPayType(),
		PayMoney: cast.ToFloat64(req.RefundAmount) / 100,
	}
	req.RecordsPays = append(req.RecordsPays, recordsPays)
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口RefundHealthPlanCard", err)
	}
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口RefundHealthPlanCard", err)
	}
	response := models.RefundHealthPlanCardResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口RefundHealthPlanCard", err)
	}
	return response
}

//宠物健康计划 - 卡权益查询
//https://yapi.rp-field.com/project/101/interface/api/16537
//新的地址 https://yapi.rp-field.com/project/101/interface/api/16528
func EnsureCardsList(req models.EnsureCardsListReq) models.EnsureCardsListResponse {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + ensureCardsList
	req.Criteria.CardType = cardType
	//req.Criteria.CompanyCode = "ZILONG"
	req.Source = source
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口EnsureCardsList", err)
	}
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口EnsureCardsList", err)
	}
	response := models.EnsureCardsListResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口EnsureCardsList", err)
	}
	return response
}

//查询套餐列表
//https://yapi.rp-field.com/project/137/interface/api/22153
func GetCompanyCategoryList(req models.GetCompanyCategoryListReq) (models.GetCompanyCategoryListResponse, error) {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + getCompanyCategoryListUrl
	req.Criteria.CardType = cardType
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口GetCompanyCategoryList", err)
		return models.GetCompanyCategoryListResponse{}, err
	}
	//header := "scrm-dataauthoritycodes|CX0004"
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口GetCompanyCategoryList", err)
		return models.GetCompanyCategoryListResponse{}, err
	}
	response := models.GetCompanyCategoryListResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口GetCompanyCategoryList", err)
		return models.GetCompanyCategoryListResponse{}, err
	}
	return response, nil
}

//查询健康管理订阅套餐信息（带详情）
//https://yapi.rp-field.com/project/137/interface/api/30343
func GetSetMeals(req models.GetSetMealsReq) (models.GetSetMealsResponse, error) {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + getSetMealsUrl
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口GetSetMeals", err)
		return models.GetSetMealsResponse{}, err
	}
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口GetSetMeals", err)
		return models.GetSetMealsResponse{}, err
	}
	response := models.GetSetMealsResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口GetSetMeals", err)
		return models.GetSetMealsResponse{}, err
	}
	return response, nil
}

//https://yapi.rp-field.com/mock/101/scrm-organization-api/ensureCards/mini/renewHealthPlanCard
//宠物健康计划 续卡
func ReNewHealthPlanCard(req models.ReNewHealthPlanCardReq) models.NewHealthPlanCardResponse {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + reNewHealthPlanCardUrl
	req.HospitalCode = getHealthHospitalCode()
	req.RecordsPays[0].PayType = getPayType()
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口ReNewHealthPlanCard", dataJson, err)
	}
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口ReNewHealthPlanCard", dataJson, err)
	}
	response := models.NewHealthPlanCardResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口ReNewHealthPlanCard", dataJson, err)
	}
	if !response.Success {
		glog.Error("调用SCRM接口ReNewHealthPlanCard", dataJson, response)
	}
	return response
}

//https://yapi.rp-field.com/project/101/interface/api/30748
//根据订单号获取 卡号跟 批次号
func GetCardBatchCode(req models.GetCardBatchCodeReq) models.GetCardBatchCodeResponse {
	baseUrl := getScrmBaseUrl()
	url := baseUrl + getCardBatchCodeUrl
	dataJson, err := json.Marshal(req)
	if err != nil {
		glog.Error("调用SCRM接口GetCardBatchCode", dataJson, err)
	}
	res, err := HttpPostToBJ(url, dataJson, "")
	if err != nil {
		glog.Error("调用SCRM接口GetCardBatchCode", dataJson, err)
	}
	response := models.GetCardBatchCodeResponse{}
	err = json.Unmarshal(res, &response)
	if err != nil {
		glog.Error("调用SCRM接口GetCardBatchCode", dataJson, err)
	}
	if !response.Success {
		glog.Error("调用SCRM接口GetCardBatchCode", dataJson, response)
	}
	return response
}
