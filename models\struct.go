package models

import (
	"encoding/json"
	"order-api/proto/oc"
)

type (
	BaseResponse struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	MemberInfo struct {
		Id             string `json:"id"`
		Name           string `json:"name"`
		CacheTime      int64  `json:"cache_time"`
		ScrmUserId     string `json:"scrm_user_id"`
		ScrmUserName   string `json:"scrm_user_name"`
		ScrmUserMobile string `json:"scrm_user_mobile"`
		OrgId          int    `json:"org_id"`
	}

	UserTokenResponse struct {
		BaseResponse
		Data *UserToken `json:"data"`
	}

	UserToken struct {
		Token string `json:"token"`
	}

	/*************************** aftersale begin ***************************/
	Product struct {
		OrderProductId      int64  `json:"order_product_id"`
		ProductId           int32  `json:"product_id"`
		ProductName         string `json:"product_name"`
		Number              int32  `json:"number"`        //购买数量
		RefundNumber        int32  `json:"refund_number"` //已退数量
		Image               string `json:"image"`
		Specs               string `json:"specs"`
		Price               string `json:"price"`     //商品单价
		PayPrice            string `json:"pay_price"` //实际支付单价（美团）
		SkuId               int32  `json:"sku_id"`
		ParentSkuId         string `json:"parent_sku_id"`         //父商品sku
		PromotionId         int32  `json:"promotion_id"`          //折扣活动id
		UsedNumber          int32  `json:"used_number"`           //虚拟商品已核销数量
		SurplusNumber       int32  `json:"surplus_number"`        //可退数量
		SurplusRefundAmount int32  `json:"surplus_refund_amount"` //可退金额

	}
	ApplyList struct {
		ShopId           string    `json:"shop_id"`            //店铺id
		ShopName         string    `json:"shop_name"`          //店铺名称
		OrderSn          string    `json:"order_sn"`           //订单编号
		Product          []Product `json:"product"`            //订单商品信息
		CanRefund        int32     `json:"can_refund"`         //能否退款，0否1是
		HasRefundRecord  int32     `json:"has_refund_record"`  //是否有退款记录，0否1是
		IsOutDate        int32     `json:"is_out_date"`        //是否超过售后时间，0否1是
		Total            string    `json:"total"`              //总金额（实际付款金额。加运费，加包装费，减优惠金额）
		GoodsTotal       string    `json:"goods_total"`        //商品总金额（未加运费，未加包装费，服务费,减优惠金额）
		Freight          string    `json:"freight"`            //总运费
		OrderStatusChild int32     `json:"order_status_child"` //子状态： 20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货; 20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
		IsVirtual        int32     `json:"is_virtual"`         //是否虚拟订单
	}
	ApplyListResponse struct {
		BaseResponse
		Data []*ApplyList `json:"data"`
	}

	RefundList struct {
		RefundSn    string    `json:"refund_sn"`    //退款编号
		OrderSn     string    `json:"order_sn"`     //订单编号
		Product     []Product `json:"product"`      //订单商品信息
		Status      int32     `json:"status"`       //售后状态:1等待商家审核 2已撤销 3退货退款成功 4等待买家退货 5买家已退货
		StatusText  string    `json:"status_text"`  //售后状态描述
		Refundtype  int32     `json:"refundtype"`   //申请类型: 1为退款,2为退货，默认为1
		CanRevoke   int32     `json:"can_revoke"`   //能否撤销，0否1是
		NeedExpress int32     `json:"need_express"` //是否需要快递，0否1是
	}
	RefundListResponse struct {
		BaseResponse
		Data []*RefundList `json:"data"`
	}

	RefundLog struct {
		Title string `json:"title"`
		Desc  string `json:"desc"`
		Time  string `json:"time"`
	}
	ReturnAddress struct {
		Name    string `json:"name"`
		Phone   string `json:"phone"`
		Address string `json:"address"`
	}
	RefundDetail struct {
		RefundAmount  string        `json:"refund_amount"`  //退款金额
		RefundAccount string        `json:"refund_account"` //退款账户
		ExpressName   string        `json:"express_name"`   //退款物流名称
		ExpressNo     string        `json:"express_no"`     //退款物流号
		ReturnAddress ReturnAddress `json:"return_address"` //退货地址
		Status        int32         `json:"status"`         //售后状态
		StatusText    string        `json:"status_text"`    //售后状态描述
		Refundtype    int32         `json:"refundtype"`     //售后类型: 1为退款,2为退货，默认为1
		CanRevoke     int32         `json:"can_revoke"`     //能否撤销，0否1是
		NeedExpress   int32         `json:"need_express"`   //是否需要快递，0否1是
		RemainingTime int32         `json:"remaining_time"` //剩余秒数
		RefundLogs    []RefundLog   `json:"refund_logs"`    //退款流程
		RefundProduct []Product     `json:"refund_product"` //退款信息
		RefundReason  string        `json:"refund_reason"`  //退款原因
		OrderSn       string        `json:"order_sn"`       //订单编号
	}
	RefundDetailResponse struct {
		BaseResponse
		Data *RefundDetail `json:"data"`
	}
	/*************************** aftersale end ***************************/
	/*************************** write-off-order start ***************************/
	WriteOffReq struct {
		VerifyCode string `json:"verify_code"`
		StoreId    string `json:"store_id"`
		Source     int32  `json:"source"`
		UserAgent  int32  `json:"user_agent"`
		MemberId   string `json:"member_id"`
	}
	OldOrderDetail struct {
		Petid             string             `json:"petid"`             //宠物id
		Isneedpost        bool               `json:"isneedpost"`        //是否需要邮寄
		Recipient         string             `json:"recipient"`         //收件人
		Address           string             `json:"address"`           //收件地址
		Mobile            string             `json:"mobile"`            //收件人联系电话
		Expressno         string             `json:"expressno"`         //物流单号
		Expresscode       string             `json:"expresscode"`       //物流公司编码
		Expressstate      int32              `json:"expressstate"`      //物流状态
		Cancelltime       string             `json:"cancelltime"`       //取消时间
		PayTime           string             `json:"paytime"`           //支付时间
		RefundTime        string             `json:"refundtime"`        //退款时间
		Platform          string             `json:"platform"`          //平台名称
		Membermobile      string             `json:"membermobile"`      //用户手机号
		Payway            string             `json:"payway"`            //支付方式
		Expressmoney      int32              `json:"expressmoney"`      //油费
		Ordertypedetail   int32              `json:"ordertypedetail"`   //订单类型小类1-255
		Orderid           string             `json:"orderid"`           //订单号
		Memberid          string             `json:"memberid"`          //用户id
		Ordermoney        string             `json:"ordermoney"`        //订单金额
		Ordertype         int32              `json:"ordertype"`         //订单类型
		Ordertypename     string             `json:"ordertypename"`     //订单类型名称
		Useragent         int32              `json:"useragent"`         //用户useragent
		Orderstate        int32              `json:"orderstate"`        //订单状态
		Orderchildenstate int32              `json:"orderchildenstate"` //订单子状态
		Orderdetail       string             `json:"orderdetail"`       //订单详情
		Platformid        int32              `json:"platformid"`        //平台ID
		Belonghospitalid  int32              `json:"belonghospitalid"`  //订单归属门店信息
		Createtime        string             `json:"createtime"`        //订单创建时间
		Lasttime          string             `json:"lasttime"`          //最后更新时间
		Sumquantity       int32              `json:"sumquantity"`       //
		Memberintegrals   []string           `json:"memberintegrals"`   //积分明细，暂时不要
		Isevaluate        int32              `json:"isevaluate"`        //是否有价值
		Ispostupet        int32              `json:"ispostupet"`        //是否推送优宠
		Isnotify          int32              `json:"isnotify"`          //是否通知标记
		ChannelId         int32              `json:"channel_id"`        //渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
		Goods             []OrderDetailGoods `json:"goods"`             //商品信息
	}
	OrderDetailGoods struct {
		Id                  string `json:"id"`                  //子订单号
		Orderid             string `json:"orderid"`             //订单号
		Sku                 string `json:"sku"`                 // skuid
		Goodsid             string `json:"goodsid"`             //商品货号
		Barcode             string `json:"barcode"`             //商品编码
		Goodsimage          string `json:"goodsimage"`          //商品图片地址
		Siglegoodsimage     string `json:"siglegoodsimage"`     //商品缩略图
		Name                string `json:"name"`                //商品名称
		Univalence          int32  `json:"univalence"`          //商品原价
		Sellprice           int32  `json:"sellprice"`           //商品售价
		MarkingPrice        int32  `json:"markingPrice"`        //商品原价
		Quantity            int32  `json:"quantity"`            //商品数量
		Unit                string `json:"unit"`                //商品单位
		Applyhospitalid     string `json:"applyhospitalid"`     //申请门店id
		Chargeoff           int32  `json:"chargeoff"`           //核销状态 -- 很重要 2-待核销，3-已核销，1-不需要核销，4-已过期
		Chargeoffcode       string `json:"chargeoffcode"`       //核销码
		Expiredate          string `json:"expiredate"`          //过期时间
		Chargeoffobject     string `json:"chargeoffobject"`     //核销对象
		Chargeoffobjectname string `json:"chargeoffobjectname"` //核销对象名称
		Lasttime            string `json:"lasttime"`            //最后更新时间
		Createtime          string `json:"createtime"`          //创建时间
		Chargeofftime       string `json:"chargeofftime"`       //核销时间
		Chargeoffhospitalid string `json:"chargeoffhospitalid"` //核销医院编号ID
		Chargeoffmemberid   string `json:"chargeoffmemberid"`   //核销人信息
		Isneedpush          int32  `json:"isneedpush"`          //是否推送
	}

	/*************************** write-off-order end ***************************/

	/*************************** bj  health begin ***************************/

	GetCompanyCategoryResponse struct {
		BaseResponse
		//总数
		Total int `json:"total"`
		//页码
		PageIndex int `json:"pageIndex"`
		//每页条数
		PageSize int `json:"pageSize"`
		//每页条数
		PageCount int `json:"pageCount"`

		Data *[]CompanyCategoryData `json:"data"`
	}

	RefundHealthAmountResponse struct {
		BaseResponse

		Data *RefundAmount `json:"data"`
	}

	GetSetMealsRes struct {
		BaseResponse
		Data *map[string][]SetMealData `json:"data"`
	}

	EnsureCardsList struct {
		BaseResponse
		Data []EnsureCardsListResult `json:"data"`
	}
	/*************************** bj  health end ***************************/

	/*************************** bj scrm api begin ***************************/
	NewHealthPlanCardReq struct {
		//用户id
		UserId string `json:"userId"`
		//宠物编码
		PetId string `json:"petId"`
		//机构编码
		HospitalCode string `json:"hospitalCode"`
		//套餐编码
		CategoryCode string `json:"categoryCode"`
		//订单编码
		OrderId string `json:"orderId"`
		//支付信息
		RecordsPays []RecordsPay `json:"recordsPays"`
	}

	RecordsPay struct {
		//支付方式
		PayType int `json:"payType"`
		//支付金额 (单位是元,带2位小数)
		PayMoney float64 `json:"payMoney"`
	}

	NewHealthPlanCard struct {
		//卡号
		EnsureCode string `json:"ensureCode"`
		//批次编码
		BatchCode string `json:"batchCode"`
	}

	NewHealthPlanCardResponse struct {
		//异常信息
		Message string `json:"message"`
		//未捕获系统异常
		SystemError string `json:"systemError"`
		//返回业务异常信息
		BusinessError string `json:"businessError"`
		//状态码:200正常
		StatusCode int `json:"statusCode"`
		//
		Result NewHealthPlanCard `json:"result"`
		//
		Success bool `json:"success"`
	}

	GetCompanyCategoryListReq struct {
		Criteria CompanyCategoryReq `json:"criteria"`
	}

	CompanyCategoryReq struct {
		//套餐编码
		CategoryCode string `json:"categoryCode"`
		//套餐类别
		CardType int `json:"cardType"`
		//适用医院
		//EppCode []string `json:"eppCode"`
		//页码
		PageIndex int `json:"pageIndex"`
		//每页条数
		PageSize int `json:"pageSize"`
		//宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)
		Kingof int `json:"kingof"`
		//适用年龄(1-幼年;2-成年;3-老年)
		ApplyAge int `json:"applyAge"`
		//支付方式(1-月付;2-季付;3-年付)
		PayType int `json:"payType"`
	}

	GetCompanyCategoryListResponse struct {
		//异常信息
		Message string `json:"message"`
		//未捕获系统异常
		SystemError string `json:"systemError"`
		//返回业务异常信息
		BusinessError string `json:"businessError"`
		//状态码:200正常
		StatusCode int `json:"statusCode"`
		//
		Result GetCompanyCategoryListData `json:"result"`
		//
		Success bool `json:"success"`
	}

	GetCompanyCategoryListData struct {
		Result []CompanyCategoryData `json:"result"`
		//总数
		Total int `json:"total"`
		//页码
		PageIndex int `json:"pageIndex"`
		//每页条数
		PageSize int `json:"pageSize"`
		//每页条数
		PageCount int `json:"pageCount"`
	}
	CompanyCategoryData struct {
		//id
		Id int `json:"id"`
		//套餐编码
		CategoryCode string `json:"categoryCode"`
		//套餐名称
		CategoryName string `json:"categoryName"`
		//套餐状态 （1-待售；2-在售；3-停售）
		CategoryStatus int `json:"categoryStatus"`
		//套餐状态说明
		CategoryStatusCaption string `json:"categoryStatusCaption"`
		//简拼
		CategoryAbbreviation string `json:"categoryAbbreviation"`
		//所属组织
		StructCode string `json:"structCode"`
		//所属组织名称
		StructName string `json:"structName"`
		//套餐在售期间（生效时间）
		EffectTime string `json:"effectTime"`
		//套餐在售期间（失效时间）
		LoseEffectTime string `json:"loseEffectTime"`
		//套餐类型
		CardType int `json:"cardType"`
		//宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)
		Kindof int `json:"kindof"`
		//适用年龄(1-幼年;2-成年;3-老年)
		ApplyAge int `json:"applyAge"`
		//套餐类型说明
		CardTypeCapiton string `json:"cardTypeCapiton"`
		//货号
		ItemNO string `json:"itemNO"`
		//价格类型 （1 一口价；2 折扣）
		PriceType int `json:"priceType"`
		//价格类型说明
		PriceTypeCapiton string `json:"priceTypeCapiton"`
		//折扣率
		DiscountRate float32 `json:"discountRate"`
		//金额
		CostMoney float32 `json:"costMoney"`
		//创建人
		CreateName string `json:"createName"`
		//创建时间
		CreateTime string `json:"createTime"`
		//原价
		OriginalPrice float32 `json:"originalPrice"`
		//是否支持续费
		Renewal int `json:"renewal"`
	}

	RefundAmountReq struct {
		//用户id
		UserId string `json:"userId"`
		//宠物编码
		PetId string `json:"petId"`
		//卡号
		EnsureCode string `json:"ensureCode""`
		//批次号
		BatchCode string `json:"batchCode"`
	}

	RefundAmount struct {
		//总金额
		CostAmount float64 `json:"costAmount"`
		//应退金额
		RefundAmount float64 `json:"refundAmount"`
		//已使用项目原价总和
		UsedOriginalAmount float64 `json:usedOriginalAmount`
	}

	RefundAmountResponse struct {
		//异常信息
		Message string `json:"message"`
		//未捕获系统异常
		SystemError string `json:"systemError"`
		//返回业务异常信息
		BusinessError string `json:"businessError"`
		//状态码:200正常
		StatusCode int `json:"statusCode"`
		//
		Result RefundAmount `json:"result"`
		//
		Success bool `json:"success"`
	}

	//健康管理-退卡
	RefundHealthPlanCardReq struct {
		//宠物编码
		PetId string `json:"petId"`
		//卡号
		EnsureCode string `json:"ensureCode"`
		//批次号
		BatchCode string `json:"batchCode"`
		//退款金额
		RefundAmount int64 `json:"refundAmount"`
	}

	//健康管理-退卡(组装请求参数)
	RefundPlanCardReq struct {
		//用户id
		UserId string `json:"userId"`
		//宠物编码
		PetId string `json:"petId"`
		//卡号
		EnsureCode string `json:"ensureCode"`
		//批次号
		BatchCode string `json:"batchCode"`
		//退款信息
		RecordsPays []RecordsPay `json:"recordsPays"`
		//订单号
		OrderId string `json:"orderId"`
		//退款金额
		RefundAmount float64 `json:"refundAmount"`
	}

	RefundHealthPlanCard struct {
		RmaNumber string `json:"rmaNumber"`
	}

	RefundHealthPlanCardResponse struct {
		//异常信息
		Message string `json:"message"`
		//未捕获系统异常
		SystemError string `json:"systemError"`
		//返回业务异常信息
		BusinessError string `json:"businessError"`
		//状态码:200正常
		StatusCode int `json:"statusCode"`
		//
		Result RefundHealthPlanCard `json:"result"`
		//
		Success bool `json:"success"`
	}

	EnsureCardsListReq struct {
		Criteria EnsureCardsListCriteria `json:"criteria"`
		//来源 0子龙 1小暖 2瑞鹏
		Source int `json:"source"`
	}

	EnsureCardsListCriteria struct {
		//HospitalCode string   `json:"hospitalCode"`
		UserId string `json:"userId"`
		//PetId        string   `json:"petId"`
		PetIds []string `json:"petIds"`
		//EnsureCode   string   `json:"ensureCode"`
		//BatchCode    string   `json:"batchCode"`
		//CategoryCode string   `json:"categoryCode"`
		//Status       int      `json:"status"`
		//CompanyCode string `json:"companyCode"`
		CardType int `json:"cardType"`
		//EppCode      string   `json:"eppCode"`
	}

	EnsureCardsListResult struct {
		Id int32 `json:"id"`
		//机构编码
		HospitalCode string `json:"hospitalCode"`
		//用户ID
		UserId string `json:"userId"`
		//宠物ID
		PetId string `json:"petId"`
		//用户列表
		UserIdLists []string `json:"userIdLists"`
		//宠物列表
		PetIdLists []string `json:"petIdLists"`
		//卡号
		EnsureCode string `json:"ensureCode"`
		//批次号
		BatchCode string `json:"batchCode"`
		//批次状态0：未生效 1：正常 2：过期 3：退卡
		BatchStatus int `json:"batchStatus"`
		//批次明细
		BatchDetails []EnsureCardsBatchDetails `json:"batchDetails"`
		//类别折扣
		CategoryDiscounts []EnsureCategoryDiscounts `json:"categoryDiscounts"`
		StartDate         string                    `json:"startDate"`
		ExpiryDate        string                    `json:"expiryDate"`
	}

	EnsureDiscountLevelVoList struct {
		ProductCode string `json:"productCode"`
		Type        int    `json:"type"`
	}

	EnsureCardsBatchDetails struct {
		Id int `json:"id"`
		//卡号
		EnsureCode string `json:"ensureCode"`
		//批次号
		BatchCode string `json:"batchCode"`
		//批次明细号
		BatchDetailCode string `json:"batchDetailCode"`
		//类别编码
		CategoryCode string `json:"categoryCode"`
		//类别明细编码
		CategoryDetailCode string `json:"categoryDetailCode"`
		//总价
		CostMoney float64 `json:"costMoney"`
		//总次数
		ChargeTimes int `json:"chargeTimes"`
		//余额
		CorpusBalance float64 `json:"corpusBalance"`
		//剩余次数
		LeftTimes int `json:"leftTimes"`
		//类别
		CategoryDetail EnsureCategoryDetail `json:"categoryDetail"`
		//预售包含的目录和产品
		DiscountLevelVoList []EnsureDiscountLevelVoList `json:"discountLevelVoList"`
	}

	EnsureCategoryDetail struct {
		Id int `json:"id"`
		//类别明细编码
		CategoryDetailCode string `json:"categoryDetailCode"`
		//类别编码
		CategoryCode string `json:"categoryCode"`
		//项目名称
		ProductName string `json:"productName"`
		//原价
		OriginalPrice float64 `json:"originalPrice"`
		//单价
		UnitPrice float64 `json:"unitPrice"`
		//金额
		CorpusMoney float64 `json:"corpusMoney"`
		//购买次数
		CorpusTimes        int    `json:"corpusTimes"`
		CreateId           int    `json:"createId"`
		CreateName         string `json:"createName"`
		CreateTime         string `json:"createTime"`
		LastUpdateUserId   int    `json:"lastUpdateUserId"`
		LastUpdateUserName string `json:"lastUpdateUserName"`
		LastUpdateTime     string `json:"lastUpdateTime"`
	}

	EnsureCategoryDiscounts struct {
		Id                  int                         `json:"id"`
		CategoryCode        string                      `json:"categoryCode"`
		DictId              string                      `json:"dictId"`
		ProductTypeName     string                      `json:"productTypeName"`
		Discount            string                      `json:"discount"`
		OperatorTime        string                      `json:"operatorTime"`
		OperatorId          int                         `json:"operatorId"`
		OperatorName        string                      `json:"operatorName"`
		DiscountLevelVoList []EnsureDiscountLevelVoList `json:"discountLevelVoList"`
	}

	EnsureCardsDiscountLevelVoList struct {
		ProductCode string `json:"productCode"`
		ProductName string `json:"productName"`
		Type        int    `json:"type"`
		Order       string `json:"order"`
	}

	EnsureCardsListResponse struct {
		Result        []EnsureCardsListResult `json:"result"`
		Message       string                  `json:"message"`
		SystemError   string                  `json:"systemError"`
		BusinessError string                  `json:"businessError"`
		StatusCode    int                     `json:"statusCode"`
		Success       bool                    `json:"success"`
	}

	GetSetMealsReq struct {
		Criteria SetMealReq `json:"criteria"`
	}

	SetMealReq struct {
		//适用年龄(1-幼年;2-成年;3-老年)
		ApplyAge int `json:"applyAge"`
		//宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)
		KindOf int `json:"kindOf"`
	}

	SetMealData struct {
		//套餐明细编码
		CategoryDetailCode string `json:"categoryDetailCode"`
		//套餐编码
		CategoryCode string `json:"categoryCode"`
		//项目名
		ProductName string `json:"productName"`
		//数量
		CorpusTimes int `json:"corpusTimes"`
	}
	GetSetMealsResponse struct {
		//异常信息
		Message string `json:"message"`
		//未捕获系统异常
		SystemError string `json:"systemError"`
		//返回业务异常信息
		BusinessError string `json:"businessError"`
		//状态码:200正常
		StatusCode int `json:"statusCode"`
		//
		Result map[string][]SetMealData `json:"result"`
		//
		Success bool `json:"success"`
	}

	ReNewHealthPlanCardReq struct {
		////用户id
		//UserId string `json:"userId"`
		////宠物编码
		//PetId string `json:"petId"`
		//机构编码
		HospitalCode string `json:"hospitalCode"`
		//套餐编码
		CategoryCode string `json:"categoryCode"`
		//订单编码
		OrderId string `json:"orderId"`
		//支付信息
		RecordsPays []RecordsPay `json:"recordsPays"`
		//卡号
		EnsureCode string `json:"ensureCode"`
	}

	GetCardBatchCodeReq struct {
		OrderIds []string `json:"orderIds"`
	}

	GetCardBatchCodeResponse struct {
		//异常信息
		Message string `json:"message"`
		//未捕获系统异常
		SystemError string `json:"systemError"`
		//返回业务异常信息
		BusinessError string `json:"businessError"`
		//状态码:200正常
		StatusCode int `json:"statusCode"`
		//
		Result []CardBatchCodeData `json:"result"`
		//
		Success bool `json:"success"`
	}

	CardBatchCodeData struct {
		//卡号
		EnsureCode string `json:"ensureCode"`
		//批次号
		BatchCode string `json:"batchCode"`
		//订单编码
		OrderId string `json:"orderId"`
		//支付时间
		OperatorTime string `json:"operatorTime"`
	}

	//根据第三方渠道订单号获取核销码
	GetVerifyCodeByOldOrderSnReq struct {
		//第三方渠道订单号
		OrderSn      string `json:"orderSn"`
		ProductName  string `json:"productName"`
		VerifyStatus int32  `json:"verifyStatus"`
		PageSize     int32  `json:"pageSize"`
		PageIndex    int32  `json:"pageIndex"`
	}

	GetNotVerifyCodeByMemberIdReq struct {
		//第三方渠道订单号
		PageSize  int32 `json:"pageSize"`
		PageIndex int32 `json:"pageIndex"`
	}

	GetVerifyCodeByOldOrderSnRes struct {
		//提示信息
		Message string `json:"message"`
		//满足查询条件的总数据条数
		Total int32 `json:"total"`
		//状态码:200正常 异常
		StatusCode int `json:"statusCode"`
		//核销码结果集
		Result []*VerifyCodes `json:"result"`
	}
	//核销码信息
	VerifyCodes struct {
		//所属阿闻订单号(子订单)
		OrderSn string `protobuf:"bytes,1,opt,name=order_sn,json=orderSn,proto3" json:"order_sn"`
		//核销码
		VerifyCode string `json:"verifyCode"`
		//核销码有效期
		VerifyCodeExpiryDate string `json:"verifyCodeExpiryDate"`
		//核销状态 0未核销, 1已核销, 2已退款 不传默认为未核销
		VerifyStatus int32 `json:"verifyStatus"`
		//核销门店的财务编码
		VerifyShop string `json:"verifyShop"`
		//核销时间
		VerifyTime string `json:"verifyTime"`
		//核销人的用户id（该订单是哪个会员核销的）
		VerifyMemberId string `json:"verifyMemberId"`
		//订单状态 已取消,10未付款,20已付款,30已完成
		OrderStatus int32 `json:"orderStatus"`
		//订单所属会员id（该订单是哪个会员买的）
		MemberId string `json:"memberId"`
		//订单下单时间
		CreateTime string `json:"createTime"`
		//订单支付时间
		PayTime string `json:"payTime"`
		//订单实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额)单位分
		Total int32 `json:"total"`
		//订单支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支
		PayMode int32 `json:"payMode"`
		//商品sku id
		SkuId string `json:"skuId"`
		//第三方的SkuId(货号)
		ThirdSkuId string `json:"thirdSkuId"`
		//商品均摊后实际支付单价
		PayPrice int32 `json:"payPrice"`
		//商品图片
		Image string `json:"image"`
		//商品名称
		ProductName string `json:"productName"`
		//商品购买数量
		Number int32 `json:"number"`
		//购买金额（单位分）
		PaymentTotal int32 `json:"paymentTotal"`
		//优惠金额（暂无数值）
		Privilege int32 `json:"privilege"`
		//市场价（单价 单位：分）
		MarkingPrice int32 `json:"markingPrice"`
	}

	/*************************** bj scrm api end ***************************/
)

func (s *MemberInfo) MarshalBinary() ([]byte, error) {
	return json.Marshal(s)
}

func (s *MemberInfo) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, s)
}

type OrderPrescribeReq struct {
	FinanceCode  string                     `json:"finance_code"`
	HospitalName string                     `json:"hospital_name"`
	PetWeight    string                     `json:"pet_weight"`
	OperateType  int                        `json:"operate_type"`
	Diagnose     []*oc.PrescriptionDiagnose `json:"diagnose"`
	PetInfo      *oc.ConsultMemberPetInfo   `json:"pet_info"`
	Skus         []*oc.OrderPrescribeSkuNum `json:"skus"`
}
