/*
http下单相关结构体：
OrderSubmitRequest：下单请求参数
OrderSubmitResponse：下单响应

Order：订单
OrderProductModel：订单商品
OrderPromotion：订单活动优惠
OrderPayInfo：订单支付信息
CannotSumbitProduct:无法下单商品
*/
package models

import (
	"github.com/spf13/cast"
	"order-api/proto/oc"
)

//下单请求参数
type OrderSubmitRequest struct {
	//订单
	Order *Order `json:"order"`
	//商品
	OrderProducts []*OrderProductModel `json:"order_products"`
	//参与优惠活动信息
	OrderPromotions []*OrderPromotion `json:"order_promotions"`
	//支付信息
	PayInfo *OrderPayInfo `json:"pay_info"`
	// 药品仓属性
	WarehouseType int32 `json:"warehouse_type"`
}

//下单响应参数
type OrderSubmitResponse struct {
	//状态码
	Code int32 `json:"code"`
	//消息
	Message string `json:"message"`
	//错误信息
	Error string `json:"error"`
	//无法下单商品
	CannotProducts []*CannotSumbitProduct `json:"cannot_products"`
	//提交订单成功后，返回订单ID
	OrderSn string `json:"order_sn"`
	//提交订单成功后的支付单号
	PaySn string `json:"pay_sn"`
	//提交订单成功后的订单id
	OrderId int32 `json:"order_id"`
	//创建订单时间
	CreateTime int64 `json:"create_time"`
	//在线问诊订单医生Code，快速咨询为空
	DoctorCode string `json:"doctor_code"`
}

//商品
type Order struct {
	//外部订单号
	OldOrderSn string `json:"old_order_sn"`
	//商户或门店id
	ShopId string `json:"shop_id"` //
	//渠道ID：1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店,7百度,8H5,9医疗互联网
	ChannelId int32 `json:"channel_id"`
	//商户名称
	ShopName string `json:"shop_name"`
	//收件人
	ReceiverName string `json:"receiver_name"`
	//收件省
	ReceiverState string `json:"receiver_state"`
	//收件市
	ReceiverCity string `json:"receiver_city"`
	//收件区
	ReceiverDistrict string `json:"receiver_district"`
	//收件地址
	ReceiverAddress string `json:"receiver_address"`
	//收件电话
	ReceiverPhone string `json:"receiver_phone"`
	//总优惠金额
	Privilege int32 `json:"privilege"`
	//总金额（付款金额，加上运费，加包装费 减优惠金额）
	Total int32 `json:"total"`
	//商品总金额（未加运费，不加包装费，减优惠金额，美团不减优惠金额）
	GoodsTotal int32 `json:"goods_total"`
	//总运费
	Freight int32 `json:"freight"`
	//发票信息
	Invoice string `json:"invoice"`
	//买家留言
	BuyerMemo string `json:"buyer_memo"`
	//收货地址纬度
	Latitude float64 `json:"latitude"`
	//收货地址经度
	Longitude float64 `json:"longitude"`
	//总重量(非必填)
	TotalWeight int32 `json:"total_weight"`
	//预计送达时间
	ExpectedTime string `json:"expected_time"`
	//订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划订单,7保险订单,8积分订单 9秒杀订单 , 13在线问诊，15社区团购
	OrderType int32 `json:"order_type"`
	//是否是虚拟订单，0否1是
	IsVirtual int32 `json:"is_virtual"`
	//是否有拆单，0否1是
	IsSplit int32 `json:"is_split"`
	//助力订单id（电商使用）
	PowerId int32 `json:"power_id"`
	//渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它 houduanzuo
	UserAgent int32 `json:"user_agent"`
	//设备当前时间（竖屏排查问题用）
	DeviceCurrentTime string `json:"device_current_time"`
	//交易类型(01：前置仓门店订单，02 非置仓门店订单，03：非分销订单，04：分销订单05：团单订单 06：预售套餐订单 07:健康订阅订单 08：保障卡会员卡订单 09：在线问诊订单)

	OrderPayType string `json:"order_pay_type"`
	// v2.9.10 添加 1分享连接，2扫码 3 自己扫码自己 5 自主访问下单  商城分销用 原商城订单dis_type字段
	DisType int32 `json:"dis_type"`
	// v2.9.10 添加 希望送货时间 商城下单用 原商城订单receiver_date_msg字段
	ReceiverDateMsg string `json:"receiver_date_msg"`
	// v2.9.10 添加 收件人座机 原商城订单tel_phone字段
	TelPhone string `json:"tel_phone"`
	// v2.9.10 添加 1 小程序(阿闻智慧门店) 2:阿闻宠物(北京那边用) //3阿闻商城(自用) 商城用 原商城虚拟订单的source字段
	Source int32 `json:"source"`
	// v2.9.10 添加 原商城虚拟订单first_order字段
	FirstOrder string `json:"first_order"`
	// v2.9.10 添加 小城原商城虚拟订单的open_id字段
	OpenId string `json:"open_id"`
	// v2.9.10 添加 分销id 原商城虚拟订单的dis_id字段
	DisId string `json:"dis_id"`
	// v2.9.10 添加 用户选择的收件以 原商城虚拟订单的address_id字段
	AddressId string `json:"address_id"`
	// 社区团购站点
	PickupStationId int32 `json:"pickup_station_id"`
	//医疗互联网订单号\处方ID\推荐ID
	ConsultOrderSn string `json:"consult_order_sn"`
	// 秒杀运费信息
	SkFreightEncrypt string `json:"sk_freight_encrypt"`
	// 扩展字段
	ExtraInfo struct {
		// 团购人名称
		GroupName string `json:"group_name"`
		// 团购人手机号
		GroupMobile string `json:"group_mobile"`
		// 团购人的地址
		GroupAddress string `json:"group_address"`
		// 参团昵称
		NickName string `json:"nick_name"`
		// 参团头像完整地址
		AvatarUrl string `json:"avatar_url"`
		// 店铺分销员id
		DisMemberId string `json:"dis_member_id"`
		// 分销店铺
		DisShopId string `json:"dis_shop_id"`
		// 店铺分销员id来源 0默认 1
		DisMemberFrom int32 `json:"dis_member_from"`
	}
	//组织ID
	OrgId int32 `json:"org_id" xorm:"default 'null' comment('机构ID') INT(11) 'org_id'"`
}

//商品
type OrderProductModel struct {
	//sku
	Sku string `json:"sku"`
	//商品类型1-实物商品，2-虚拟商品，3-组合商品
	ProductType int32 `json:"product_type"`
	//组合商品父级sku
	ParentSku string `json:"parent_sku"`
	//商品id
	ProductId string `json:"product_id"`
	//商品名称
	ProductName string `json:"product_name"`
	//商品编码
	BarCode string `json:"bar_code"`
	//单价
	Price int32 `json:"price"`
	//数量
	Number int32 `json:"number"`
	//商品图片
	Image string `json:"image"`
	//参与限时折扣的商品数量
	DiscountCount int32 `json:"discount_count"`
	//促销活动Id
	PromotionId int32 `json:"promotion_id"`
	//折扣后的单价
	DiscountPrice int32 `json:"discount_price"`
	//仅vip折扣价格，用于超出限购会员原价
	VipPrice int32 `json:"vip_price"`
	//活动类型1-满减商品2限时折扣3-满减运费 11 秒杀
	PromotionType int32 `json:"promotion_type"`
	//仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
	Source int32 `json:"source"`
	//组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟
	CombineType int32 `json:"combine_type"`
	//只有虚拟商品才有值1-有效期至多少2-有效期天数
	TermType int32 `json:"term_type"`
	//如果expire_type=1存时间戳,如果term_type=2存多少天
	TermValue int32 `json:"term_value"`
	//是否支持过期退款 1：是  0：否
	VirtualInvalidRefund int32 `json:"virtual_invalid_refund"`
	//是否是第三方商品信息 1：是  0：否 默认0
	IsThirdProduct int32 `json:"is_third_product"`
	//组合商品折扣类型(1-按折扣优惠，2-按固定价格优惠)
	GroupDiscountType int32 `json:"group_discount_type"`
	//组合商品折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）
	GroupDiscountValue int32 `json:"group_discount_value"`
	//电商对应的商品id(电商使用)
	RecId int32 `json:"rec_id"`
	//规格
	Specs string `json:"specs"`
	//子商品列表
	ChildProductList []*OrderProductModel `json:"child_product_list"`
	//商品均摊后实际支付单价
	PayPrice int32 `json:"pay_price"`
	//sku实付总金额，discount_price*number
	PaymentTotal int32 `json:"payment_total"`
	// 药品仓属性
	WarehouseType int32 `json:"warehouse_type"`
	// 是否使用了虚拟库存
	UseVirtualStock int32 `json:"use_virtual_stock"`
}

//活动优惠
type OrderPromotion struct {
	//主键Id
	Id int64 `json:"id"`
	//促销活动Id
	PromotionId int32 `json:"promotion_id"`
	//活动类型 活动类型 1 满减活动 2 限时折扣 3 满减运费 11秒杀
	PromotionType int32 `json:"promotion_type"`
	//促销活动优惠
	PromotionTitle string `json:"promotion_title"`
	//活动名称
	PromotionName string `json:"promotion_name"`
	//活动优惠金额
	PromotionFee int32 `json:"promotion_fee"`
}

//支付信息
type OrderPayInfo struct {
}

//无法下单的商品
type CannotSumbitProduct struct {
	//商品skuid
	SkuId string `json:"sku_id"`
	//1:无货 2:失效 3:下架
	Status int32 `json:"status"`
}

//下单的用户
type LoginUser struct {
	//用户id
	MemberId string `json:"product_id"`
	//电话
	MemberTel string `json:"product_name"`
	//用户名
	MemberName string `json:"bar_code"`
}

type DiagnoseData struct {
	//宠物id
	PetId string `json:"pet_id" validate:"required" label:"宠物id"`
	//医生编号
	DoctorCode string `json:"doctor_code"  label:"医生编号"`
	//医生类别
	DoctorType int32 `json:"doctor_type"  label:"医生类别：1门店医生，2互联网医生"`
	//问诊项目：1-免费义诊，2-快速咨询，3-找医生
	DiagnoseProject int32 `json:"diagnose_project" validate:"required,numeric,min=1" label:"问诊项目"`
	//问诊形式：1-图文，2-电话，3-视频
	DiagnoseForm int32 `json:"diagnose_form" validate:"required,numeric,min=1" label:"问诊形式"`
	//免疫情况:1已免疫，2未免疫，3免疫不全，4免疫不详
	ImmuneStatus int32 `json:"immune_status" validate:"required,numeric,min=1" label:"免疫情况"`
	//症状：0其他，1呕吐，2软便拉稀，3皮肤问题，4眼睛问题，5泌尿问题，6绝育，7疫苗，8驱虫，9养护问题,多个用英文逗号隔开（【其他】选项与具体症状关键词二选一）
	Symptom string `json:"symptom"  label:"症状"`
	//补充症状(选择【其他】关键词，输入框描述症状必填)
	SymptomDesc string `json:"symptom_desc"  label:"补充症状"`
	//症状出现时间：1-小于7天，2-小于1个月，3-小于3个月，4-3个月以上
	SymptomRecent int32 `json:"symptom_recent" validate:"required,numeric,min=1" label:"症状出现时间"`
	//宠物症状照片，多个用英文逗号隔开
	Image string `json:"image" label:"宠物症状照片"`
	//是否就诊过：0未就诊，1就诊过
	HaveHospital int32 `json:"have_hospital" validate:"numeric,min=0" label:"是否就诊过"`
	//就诊过的医院名称
	HaveHospitalName string `json:"have_hospital_name"  label:"就诊过的医院名称"`
	//医生诊断结果与治疗方案
	HaveHospitalResult string `json:"have_hospital_result"  label:"医生诊断结果与治疗方案"`
	//历史就诊的检查照片/药品照片
	HaveHospitalImage string `json:"have_hospital_image"  label:"历史就诊的检查照片/药品照片"`
	//是否有其他病史：0否，1是
	MedicalHistory int32 `json:"medical_history" validate:"numeric,min=0" label:"是否有其他病史"`
	//其他病史信息
	MedicalHistoryInfo string `json:"medical_history_info"  label:"其他病史信息"`
	//宠物头像
	PetAvatar string `json:"pet_avatar"  label:"宠物头像"`
	//宠物名称
	PetName string `json:"pet_name"  label:"宠物名称"`
	//宠物种类大分类
	PetKindof string `json:"pet_kindof"  label:"宠物种类大分类"`
	//宠物种类
	PetVariety string `json:"pet_variety"  label:"宠物种类"`
	//宠物生日
	PetBirthday string `json:"pet_birthday"  label:"宠物生日"`
	//宠物性别 性别：1GG,2MM
	PetSex int32 `json:"pet_sex"  label:"宠物性别 性别：1GG,2MM"`
	//1：已绝育 0：未绝育
	PetNeutering int32 `json:"pet_neutering"  label:"1：已绝育 0：未绝育"`
	//金额
	Amount int32 `json:"amount"  label:"金额"`
	//问诊时长
	Duration int32 `json:"duration"  label:"问诊时长"`
}

//TransToProtoDataMall
//将商城入参OrderPromotion数据转换为请求订单中心的活动数据
func (o *OrderPromotion) TransToProtoDataMall() *oc.OrderPromotionModel {
	var promotion = new(oc.OrderPromotionModel)
	promotion.PromotionId = o.PromotionId
	promotion.PromotionType = o.PromotionType
	promotion.PromotionTitle = o.PromotionTitle
	promotion.PromotionFee = o.PromotionFee
	return promotion
}

//TransToProtoData
//将到家订单入参OrderPromotion数据转换为请求订单中心的活动数据
func (o *OrderPromotion) TransToProtoData() *oc.OrderPromotionModel {
	var promotion = new(oc.OrderPromotionModel)
	promotion.PromotionId = o.PromotionId
	promotion.PromotionType = o.PromotionType
	promotion.PromotionTitle = o.PromotionTitle
	promotion.PromotionFee = o.PromotionFee

	promotion.PoiCharge = o.PromotionFee
	promotion.PtCharge = 0

	return promotion
}

//TransToProtoDataMall
//将商城订单入参Order数据转换为请求订单中心的订单数据
func (o *Order) TransToProtoDataMall() *oc.MtAddOrderRequest {

	addOrder := &oc.MtAddOrderRequest{
		OrderStatus:      10,
		OrderStatusChild: 20101,
		LogisticsCode:    "0000",

		ShopId:       o.ShopId,
		ShopName:     o.ShopName,
		BuyerMemo:    o.BuyerMemo,
		DeliveryType: o.OrderType,
		Freight:      o.Freight,
		Total:        o.Total,
		GoodsTotal:   o.GoodsTotal,
		Latitude:     o.Latitude,
		Longitude:    o.Longitude,

		OrderType:        o.OrderType,
		Privilege:        o.Privilege,
		ReceiverName:     o.ReceiverName,
		ReceiverPhone:    o.ReceiverPhone,
		ReceiverMobile:   o.ReceiverPhone,
		ReceiverState:    o.ReceiverState,
		ReceiverCity:     o.ReceiverCity,
		ReceiverDistrict: o.ReceiverDistrict,
		ReceiverAddress:  o.ReceiverAddress,

		Invoice:      o.Invoice,
		ExpectedTime: o.ExpectedTime,
		TotalWeight:  o.TotalWeight, //前端商品总金额已减去优惠金额
		OrderSn:      o.OldOrderSn,  //
		PowerId:      o.PowerId,
		IsVirtual:    o.IsVirtual,
		//v2.9.10 添加的字段
		ReceiverDateMsg: o.ReceiverDateMsg,
		TelPhone:        o.TelPhone,
		Source:          o.Source,
		OpenId:          o.OpenId,
		FirstOrder:      cast.ToInt32(o.FirstOrder),
		DisType:         cast.ToInt32(o.DisType),
		DisId:           cast.ToInt32(o.DisId),
		AddressId:       cast.ToInt32(o.AddressId),
	}
	if o.OrderType == 20 {
		addOrder.DeliveryType = 1
	}
	return addOrder
}

//TransToProtoData
//将到家订单入参OrderProductModel转换成 请求订单中心的入参oc.OrderProductModel
func (o *Order) TransToProtoData() *oc.MtAddOrderRequest {
	addOrder := &oc.MtAddOrderRequest{
		DeliveryType:     2,
		OrderStatus:      10,
		OrderStatusChild: 20101,
		LogisticsCode:    "0000",

		ShopId:           o.ShopId,
		ShopName:         o.ShopName,
		BuyerMemo:        o.BuyerMemo,
		Freight:          o.Freight,
		GoodsTotal:       o.GoodsTotal + o.Privilege,
		Latitude:         o.Latitude,
		Longitude:        o.Longitude,
		OrderType:        o.OrderType,
		Privilege:        o.Privilege, //前端给过来的只包含满减的优惠，不包括满减运费和限时折扣活动的优惠，但是下单的时候是要加上
		ReceiverName:     o.ReceiverName,
		ReceiverPhone:    o.ReceiverPhone,
		ReceiverMobile:   o.ReceiverPhone,
		ReceiverState:    o.ReceiverState,
		ReceiverCity:     o.ReceiverCity,
		ReceiverDistrict: o.ReceiverDistrict,
		ReceiverAddress:  o.ReceiverAddress,

		Invoice:        o.Invoice,
		ExpectedTime:   o.ExpectedTime,
		TotalWeight:    o.TotalWeight, //前端商品总金额已减去优惠金额
		ConsultOrderSn: o.ConsultOrderSn,
		DisId:          cast.ToInt32(o.DisId), //电商分销到本地生活
		DisType:        cast.ToInt32(o.DisType),
	}

	if o.OrderType == 3 {
		addOrder.DeliveryType = 3
	}

	return addOrder

}

//TransToProtoDataMall
//将商城订单入参OrderProductModel转换成 请求订单中心的入参oc.OrderProductModel
//只适用于非 子商品
func (o *OrderProductModel) TransToProtoDataMall() *oc.OrderProductModel {
	var product = new(oc.OrderProductModel)
	product.Sku = o.Sku
	product.ProductId = o.ProductId
	product.ProductName = o.ProductName
	product.BarCode = o.BarCode
	product.Specs = o.Specs
	product.ProductType = o.ProductType
	product.CombineType = o.CombineType
	product.Image = o.Image

	product.Number = o.Number

	product.Price = o.Price
	product.PayPrice = o.Price
	product.MarkingPrice = o.Price
	product.SkuPayTotal = o.Price * o.Number
	product.PaymentTotal = o.Price * o.Number

	product.TermType = o.TermType
	product.TermValue = o.TermValue
	product.MallOrderProductId = int64(o.RecId)
	product.VirtualInvalidRefund = o.VirtualInvalidRefund

	product.PromotionId = o.PromotionId
	product.PromotionType = o.PromotionType
	product.WarehouseType = o.WarehouseType
	//待确定 之前赠品该字段为0 非正品获取传的值
	product.IsThirdProduct = o.IsThirdProduct
	product.UseVirtualStock = o.UseVirtualStock

	return product
}

//TransToProtoData
//将到家订单入参OrderProductModel转换成 请求订单中心的入参oc.OrderProductModel
func (o *OrderProductModel) TransToProtoData() *oc.OrderProductModel {
	var product = new(oc.OrderProductModel)
	product.Sku = o.Sku
	product.ProductId = o.ProductId
	product.ProductName = o.ProductName
	product.ProductType = o.ProductType
	product.CombineType = o.CombineType
	product.Specs = o.Specs
	product.Image = o.Image
	product.BarCode = o.BarCode

	product.Number = o.Number

	product.Price = o.Price
	product.VipPrice = o.VipPrice
	product.PayPrice = o.Price
	product.MarkingPrice = o.Price
	product.PaymentTotal = o.DiscountPrice * o.Number
	product.PrivilegeTotal = o.Price * o.Number

	product.PromotionId = o.PromotionId
	product.PromotionType = o.PromotionType
	product.TermType = o.TermType
	product.TermValue = o.TermValue
	product.VirtualInvalidRefund = o.VirtualInvalidRefund
	return product
}

//TransToProtoDataMallChild
//将商城订单入参OrderProductModel中的子商品转换成 请求订单中心的入参oc.OrderProductModel
//只适用于子商品
func (o *OrderProductModel) TransToProtoDataMallChild(parent *OrderProductModel) *oc.OrderProductModel {
	var product = new(oc.OrderProductModel)

	product.Sku = o.Sku
	product.ProductId = o.ProductId
	product.ProductName = o.ProductName
	product.BarCode = o.BarCode
	product.Specs = o.Specs
	product.ProductType = o.ProductType
	product.CombineType = 0
	product.Image = o.Image
	product.GroupItemNum = o.Number

	product.ParentSkuId = parent.Sku
	product.Number = parent.Number * o.Number
	product.DeliverNum = parent.Number * o.Number

	product.Price = o.Price
	product.PayPrice = o.Price
	product.MarkingPrice = o.Price
	product.PaymentTotal = o.Price * parent.Number * o.Number
	product.SkuPayTotal = o.Price * parent.Number * o.Number

	product.TermType = o.TermType
	product.TermValue = o.TermValue
	product.MallOrderProductId = int64(parent.RecId) //组合商品子商品也是用这个
	product.VirtualInvalidRefund = parent.VirtualInvalidRefund

	product.PromotionId = parent.PromotionId
	product.PromotionType = parent.PromotionType
	product.WarehouseType = parent.WarehouseType

	return product
}

//TransToProtoDataChild
//将到家订单入参OrderProductModel中的子商品转换成 请求订单中心的入参oc.OrderProductModel
//只适用于子商品
func (o *OrderProductModel) TransToProtoDataChild(parent *oc.OrderProductModel) *oc.OrderProductModel {
	combineChildNumber := parent.Number * o.Number
	combineChildProduct := &oc.OrderProductModel{
		Sku:          o.Sku,
		ProductId:    o.ProductId,
		ParentSkuId:  parent.Sku,
		ProductName:  o.ProductName,
		ProductType:  o.ProductType,
		BarCode:      o.BarCode,
		Specs:        o.Specs,
		Image:        o.Image,
		GroupItemNum: o.Number,

		Price:        o.Price, //子商品（虚拟商品和子实物商品不查快照，前端传过来；因为虚拟商品不需要上架，子商品也可用不需要上架，取渠道数据，符合重量，价格，库存条件即可售卖组合商品）
		PayPrice:     o.Price,
		MarkingPrice: o.Price,
		PaymentTotal: combineChildNumber * o.Price,

		Number:               combineChildNumber, //组合商品下子商品数量
		TermType:             o.TermType,
		TermValue:            o.TermValue,
		PromotionId:          parent.PromotionId,
		PromotionType:        parent.PromotionType,
		VirtualInvalidRefund: o.VirtualInvalidRefund,
	}
	return combineChildProduct

}
