package utils

import (
	"order-api/models"
	"reflect"
	"testing"
)

func TestNewHealthPlanCard(t *testing.T) {
	type args struct {
		req models.NewHealthPlanCardReq
	}
	tests := []struct {
		name string
		args args
		want models.NewHealthPlanCardResponse
	}{
		{name: "TestNewHealthPlanCard"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			req := models.NewHealthPlanCardReq{
				UserId:       "e9a3352b5ce14fa291d8177cc04adb66",
				PetId:        "bc3524f1f9174022b255731d8152c316",
				HospitalCode: "ZLH0002",
				CategoryCode: "CM20210310000003",
				OrderId:      "1615540185714577",
			}
			req.RecordsPays = append(req.RecordsPays, models.RecordsPay{
				PayType:  1,
				PayMoney: 0.01,
			})

			if got := NewHealthPlanCard(req); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("NewHealthPlanCard() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRefundAmount(t *testing.T) {
	type args struct {
		req models.RefundAmountReq
	}
	tests := []struct {
		name string
		args args
		want models.RefundAmountResponse
	}{
		{name: "TestRefundAmount"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := models.RefundAmountReq{
				UserId:     "f6a939c4bbf545d1994889db5a2a1f12",
				PetId:      "f89ddfd23f514a2dbf1666d813bbb9b6",
				EnsureCode: "f89ddfd23f514a2dbf1666d813bbb9b6",
				BatchCode:  "f89ddfd23f514a2dbf1666d813bbb9b6",
			}

			if got := RefundAmount(req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RefundAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetCompanyCategoryList(t *testing.T) {
	type args struct {
		req models.GetCompanyCategoryListReq
	}
	tests := []struct {
		name    string
		args    args
		want    models.GetCompanyCategoryListResponse
		wantErr bool
	}{
		{name: "TestGetCompanyCategoryList", args: args{req: models.GetCompanyCategoryListReq{Criteria: models.CompanyCategoryReq{
			CardType:     7,
			PageIndex:    1,
			PageSize:     10,
			CategoryCode: "CM20201218000001",
			//EppCode: []string{
			//	"RPX0001",
			//},
			Kingof:   1000,
			ApplyAge: 1,
			PayType:  3,
		}}}}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetCompanyCategoryList(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCompanyCategoryList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCompanyCategoryList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetSetMeals(t *testing.T) {
	type args struct {
		req models.GetSetMealsReq
	}
	tests := []struct {
		name string
		args args
	}{
		{args: args{req: models.GetSetMealsReq{Criteria: models.SetMealReq{
			ApplyAge: 2,
			KindOf:   1000,
		}}}}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			GetSetMeals(tt.args.req)
		})
	}
}

func TestEnsureCardsList(t *testing.T) {
	type args struct {
		req models.EnsureCardsListReq
	}
	tests := []struct {
		name string
		args args
		want models.EnsureCardsListResponse
	}{
		{name: "TestEnsureCardsList"}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.args.req = models.EnsureCardsListReq{
				Criteria: models.EnsureCardsListCriteria{
					UserId: "bbdd4d8021ed430ead39153c7f6b28d5",
					//PetId:    "f93d59370a3d45a389bdb352e67efe16",
					PetIds: []string{
						"51ea02c68bee4a8c8e9abf933d9abe0f",
						"5070b20e02cc426fac4cdd4cf193ed90",
						"26ddcb46c21c42a39f0f41596bbce94e",
						"4c01c1612584472085c876be3979ee2a",
						"820f3860cadd4185b25b82ed20a11d58",
						"44a6d8f198ef44c0887da3494b00be41",
						"4fda02a8c5a44aad8d836b0cf7b7e544",
						"4b29a71906c64f6aa5098834dca110f6",
						"4fcc61817dfc437e9ba5eef88a09e4e0",
						"3479638098be4a34a66b7ab5cb73b10a",
						"1825a7ffb4824936a377d4fc3a8fb569",
						"946d54f8099a4cc396676afcad8891b3",
						"f092e3a1a3b7478ab24b457c7b8556b1",
						"8231cdb01b44487a918a765c4d4f4134",
						"4284870a1d9e41bf86df7c92cbaab977",
						"b78488772eb9426b96262d3aedb87fa8",
						"2302a7d0cb8a4ff0ae64078c61bfecff",
						"9d8208d6bd1e4b139d88660087a05e26",
						"b5a3894a5a71437ab1713872b8a9bef4",
						"51ea8d1f35f94021b5d6e6d7e8e3ecf3",
						"7ce960cc09544e78901a5be9dab4782d",
					},
					//EppCode:  "YC0001",
				},
			}

			if got := EnsureCardsList(tt.args.req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EnsureCardsList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReNewHealthPlanCard(t *testing.T) {
	type args struct {
		req models.NewHealthPlanCardReq
	}
	tests := []struct {
		name string
		args args
		want models.NewHealthPlanCardResponse
	}{
		{name: ""}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := models.ReNewHealthPlanCardReq{
				HospitalCode: "ZLH0002",
				CategoryCode: "CM20210310000003",
				OrderId:      "1615564598203334",
				EnsureCode:   "HP20210312000020",
			}
			req.RecordsPays = append(req.RecordsPays, models.RecordsPay{
				PayType:  1,
				PayMoney: 0.01,
			})

			if got := ReNewHealthPlanCard(req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReNewHealthPlanCard() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetCardBatchCode(t *testing.T) {
	type args struct {
		req models.GetCardBatchCodeReq
	}
	tests := []struct {
		name string
		args args
		want models.GetCardBatchCodeResponse
	}{
		{name: "", args: args{req: models.GetCardBatchCodeReq{
			OrderIds: []string{
				"1615789275860550",
			},
		}}}, // TODO: Add test cases
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetCardBatchCode(tt.args.req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCardBatchCode() = %v, want %v", got, tt.want)
			}
		})
	}
}
