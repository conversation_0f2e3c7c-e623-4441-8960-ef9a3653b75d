package controller

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	"order-api/models"
	"order-api/proto/oc"
	"order-api/utils"
	"strings"
)

// @Summary 发票抬头列表
// @Tags 发票抬头
// @Accept json
// @Produce json
// @Param InvoiceTitleList body oc.InvoiceTitleListRequest true " "
// @Success 200 {object} oc.InvoiceTitleListResponse
// @Failure 400 {object} oc.InvoiceTitleListResponse
// @Router /order-api/invoice-title/list [post]
func InvoiceTitleList(c echo.Context) error {
	req := new(oc.InvoiceTitleListRequest)
	if err := c.Bind(req); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	memberInfo, _ := c.Get("member_info").(*models.MemberInfo)
	if memberInfo != nil {
		req.ScrmId = memberInfo.ScrmUserId
	}

	if req.ScrmId == "" {
		return r.NewHTTPError(400, "获取用户信息失败")
	}

	client := GetOrderServiceClient()
	defer client.Close()
	resp, err := client.IV.InvoiceTitleList(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}

// @Summary 编辑发票抬头
// @Tags 发票抬头
// @Accept json
// @Produce json
// @Param InvoiceTitleEdit body oc.InvoiceTitleEditRequest true " "
// @Success 200 {object} oc.InvoiceTitleEditResponse
// @Failure 400 {object} oc.InvoiceTitleEditResponse
// @Router /order-api/invoice-title/add [post]
func InvoiceTitleEdit(c echo.Context) error {
	req := new(oc.InvoiceTitleEditRequest)
	if err := c.Bind(req); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	memberInfo, _ := c.Get("member_info").(*models.MemberInfo)
	if memberInfo != nil {
		req.ScrmId = memberInfo.ScrmUserId
	}

	if req.ScrmId == "" {
		return r.NewHTTPError(400, "获取用户信息失败")
	}

	client := GetOrderServiceClient()
	defer client.Close()
	resp, err := client.IV.InvoiceTitleEdit(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}

// @Summary 添加票抬头
// @Tags 发票抬头
// @Accept json
// @Produce json
// @Param InvoiceTitleAdd body oc.InvoiceTitleAddRequest true " "
// @Success 200 {object} oc.InvoiceTitleAddResponse
// @Failure 400 {object} oc.InvoiceTitleAddResponse
// @Router /order-api/invoice-title/add [post]
func InvoiceTitleAdd(c echo.Context) error {
	req := new(oc.InvoiceTitleAddRequest)
	if err := c.Bind(req); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	memberInfo, _ := c.Get("member_info").(*models.MemberInfo)
	if memberInfo != nil {
		req.ScrmId = memberInfo.ScrmUserId
	}

	if req.ScrmId == "" {
		return r.NewHTTPError(400, "获取用户信息失败")
	}

	client := GetOrderServiceClient()
	defer client.Close()
	resp, err := client.IV.InvoiceTitleAdd(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}

// @Summary 模糊查询公司
// @Tags 开票
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键字"
// @Success 200 {object} oc.QueryInvoiceCompanyResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order-api/invoice/companies [get]
func QueryInvoiceCompany(c echo.Context) error {
	keyword := c.QueryParam("keyword")
	if strings.Trim(keyword, " ") == "" {
		return r.NewHTTPError(400, "关键字不能为空")
	}

	client := GetOrderServiceClient()
	defer client.Close()

	resp, err := client.IV.QueryInvoiceCompany(client.Ctx, &oc.QueryInvoiceCompanyRequest{
		Keyword: keyword,
	})
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, resp)
}

// @Summary 通过编码查询企业信息
// @Tags 开票
// @Accept json
// @Produce json
// @Param code query string true "企业编码"
// @Success 200 {object} oc.InvoiceCompanyInfoResponse
// @Failure 400 {object} oc.InvoiceCompanyInfoResponse
// @Router /order-api/invoice/company-info [get]
func InvoiceCompanyInfo(c echo.Context) error {
	code := c.QueryParam("code")
	if strings.Trim(code, " ") == "" {
		return r.NewHTTPError(400, "企业编码不能为空")
	}

	client := GetOrderServiceClient()
	defer client.Close()

	resp, err := client.IV.InvoiceCompanyInfo(client.Ctx, &oc.InvoiceCompanyInfoRequest{
		Code: code,
	})
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, resp)
}

// @Summary 发票申请
// @Tags 开票
// @Accept json
// @Produce json
// @Param InvoiceApply body oc.CreateInvoiceRequest true " "
// @Success 200 {object} oc.InvoiceResponse
// @Failure 400 {object} oc.InvoiceResponse
// @Router /order-api/invoice/apply [post]
func InvoiceApply(c echo.Context) error {
	req := new(oc.CreateInvoiceRequest)
	if err := c.Bind(req); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if utils.IsEmail(req.Email) == false {
		return r.NewHTTPError(400, "邮箱格式错误")
	}
	memberInfo, _ := c.Get("member_info").(*models.MemberInfo)
	if memberInfo != nil {
		req.ScrmId = memberInfo.ScrmUserId
	}

	client := GetOrderServiceClient()
	defer client.Close()
	resp, err := client.IV.InvoiceApply(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}

// @Summary 开票状态
// @Tags 开票
// @Accept json
// @Produce json
// @Param order_sn query string true "订单号"
// @Success 200 {object} oc.InvoiceStatusResponse
// @Failure 400 {object} oc.InvoiceStatusResponse
// @Router /order-api/invoice/status [get]
func InvoiceStatus(c echo.Context) error {
	orderSn := c.QueryParam("order_sn")
	if strings.Trim(orderSn, " ") == "" {
		return r.NewHTTPError(400, "订单信息不能为空")
	}
	req := &oc.InvoiceStatusRequest{
		OrderSn: cast.ToString(orderSn),
	}
	memberInfo, _ := c.Get("member_info").(*models.MemberInfo)
	if memberInfo != nil {
		req.ScrmId = memberInfo.ScrmUserId
	}

	client := GetOrderServiceClient()
	defer client.Close()
	resp, err := client.IV.InvoiceStatus(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}

// @Summary 发票详情
// @Tags 开票
// @Accept json
// @Produce json
// @Param order_sn query string true "订单号"
// @Success 200 {object} oc.InvoiceDetailResponse
// @Failure 400 {object} oc.InvoiceDetailResponse
// @Router /order-api/invoice/detail [get]
func InvoiceDetail(c echo.Context) error {
	orderSn := c.QueryParam("order_sn")
	if strings.Trim(orderSn, " ") == "" {
		return r.NewHTTPError(400, "订单信息不能为空")
	}
	req := &oc.InvoiceDetailRequest{
		OrderSn: cast.ToString(orderSn),
	}
	memberInfo, _ := c.Get("member_info").(*models.MemberInfo)
	if memberInfo != nil {
		req.ScrmId = memberInfo.ScrmUserId
	}

	client := GetOrderServiceClient()
	defer client.Close()

	resp, err := client.IV.InvoiceDetail(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}

// @Summary 发送邮件
// @Tags 开票
// @Accept json
// @Produce json
// @Param InvoiceSendEmail body oc.InvoiceSendEmailRequest true " "
// @Success 200 {object} oc.InvoiceResponse
// @Failure 400 {object} oc.InvoiceResponse
// @Router /order-api/invoice/send-email [post]
func InvoiceSendEmail(c echo.Context) error {
	req := new(oc.InvoiceSendEmailRequest)
	if err := c.Bind(req); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if len(req.OrderSn) == 0 || utils.IsEmail(req.Email) == false {
		return r.NewHTTPError(400, "参错错误")
	}

	memberInfo, _ := c.Get("member_info").(*models.MemberInfo)
	if memberInfo != nil {
		req.ScrmId = memberInfo.ScrmUserId
	}

	client := GetOrderServiceClient()
	defer client.Close()

	resp, err := client.IV.InvoiceSendEmail(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}

// @Summary 发票退款，红冲
// @Tags 开票
// @Accept json
// @Produce json
// @Param InvoiceSendEmail body oc.RefundInvoiceRequest true " "
// @Success 200 {object} oc.InvoiceResponse
// @Failure 400 {object} oc.InvoiceResponse
// @Router /order-api/invoice/refund [post]
func RefundInvoice(c echo.Context) error {
	req := new(oc.RefundInvoiceRequest)
	if err := c.Bind(req); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	client := GetOrderServiceClient()
	defer client.Close()

	resp, err := client.IV.RefundInvoice(client.Ctx, req)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, resp)
}
