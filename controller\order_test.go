package controller

import (
	"order-api/models"
	"order-api/proto/oc"
	"testing"
)

//v1
func TestAddReturnOrderToTencent(t *testing.T) {
	type args struct {
		orderSn string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "TestAddReturnOrderToTencent",
			args: args{orderSn: "1615552703991507"},
		}, // TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			AddReturnOrderToTencent("4000000001701325")
		})
	}
}

func TestAddOrdersToTencent(t *testing.T) {
	type args struct {
		model              *models.OrderSubmitRequest
		mtAddOrderResponse *oc.MtAddOrderResponse
		openId             string
		createTime         int64
	}
	tests := []struct {
		name string
		args args
	}{
		{name: "推送腾讯有数",
			args: args{
				model:              nil,
				mtAddOrderResponse: nil,
				openId:             "oK0F85Quvq4n3UZ1K4zDjg6ko8T8",
				createTime:         1630895872000,
			}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
		})
	}
}
