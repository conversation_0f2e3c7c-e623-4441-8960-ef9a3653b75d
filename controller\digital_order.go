package controller

import (
	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"order-api/models"
	"order-api/proto/oc"
)

//DigitalOrderNotify 数字藏品支付回调
func DigitalOrderNotify(c echo.Context) error {
	model := new(models.NotifyRequest)
	if err := c.Bind(model); err != nil {
		glog.Info("DigitalOrderNotify 数字藏品订单支付回调:获取返回参数失败" + err.Error())
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}
	glog.Info("DigitalOrderNotify 数字藏品订单支付回调:" + kit.JsonEncode(model))
	//更改支付状态
	ocClient := oc.GetOrderServiceClient()
	defer ocClient.Close()
	in := new(oc.PayDigitalOrderNotifyRequest)
	in.OrderSn = model.OrderId
	in.PaySn = model.TradeNo
	in.PayAmount = cast.ToFloat32(model.PayPrice)
	in.Status = 1 //更改为支付成功
	out, err := ocClient.DOrder.PayDigitalOrderNotify(ocClient.Ctx, in)
	if err != nil {
		glog.Errorf("DigitalOrderNotify 数字藏品订单支付回调:更改订单状态失败,error(%s),参数(%s)", err.Error(), kit.JsonEncode(in))
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}
	if out.Code != 200 {
		glog.Errorf("DigitalOrderNotify 数字藏品订单支付回调:更改订单状态失败,error(%s),参数(%s)", out.Message, kit.JsonEncode(in))
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}
	return c.JSON(200, models.NotifyResponse{Result: "success"})
}

//OrderTestPay 测试支付
func OrderTestPay(c echo.Context) error {
	maps := make(map[string]string)
	if err := c.Bind(&maps); err != nil {
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}
	ocClient := oc.GetOrderServiceClient()
	defer ocClient.Close()
	in := new(oc.OrderPayTestRequest)
	in.Openid = maps["openid"]
	out, err := ocClient.DOrder.OrderPayTest(ocClient.Ctx, in)
	if err != nil {
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}
	if out.Code != 200 {
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}
	return c.JSON(200, out)
}
