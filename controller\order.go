package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"order-api/dto"
	"order-api/models"
	"order-api/proto/ap"
	"order-api/proto/dac"
	"order-api/proto/ext"
	"order-api/proto/mk"
	"order-api/proto/oc"
	"order-api/utils"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	"github.com/tricobbler/echo-tool/validate"
	kit "github.com/tricobbler/rp-kit"
)

// AwenOfflineNotify
// @Summary 订单支付成功回调
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.NotifyRequest body models.NotifyRequest true " "
// @Success 200 {object} models.NotifyResponse
// @Failure 400 {object} models.NotifyResponse
// @Router /order-api/order/offlinenotify [post]
func AwenOfflineNotify(c echo.Context) error {
	model := new(models.NotifyRequest)
	if err := c.Bind(model); err != nil {
		glog.Info("订单支付回调:获取返回参数失败" + err.Error())
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}

	glog.Info("订单支付回调:" + kit.JsonEncode(model))

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	payMode := GetPayModelByPayType(model.PayType)
	in := &oc.OrderPayNotifyRequest{
		OrderSn:   model.OrderId,
		PaySn:     strings.Replace(model.TradeNo, "-", "", -1),
		PayTime:   model.PayTime,
		PayMode:   payMode,
		PayAmount: cast.ToInt32(model.PayPrice),
	}
	res, err := ocClient.RPC.OrderPayNotify(ocClient.Ctx, in)
	if err != nil {
		glog.Error("订单支付回调:更改订单状态失败，", err, "，参数：", kit.JsonEncode(in))
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}
	if res.Code != 200 {
		return c.JSON(200, models.NotifyResponse{Result: "fail"})
	}

	req := &oc.GetHealthOrderReq{
		OrderSn: model.OutTradeNo,
	}
	orderRes, err := ocClient.RPC.GetHealthOrder(kit.SetTimeoutCtx(context.Background()), req)
	if err != nil {
		glog.Error("调用GetHealthOrder失败，", err, "，参数：", kit.JsonEncode(req))
		return r.NewHTTPError(400, err.Error())
	}
	if orderRes.Code != 200 {
		return r.NewHTTPError(400, orderRes.Message)
	}
	if orderRes.OrderDetail != nil {
		//阿闻健康管理订单支付回掉需要调用开卡接口
		if orderRes.OrderDetail.OrderType == 6 {

			if len(orderRes.OrderDetail.EnsureCode) > 0 {
				renewCard := models.ReNewHealthPlanCardReq{
					//HospitalCode: "",
					CategoryCode: orderRes.OrderDetail.CategoryCode,
					OrderId:      model.OutTradeNo,
					EnsureCode:   orderRes.OrderDetail.EnsureCode,
				}
				recordsPay := models.RecordsPay{
					PayMoney: cast.ToFloat64(model.PayPrice) / 100,
					//PayType:  1,
				}
				renewCard.RecordsPays = append(renewCard.RecordsPays, recordsPay)
				res := utils.ReNewHealthPlanCard(renewCard)
				if !res.Success {
					glog.Error("调用续卡接口失败:NewHealthPlanCard", renewCard, res)
					return r.NewHTTPError(400, err.Error())
				}

				createReq := oc.UpdateOrderMealRequest{
					OrderSn:    model.OutTradeNo,
					EnsureCode: res.Result.EnsureCode,
					BatchCode:  res.Result.BatchCode,
				}
				_, err = ocClient.Cart.UpdateOrderMeal(kit.SetTimeoutCtx(context.Background()), &createReq)
				if err != nil {
					glog.Error("调用UpdateOrderMeal失败，", err, "，参数：", kit.JsonEncode(req))
					return r.NewHTTPError(400, err.Error())
				}

				return c.JSON(200, models.NotifyResponse{Result: "success"})
			}

			newCard := models.NewHealthPlanCardReq{
				UserId: orderRes.OrderDetail.ScrmUserId,
				PetId:  orderRes.OrderDetail.ScrmPetId,
				//HospitalCode: utils.HealthHospitalCode,
				CategoryCode: orderRes.OrderDetail.CategoryCode,
				OrderId:      model.OutTradeNo,
			}
			recordsPay := models.RecordsPay{
				PayMoney: cast.ToFloat64(model.PayPrice) / 100,
				//PayType:  1,
			}
			newCard.RecordsPays = append(newCard.RecordsPays, recordsPay)
			res := utils.NewHealthPlanCard(newCard)
			if !res.Success {
				glog.Error("调用开卡接口失败:NewHealthPlanCard", newCard, res)
			}

			createReq := oc.UpdateOrderMealRequest{
				OrderSn:    model.OutTradeNo,
				EnsureCode: res.Result.EnsureCode,
				BatchCode:  res.Result.BatchCode,
			}
			_, err = ocClient.Cart.UpdateOrderMeal(kit.SetTimeoutCtx(context.Background()), &createReq)
			if err != nil {
				glog.Error("调用UpdateOrderMeal失败，", err, "，参数：", kit.JsonEncode(req))
				return r.NewHTTPError(400, err.Error())
			}
		}
	}

	return c.JSON(200, models.NotifyResponse{Result: "success"})
}

// @Summary 电银订单支付成功回调
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.NotifyRequest body models.DYPayNotifyRequest true " "
// @Success 200 {object} models.PayNotifyResponse
// @Failure 400 {object} models.PayNotifyResponse
// @Router /order-api/order/paynotify [post]
func DYPayNotify(c echo.Context) error {
	model := new(models.DYPayNotifyRequest)
	if err := c.Bind(model); err != nil {
		glog.Error("订单支付回调:获取返回参数失败", err)
		return r.NewHTTPError(400, "失败")
		//return c.JSON(200, models.PayNotifyResponse{Code: 400, Message: "失败"})
	}

	glog.Info("订单支付回调:" + kit.JsonEncode(model))

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.OrderPayNotifyRequest{
		OrderSn:   model.OrderNo,
		PaySn:     model.TradeNo,
		PayTime:   model.PayTime,
		PayMode:   2,
		PayAmount: model.PayAmount,
	}
	res, err := ocClient.RPC.OrderPayNotify(ocClient.Ctx, in)
	if err != nil {
		glog.Error("订单支付回调:更改订单状态失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, "失败")
		//return c.JSON(200, models.PayNotifyResponse{Code: 400, Message: "失败"})
	}

	if res.Code == 200 && res.Message != "" {
		return r.NewHTTPError(400, "失败")
		//return c.JSON(200, models.PayNotifyResponse{Code: 400, Message: "失败"})
	}

	return c.JSON(200, models.PayNotifyResponse{Code: 200, Message: "成功"})
}

// @Summary 拼团订单支付成功回调
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.NotifyRequest body models.NotifyRequest true " "
// @Success 200 {object} models.PayNotifyResponse
// @Failure 400 {object} models.PayNotifyResponse
// @Router /order-api/order/pinpaynotify [post]
func PinPayNotify(c echo.Context) error {
	model := new(models.NotifyRequest)
	if err := c.Bind(model); err != nil {
		glog.Error("拼团订单支付回调:获取返回参数失败", err)
		return r.NewHTTPError(400, "失败")
		//return c.JSON(200, models.PayNotifyResponse{Code: 400, Message: "失败"})
	}

	glog.Info("拼团订单支付回调:" + kit.JsonEncode(model))

	ocClient := ap.GetPinGroupServiceClient()

	payMode := GetPayModelByPayType(model.PayType)
	in := &ap.OrderPayNotifyRequest{
		OrderSn:   model.OrderId,
		PaySn:     model.TradeNo,
		PayTime:   model.PayTime,
		PayMode:   payMode,
		PayAmount: cast.ToInt32(model.PayPrice),
	}
	res, err := ocClient.RPC.PinOrderPayNotify(ocClient.Ctx, in)
	if err != nil {
		glog.Error("拼团订单支付回调:更改订单状态失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, "失败")
		//return c.JSON(200, models.PayNotifyResponse{Code: 400, Message: "失败"})
	}

	if res.Code == 200 && res.Message != "" {
		return r.NewHTTPError(400, "失败")
		//return c.JSON(200, models.PayNotifyResponse{Code: 400, Message: "失败"})
	}

	return c.JSON(200, models.NotifyResponse{Result: "success"})
}

// @Summary  订单支付
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.OrderPayRequest body models.OrderPayRequest true " "
// @Success 200 {object} models.OrderPayResponse
// @Failure 400 {object} models.OrderPayResponse
// @Router /order-api/order/pay [post]
func AwenOrderPay(c echo.Context) error {
	var res models.OrderPayResponse
	params := new(models.OrderPayRequest)
	if err := c.Bind(params); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	glog.Info("order-api-AwenOrderPay====入参：", kit.JsonEncode(params))
	if params.OrderType == 4 {
		pinRep := ap.PinOrderPayRequest{
			PinOrderSn: params.Order_sn,
			OpenId:     params.Openid,
			TransType:  params.TransType,
		}
		client := ap.GetPinGroupServiceClient()
		defer client.Close()
		grpcRes, err := client.RPC.PinOrderPay(context.Background(), &pinRep)
		if err != nil {
			glog.Error("拼团单支付 PinOrderPay rpc error :"+err.Error(), kit.JsonEncode(pinRep))
			res.Code = 400
			res.Message = err.Error()
			return c.JSON(400, res)
		}
		if grpcRes.Code != 200 {
			glog.Info("拼团单支付 PinOrderPay rpc fail :", kit.JsonEncode(pinRep))
			return c.JSON(400, grpcRes)
		}
		res.Code = 200
		res.Message = "成功"
		res.Data = kit.JsonEncode(grpcRes)
	} else {
		ocClient := GetOrderServiceClient()
		defer ocClient.Close()
		//todo 在支付接口查订单信息，不做额外处理
		//in := &oc.GetOneOrderRequest{
		//	OrderSn: params.Order_sn,
		//}
		//orderRes, err := ocClient.RPC.GetOneOrder(ocClient.Ctx, in)
		//if err != nil {
		//	glog.Error(params.Order_sn, ", 调用GetOneOrder失败, ", err)
		//	return r.NewHTTPError(400, err.Error())
		//}
		//if orderRes.Code != 200 {
		//	glog.Error(params.Order_sn, ", 调用GetOneOrder失败, ", kit.JsonEncode(orderRes))
		//	return r.NewHTTPError(400, orderRes.Message)
		//}
		//order := params.Order_sn
		model := new(oc.AwenOrderPayRequest)
		model.TransType = params.TransType

		if params.TransType == 17 {
			model.Openid = c.Get("unionid").(string)
			model.SubAppId = c.Get("unionid").(string)
		} else {
			model.Openid = params.Openid
		}

		model.OrderId = params.Order_sn
		model.OutTradeNo = params.Order_sn
		//model.Discount = order.Privilege
		//model.PayPrice = order.Total
		model.ProductDesc = "商品描述"
		model.ProductId = "RP0000001"
		model.ProductName = "商品套餐-" + params.Order_sn
		//model.TotalPrice = order.Privilege + order.Total
		//model.TotalPrice = order.Total
		//model.OrderPayType = order.OrderPayType
		model.AppId = params.AppId

		glog.Info(params.Order_sn, ", 调用AwenOrderPay参数：", kit.JsonEncode(model))
		grpcRes, err := ocClient.RPC.AwenOrderPay(ocClient.Ctx, model)
		glog.Info(params.Order_sn, ", 调用AwenOrderPay返回结果：", kit.JsonEncode(grpcRes))

		if err != nil {
			glog.Error(params.Order_sn, ", 调用AwenOrderPay失败，", err)
			return r.NewHTTPError(400, err.Error())
		}
		if grpcRes.Code != 200 {
			glog.Error(params.Order_sn, ", 调用AwenOrderPay失败，", kit.JsonEncode(grpcRes))
			return r.NewHTTPError(400, grpcRes.Error+"-"+grpcRes.Message)
		}

		res.Code = 200
		res.Message = "成功"
		res.Data = grpcRes.Message
		//if len(orderRes.Order.CreateTime) > 0 {
		//	// ======= 将时间字符串转换为时间戳 =======
		//	stamp, _ := time.ParseInLocation("2006-01-02 15:04:05", orderRes.Order.CreateTime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
		//	res.CreateTime = stamp.UnixNano() / 1e6
		//}
	}

	//res.CreateTime =
	return c.JSON(200, res)
}

// @Summary 订单支付状态查询
// @Tags 订单
// @Accept json
// @Produce json
// @Param order_id query string true "order-api/order/pay接口返回的order_id"
// @Success 200 {object} oc.AwenOrderPayQueryResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/order/pay-query [GET]
func AwenOrderPayQuery(c echo.Context) error {
	in := &oc.AwenOrderPayQueryRequest{
		OrderId: c.QueryParam("order_id"),
	}
	if len(in.OrderId) > 50 || len(in.OrderId) < 10 {
		return r.NewHTTPError(400, "订单号格式错误")
	}
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	res, err := ocClient.RPC.AwenOrderPayQuery(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用AwenOrderPayQuery失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, res)
}

// @Summary 订单支付（电银B2C）
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.OrderPayRequest body models.OrderPayDyB2CRequest true " "
// @Success 200 {object} models.OrderPayResponse
// @Failure 400 {object} models.OrderPayResponse
// @Router /order-api/order/paydyb2c [post]
func OrderPayDyB2C(c echo.Context) error {
	params := new(models.OrderPayDyB2CRequest)
	if err := c.Bind(params); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.QueryOrderRequest{
		OrderSn: params.OrderSn,
	}
	orderRes, err := ocClient.RPC.QueryOrder(ocClient.Ctx, in)
	if err != nil {
		glog.Error(params.OrderSn, ", 调用QueryOrder失败, ", err)
		return r.NewHTTPError(400, err.Error())
	}
	if orderRes.Code != 200 {
		glog.Error(params.OrderSn, ", 调用QueryOrder失败, ", kit.JsonEncode(orderRes))
		return r.NewHTTPError(400, orderRes.Message)
	}
	payRequest := &oc.AwenOrderB2CPayRequest{
		BarCode:     params.BarCode,
		OutOrderNo:  orderRes.OrderModel.OrderSn,
		PayType:     params.PayType,
		TotalAmount: orderRes.OrderModel.Total + orderRes.OrderModel.Privilege,
		PayAmount:   orderRes.OrderModel.Total,
		Discount:    orderRes.OrderModel.Privilege,
		OrderName:   "竖屏本地生活",
		OrderDesc:   "微信",
		MercId:      params.MerchantId,
		OrgId:       params.OrgId,
		TrmId:       params.TrmId,
		TrmSn:       params.TrmSn,
		Source:      params.Source,
		Location:    params.Location,
	}
	if params.Source == 1 {
		//电商的订单用old_order_sn请求支付中心，否则支付回调找不到订单号
		payRequest.OutOrderNo = orderRes.OrderModel.OldOrderSn
		//判断支付宝支付
		codePrefix := cast.ToInt(params.BarCode[:2])
		if codePrefix >= 25 && codePrefix <= 30 {
			payRequest.OrderDesc = "支付宝"
			payRequest.PayType = 2
		}
	}
	grpcRes, err := ocClient.RPC.AwenOrderB2CPay(ocClient.Ctx, payRequest)
	if err != nil {
		glog.Error("调用AwenOrderB2CPay失败，", err, "，参数：", kit.JsonEncode(payRequest))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, grpcRes)
}

// @Summary 完成订单
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.OrderPayRequest body models.OrderPayRequest true " "
// @Success 200 {object} models.OrderPayResponse
// @Failure 400 {object} models.OrderPayResponse
// @Router /order-api/order/OrderPayCompleteTemporary [post]
func OrderPayCompleteTemporary(c echo.Context) error {
	var res models.OrderPayResponse
	params := new(models.OrderPayRequest)
	if err := c.Bind(params); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.GetOneOrderRequest{
		OrderSn: params.Order_sn,
	}
	orderRes, err := ocClient.RPC.OrderPayCompleteTemporary(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用OrderPayCompleteTemporary失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}

	res.Code = 200
	res.Error = orderRes.Error
	res.Data = orderRes.Message
	return c.JSON(200, res)
}

// Deprecated
// @Summary 订单提交
// @Tags 订单
// @Accept json
// @Produce json
// @Param AwenOrderSubmitRequest body oc.AwenOrderSubmitRequest true " "
// @Success 200 {object} oc.AwenOrderSubmitResponse
// @Failure 400 {object} oc.AwenOrderSubmitResponse
// @Router /order-api/order/submit [post]
//func AwenOrderSubmit(c echo.Context) error {
//	var out oc.AwenOrderSubmitResponse
//	model := new(oc.AwenOrderSubmitRequest)
//	if err := c.Bind(model); err != nil {
//		glog.Error("解析参数错误，", err)
//		return r.NewHTTPError(400, "解析参数错误")
//	}
//
//	glog.Info("阿闻商城订单：订单参数:", kit.JsonEncode(model))
//
//	//不给下0元的订单
//	if model.Total <= 0 {
//		return r.NewHTTPError(400, "异常单:订单支付金额小于等于0")
//	}
//
//	promotionProducts := make([]*mk.PromotionCalcProductDto, 0)
//	for _, item := range model.OrderProductModel {
//		promotionProducts = append(promotionProducts, &mk.PromotionCalcProductDto{
//			SkuId:    item.Sku,
//			SumMoney: int64(item.Price * item.Number),
//			Price:    int64(item.Price),
//			Count:    int64(item.Number),
//		})
//	}
//	//判断参与限时折扣商品是否超过单日库存数量
//	var Promotionids []string
//	for _, item := range model.PromotionOrderList {
//		if item.PromotionType == 2 { //限时折扣活动
//			PromotionIdStr := strconv.Itoa(int(item.PromotionId))
//			Promotionids = append(Promotionids, PromotionIdStr)
//		}
//	}
//	var skuids []string
//	for _, item := range model.OrderProductModel {
//		skuids = append(skuids, item.Sku)
//	}
//	timeDiscountLimitCounts := make([]*mk.PromotionProductLimitCountDto, 0)
//	if len(Promotionids) > 0 { //没有参与限时折扣，跳出检查
//		mkClient1 := mk.GetTimeDiscountServiceClient()
//		defer mkClient1.Close()
//
//		in := &mk.QueryLimitCountRequest{
//			Shopid:       model.ShopId,
//			Promotionids: strings.Join(Promotionids, ","),
//			Skuids:       strings.Join(skuids, ","),
//		}
//		limitRes, err := mkClient1.TimeDiscount.QueryLimitCountBySkus(mkClient1.Ctx, in)
//		if err != nil {
//			glog.Error("调用QueryLimitCountBySkus失败，", err, "，参数：", kit.JsonEncode(in))
//			return r.NewHTTPError(400, "查询是否参与限时折扣异常："+err.Error())
//		}
//
//		timeDiscountLimitCounts = limitRes.PromotionLimits
//		code, msg := checkTimeDiscountLockDayLimit(model, timeDiscountLimitCounts)
//		if code == 400 {
//			return r.NewHTTPError(400, msg)
//		}
//	}
//
//	//活动中心
//	mkClient := mk.GetMarketingCenterClient()
//	defer mkClient.Close()
//
//	in := &mk.PromotionCalcRequest{
//		ShopId:           model.ShopId,
//		ChannelId:        cast.ToInt32(model.ChannelId),
//		DestinationX:     model.Longitude,
//		DestinationY:     model.Latitude,
//		PromotionProduct: promotionProducts,
//	}
//	//获取减免信息
//	//promotionReduceRes, err := mkClient.RPC.GetPromotionReduceMoney(mkClient.Ctx, &mk.PromotionReduceCalcRequest{ShopId: model.ShopId, ChannelId: cast.ToInt32(model.ChannelId), PromotionProduct: promotionProducts})
//	promotionReduceRes, err := mkClient.RPC.CalcPromotionMoney(mkClient.Ctx, in)
//	if err != nil {
//		glog.Error(model.ReceiverPhone, "，调用CalcPromotionMoney失败，", err, "，参数：", kit.JsonEncode(in))
//		return r.NewHTTPError(400, "获取优惠信息异常，请稍后重试")
//	}
//
//	if promotionReduceRes.ActualMoneyByMinUnit != model.GoodsTotal || (promotionReduceRes.ReduceMoneyByMinUnit) != model.Privilege {
//		return r.NewHTTPError(400, "订单实付金额或优惠金额已改变，请重新下单")
//	}
//
//	//验证商品参与活动：如果有满减活动或者限时折扣活动，但是前端传的商品未绑定活动类型和活动id，则补全
//	glog.Info("阿闻商城订单：CalcPromotionMoney返回结果:" + kit.JsonEncode(promotionReduceRes))
//	checkProductMap := make(map[string]*mk.PromotionCalcProductDto)
//	for _, p := range promotionReduceRes.PromotionProduct {
//		checkProductMap[p.SkuId] = p
//	}
//	for _, product := range model.OrderProductModel {
//		if promotionCalcProduct, ok := checkProductMap[product.Sku]; ok {
//			product.Discountcount = int32(promotionCalcProduct.DiscountCount)
//			product.DiscountPrice = promotionCalcProduct.DiscountPrice
//			product.PromotionType = promotionCalcProduct.PromotionType
//			product.PromotionId = promotionCalcProduct.PromotionId
//		}
//	}
//
//	products := make([]*oc.UpetDjMoneyCalcProductDto, 0)
//	for _, item := range model.OrderProductModel {
//		products = append(products, &oc.UpetDjMoneyCalcProductDto{
//			SkuId: item.Sku,
//			Count: item.Number,
//		})
//	}
//
//	if model.OrderType != 3 {
//		if promotionReduceRes.UpetActualDjMoneyByMinUnit != model.Freight {
//			return r.NewHTTPError(400, "订单运费已改变，请重新下单")
//		}
//	}
//	if model.OrderType != 3 { //不是自提单
//		model.Freight = promotionReduceRes.UpetDjMoneyByMinUnit //add by csf修改bug:原来的运费多少，前端取的是优惠后的运费，自己取总运费
//	}
//
//	//预计送达时间是否在一个半小时后
//	now := time.Now().Local()
//	m, _ := time.ParseDuration("120m")
//	date := now.Add(m)
//
//	spt := strings.Split(model.ExpectedTime, " ")
//	spt2 := strings.Split(spt[0], "-")
//	if len(spt2[2]) == 1 {
//		model.ExpectedTime = fmt.Sprintf("%s-%s-0%s %s", spt2[0], spt2[1], spt2[2], spt[1])
//	}
//
//	var orderType int32 = 1
//
//	if model.OrderType != 3 {
//		expectedTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, model.ExpectedTime, time.Local)
//		if err != nil {
//			return r.NewHTTPError(400, "预计送达时间参数错误")
//		}
//		if expectedTime.After(date) {
//			orderType = 2
//		}
//	}
//
//	//前端如果直接传过来就直接取值，没有按照原先的逻辑
//	if model.OrderType == 3 {
//		orderType = model.OrderType
//	}
//
//	//数据中心
//	dcClient := dac.GetDataCenterClient()
//	defer dcClient.Close()
//
//	if orderType != 3 {
//		glog.Info("阿闻商城订单：获取配送范围，", model.ReceiverPhone)
//		in := &dac.EsSearchRequest{
//			LonLat:    cast.ToString(model.Longitude) + "," + cast.ToString(model.Latitude),
//			Type:      "geo_shape",
//			PageIndex: 1,
//			PageSize:  1000,
//		}
//		//是否在配送范围内
//		esRes, err := dcClient.RPC.QueryEsStorePointShape(dcClient.Ctx, in)
//		if err != nil {
//			glog.Error(model.ReceiverPhone, "，调用QueryEsStorePointShape失败，", err, "，参数：", kit.JsonEncode(in))
//			return r.NewHTTPError(400, "配送范围查询异常，请稍后重试")
//		}
//
//		var canOrder = false
//		if esRes.Code == 200 && len(esRes.Hits.Hits) > 0 {
//			for _, item := range esRes.Hits.Hits {
//				if item.XId == model.ShopId {
//					canOrder = true
//					break
//				}
//			}
//		}
//		if canOrder == false {
//			return r.NewHTTPError(400, "收货地址不在配送范围内")
//		}
//		glog.Info("阿闻商城订单：店铺信息，", model.ReceiverPhone)
//	}
//
//	// 店铺是否打烊
//	deliveryServiceReq := &dac.ShopDeliveryServiceListRequest{
//		ChannelId:    cast.ToInt32(model.ChannelId),
//		FinanceCodes: model.ShopId,
//	}
//	deliveryRes, err := dcClient.RPC.ShopDeliveryServiceList(dcClient.Ctx, deliveryServiceReq)
//	if err != nil {
//		glog.Error(model.ReceiverPhone, "，调用ShopDeliveryServiceList失败，", err, "，参数：", kit.JsonEncode(deliveryServiceReq))
//		return r.NewHTTPError(400, "查询门店服务状态异常，请稍后重试")
//	}
//	if deliveryRes.DataList != nil {
//		if deliveryRes.DataList[0].AdvanceorderStatus == 2 && orderType != 3 {
//			orderType = 2
//		}
//
//		if deliveryRes.DataList[0].IsSelfLifting == false && orderType == 3 {
//			return r.NewHTTPError(4008, "店铺关闭了自提服务")
//		}
//
//		if deliveryRes.DataList[0].AdvanceorderStatus == 3 {
//			return r.NewHTTPError(4002, "店铺已打烊")
//		}
//
//		if deliveryRes.DataList[0].AdvanceorderStatus == 4 {
//			return r.NewHTTPError(4003, "店铺已闭店")
//		}
//	}
//
//	glog.Info("阿闻商城订单：Sku信息，", model.ReceiverPhone)
//
//	//商品ID集合
//	productIDs := make([]int32, 0)
//	//集合下商品优惠活动
//	productIds_ManJian := make(map[string]int32, 0)
//	totalMoney_ManJian := int32(0)
//
//	for _, item := range model.OrderProductModel {
//		productIDs = append(productIDs, cast.ToInt32(item.ProductId))
//		if item.PromotionType == 1 {
//			productIds_ManJian[item.ProductId] = cast.ToInt32(item.ProductId)
//			totalMoney_ManJian += item.Number * item.Price
//		}
//	}
//
//	glog.Info("阿闻商城订单：查询商品信息，", model.ReceiverPhone)
//	//商品中心
//	pcClient := pc.GetDcProductClient()
//	defer pcClient.Close()
//
//	storeProductReq := &pc.ChannelStoreProductRequest{
//		ChannelId:   cast.ToInt32(model.ChannelId),
//		FinanceCode: []string{model.ShopId},
//		ProductId:   productIDs,
//		UpDownState: -1,
//	}
//	storeProductRes, err := pcClient.RPC.QueryChannelStoreProduct(pcClient.Ctx, storeProductReq)
//	if err != nil {
//		glog.Error(model.ReceiverPhone, "，调用QueryChannelStoreProduct失败，", err, "，参数：", kit.JsonEncode(storeProductReq))
//		return r.NewHTTPError(400, "商品信息查询异常，请稍后重试")
//	}
//	if storeProductRes.Details == nil {
//		return r.NewHTTPError(400, "商品信息不存在")
//	}
//
//	out.DataList = make([]*oc.SubmitProduct, 0)
//	var productSnapsIds []int32
//	if storeProductRes.Code == 200 {
//		for _, item := range storeProductRes.Details {
//			if item.UpDownState == 0 {
//				out.DataList = append(out.DataList, &oc.SubmitProduct{Status: 3, Id: cast.ToString(item.Id), ProductId: cast.ToString(item.ProductId)})
//			}
//			productSnapsIds = append(productSnapsIds, item.SnapshotId)
//		}
//	}
//
//	glog.Info("阿闻商城订单：下单，", model.ReceiverPhone)
//
//	// 通用下单
//	var mtOrderReq = oc.MtAddOrderRequest{}
//	mtOrderReq.ShopId = model.ShopId
//	mtOrderReq.ShopName = model.ShopName
//	mtOrderReq.BuyerMemo = model.BuyerMemo
//	mtOrderReq.DeliveryType = 2
//	if orderType == 3 {
//		mtOrderReq.DeliveryType = 3
//	}
//	mtOrderReq.Freight = model.Freight
//	mtOrderReq.GoodsTotal = model.GoodsTotal + model.Privilege
//	mtOrderReq.Latitude = model.Latitude
//	mtOrderReq.Longitude = model.Longitude
//	mtOrderReq.OrderStatus = 10
//	mtOrderReq.OrderStatusChild = 20101
//	mtOrderReq.OrderType = orderType
//
//	mtOrderReq.Privilege = model.Privilege //前端给过来的只包含满减的优惠，不包括满减运费和限时折扣活动的优惠，但是下单的时候是要加上
//	mtOrderReq.ReceiverName = model.ReceiverName
//	mtOrderReq.ReceiverPhone = model.ReceiverPhone
//	mtOrderReq.ReceiverMobile = model.ReceiverPhone
//	mtOrderReq.ReceiverState = model.ReceiverState
//	mtOrderReq.ReceiverCity = model.ReceiverCity
//	mtOrderReq.ReceiverDistrict = model.ReceiverDistrict
//	mtOrderReq.ReceiverAddress = model.ReceiverAddress
//	mtOrderReq.LogisticsCode = "0000"
//	mtOrderReq.Invoice = model.Invoice
//	mtOrderReq.ExpectedTime = model.ExpectedTime
//	//mtOrderReq.Total = model.GoodsTotal + model.Freight //前端商品总金额已减去优惠金额
//	mtOrderReq.TotalWeight = model.TotalWeight
//	glog.Info("阿闻商城订单：获取用户信息，", model.ReceiverPhone)
//	// 读取用户信息
//	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
//	if !ok || memberInfo == nil {
//		return r.NewHTTPError(400, "用户信息不存在")
//	}
//
//	mtOrderReq.MemberId = memberInfo.Id
//	mtOrderReq.MemberTel = utils.GetTelFromHeader(c)
//	mtOrderReq.MemberName = memberInfo.Name
//
//	in2 := &pc.ChannelProductSnapshotRequest{
//		ChannelId: cast.ToInt32(model.ChannelId),
//		Ids:       productSnapsIds,
//	}
//	//订单商品信息处理
//	skuRes, err := pcClient.RPC.QueryChannelProductSnapshot(pcClient.Ctx, in2)
//	if err != nil {
//		glog.Error(model.ReceiverPhone, "，调用QueryChannelProductSnapshot失败，", err, "，参数：", kit.JsonEncode(in2))
//		return r.NewHTTPError(400, "查询商品信息异常，请稍后重试")
//	}
//	SkuProducts := make(map[int32]pc.ChannelProductSnapshot)
//	for _, v := range skuRes.Details {
//		if _, ok = SkuProducts[v.ProductId]; !ok {
//			SkuProducts[v.ProductId] = *v
//		}
//	}
//
//	//统计优惠
//	var allPrivilegeAmount int32 //所有活动的总优惠金额
//	allPrivilegeAmount = 0
//	var freightPrivilegeAmount int32 //运费优惠金额
//	freightPrivilegeAmount = 0
//	orderPromots := make([]*oc.OrderPromotionModel, 0)
//	for _, item := range model.PromotionOrderList {
//		if model.OrderType == 3 && item.PromotionType == 3 {
//			//自提单且活动类型是满减运费活动，则过滤掉,因为自提单没有满减运费
//			continue
//		} else {
//			orderPromots = append(orderPromots, &oc.OrderPromotionModel{
//				PromotionId: item.PromotionId,
//				//PromotionType:  2,
//				PromotionType:  item.PromotionType,
//				PromotionTitle: item.PromotionTitle,
//				//PromotionTitle: "满减活动",
//				PoiCharge:    item.PromotionFee,
//				PtCharge:     0,
//				PromotionFee: item.PromotionFee,
//			})
//
//			allPrivilegeAmount += item.PromotionFee
//			if item.PromotionType == 3 {
//				freightPrivilegeAmount += item.PromotionFee
//			}
//		}
//	}
//
//	glog.Info("阿闻商城订单：Sku处理，", model.ReceiverPhone)
//
//	in3 := &dac.ShopStoreGetRequest{
//		Finance_Code: model.ShopId,
//	}
//	shopStore, err := dcClient.RPC.ShopStoreGet(dcClient.Ctx, in3)
//	if err != nil {
//		glog.Error("调用ShopStoreGet失败，", err, "，参数：", kit.JsonEncode(in3))
//		return r.NewHTTPError(400, "店铺信息查询异常，请稍后重试")
//	}
//
//	zilongId := cast.ToInt32(shopStore.Data.ZilongId)
//	orderProducts := make([]*oc.OrderProductModel, 0)
//	var pall int32            //累计所有商品优惠金额，用于计算最后一个商品的均摊优惠金额（总优惠-累计的优惠金额=最后一个商品的优惠金额）
//	var skuPayTotalPull int32 //累计订单所有sku实付金额，用于计算最后一个sku商品的实际支付金额（订单实付金额-累计的实付金额=最后一个商品的实付金额）-- 满减适用
//	skucodeInfo := make([]*ic.SkuCodeInfo, 0)
//
//	//统计订单折扣总金额
//	for _, item := range model.OrderProductModel {
//		product, ok := SkuProducts[cast.ToInt32(item.ProductId)]
//		if !ok {
//			return r.NewHTTPError(4005, "["+item.ProductId+"]"+item.ProductName+"信息不存在，下单失败")
//		}
//
//		productSnapshotResponse := models.ProductSnapshotResponse{}
//		err = json.Unmarshal([]byte(product.JsonData), &productSnapshotResponse)
//		if err != nil {
//			glog.Error(item.ProductId, ", json解析失败, ", err, ", json, ", product.JsonData)
//			return r.NewHTTPError(400, "下单失败，商品["+item.ProductId+"]快照信息异常")
//		}
//
//		if len(productSnapshotResponse.SkuInfo) == 0 || (len(productSnapshotResponse.SkuInfo) > 0 && len(productSnapshotResponse.SkuInfo[0].SnapsThirdSku) == 0) {
//			glog.Error("[" + item.ProductId + "]" + item.ProductName + "商品第三方Sku信息不存在，" + model.ReceiverPhone)
//			return r.NewHTTPError(400, "["+item.ProductId+"]"+item.ProductName+"商品第三方Sku信息不存在，下单失败")
//		}
//
//		marketPrice := productSnapshotResponse.SkuInfo[0].MarketPrice
//		specs := productSnapshotResponse.SkuInfo[0].SnapsSkuv[0].SpecName + "：" + productSnapshotResponse.SkuInfo[0].SnapsSkuv[0].SpecValue
//		img := ""
//		picArr := strings.Split(productSnapshotResponse.Product.Pic, ",")
//		if len(picArr) > 0 {
//			img = picArr[0]
//		}
//
//		for _, v := range productSnapshotResponse.SkuInfo[0].SnapsThirdSku {
//			skucodeInfo = append(skucodeInfo, &ic.SkuCodeInfo{
//				FinanceCode: model.ShopId,
//				Sku:         item.Sku,
//				ThirdSkuid:  v.ThirdSkuId,
//				ErpId:       v.ErpId,
//				ZlId:        zilongId,
//			})
//		}
//
//		//均摊逻辑：满减运费和限时折扣活动不参与均摊，只有满减活动参与均摊
//
//		//if idx == len(model.OrderProductModel)-1 {
//		//不管是否是参与活动商品信息
//		var skuPayTotal int32
//		discoutProduct := oc.OrderProductModel{}
//		orderProduct := oc.OrderProductModel{}
//		discoutProduct = oc.OrderProductModel{Sku: item.Sku,
//			ProductId:      item.ProductId,
//			ProductName:    item.ProductName,
//			BarCode:        item.BarCode,
//			Price:          marketPrice,
//			Number:         item.Number,
//			PayPrice:       marketPrice, //折扣价
//			MarkingPrice:   marketPrice,
//			PaymentTotal:   item.Number * item.DiscountPrice,
//			Privilege:      0,
//			PrivilegeTotal: item.Number * marketPrice,
//			Specs:          specs,
//			Image:          img,
//			PromotionId:    item.PromotionId,
//		}
//		orderProduct = discoutProduct //拷贝一份
//
//		if item.PromotionType == 1 {
//			//满减活动
//			payPrice := int32(0)
//			priviTotal := int32(0)
//			if len(productIds_ManJian) == 1 {
//				n := cast.ToFloat64(model.Privilege-pall) / cast.ToFloat64(item.Number-item.Discountcount)
//				payPrice = marketPrice - cast.ToInt32(n)
//				if marketPrice <= cast.ToInt32(n) { //add by csf保证不能小于等于0
//					payPrice = 1
//				}
//				priviTotal = model.Privilege - pall
//				skuPayTotal = totalMoney_ManJian - model.Privilege - skuPayTotalPull
//				delete(productIds_ManJian, item.ProductId) //减少参与打折的商品种类
//			} else if len(productIds_ManJian) > 1 {
//				//计算当前
//				n := cast.ToFloat64(marketPrice*(item.Number-item.Discountcount)) / cast.ToFloat64(totalMoney_ManJian) * cast.ToFloat64(model.Privilege) //model.Privilege只是满减活动的优惠，不包含折扣活动和满减运费的优惠
//				payPrice = marketPrice - cast.ToInt32(math.Ceil(n)/cast.ToFloat64(item.Number-item.Discountcount))
//				priviTotal = cast.ToInt32(math.Ceil(n))
//				pall += priviTotal
//				if pall > model.Privilege { //add by csf@累计的优惠金额不能超过实际的优惠总金额
//					pall = model.Privilege
//					priviTotal -= pall - model.Privilege //如果上次已超出，则本商品需要减去已超出的优惠金额
//				}
//				skuPayTotal += (marketPrice*item.Number - cast.ToInt32(math.Ceil(n)))
//				//skuPayTotal += (item.Number) * payPrice    //记录实付金额
//				delete(productIds_ManJian, item.ProductId) //减少参与打折的商品种类
//			} else {
//				return r.NewHTTPError(400, "订单的商品数量不合法")
//			}
//			orderProduct.PayPrice = payPrice
//			orderProduct.PaymentTotal = skuPayTotal
//			orderProduct.PrivilegeTotal = priviTotal
//			skuPayTotalPull += skuPayTotal //记录实付金额
//		} else if item.PromotionType == 2 {
//			//折扣活动，购买的商品数量超过限制的折扣数量
//			if item.Discountcount > 0 { //如果有参与限时折扣活动的商品，则不参与均摊
//				discoutProduct.Number = item.Discountcount
//				discoutProduct.PayPrice = item.DiscountPrice
//				discoutProduct.PaymentTotal = item.Discountcount * item.DiscountPrice
//				discoutProduct.PrivilegeTotal = item.Discountcount * (marketPrice - item.DiscountPrice)
//				skuPayTotal += item.Discountcount * item.DiscountPrice //记录实付金额
//			}
//			//如果还有未打折商品-走原价处理
//			if item.Number-item.Discountcount > 0 {
//				orderProduct.Number = item.Number - item.Discountcount
//				orderProduct.PaymentTotal = (item.Number - item.Discountcount) * marketPrice
//				skuPayTotal += (item.Number - item.Discountcount) * marketPrice //记录实付金额
//				orderProduct.PrivilegeTotal = 0
//			}
//		} else {
//			//无任何活动的商品
//			skuPayTotal += (item.Number) * marketPrice
//			orderProduct.PaymentTotal = (item.Number) * marketPrice
//			orderProduct.PrivilegeTotal = 0
//		}
//
//		//最后处理保存订单的商品集合
//		if item.Discountcount > 0 {
//			discoutProduct.SkuPayTotal = skuPayTotal
//			orderProducts = append(orderProducts, &discoutProduct)
//		}
//		if item.Number-item.Discountcount > 0 {
//			orderProduct.SkuPayTotal = skuPayTotal
//			orderProducts = append(orderProducts, &orderProduct)
//		}
//	}
//
//	mtOrderReq.OrderProductModel = orderProducts
//
//	stockProducts, err := GetStockInfoBySkuCode(skucodeInfo, 1)
//	if err != nil {
//		glog.Error("查询商品库存失败，" + err.Error() + "，" + model.ReceiverPhone)
//		return r.NewHTTPError(400, "查询商品库存失败，请稍后重试")
//	}
//
//	for _, item := range model.OrderProductModel {
//		key := model.ShopId + ":" + item.Sku
//		c := stockProducts[key]
//		if c == 0 || c < item.Number {
//			hasDown := false
//			for _, p := range out.DataList {
//				if p.ProductId == item.ProductId {
//					hasDown = true
//				}
//			}
//
//			if !hasDown {
//				out.DataList = append(out.DataList, &oc.SubmitProduct{Status: 1, Id: item.Sku, ProductId: item.ProductId})
//			}
//		}
//	}
//	if len(out.DataList) > 0 {
//		return r.NewHTTPError(4004, "下单失败，存在无货、下架商品")
//	}
//
//	glog.Info("阿闻商城订单：优惠活动处理，", model.ReceiverPhone)
//	mtOrderReq.Privilege = allPrivilegeAmount                                    //mod by csf 前端给过来的优惠金额没有包含限时折扣和满减运费活动的优惠金额，导致李强那边下单有问题，所以修改加上限时活动优惠，但是自提单是要去掉满减运费活动的
//	mtOrderReq.Total = model.GoodsTotal + model.Freight - freightPrivilegeAmount //前端商品总金额已减去优惠金额,Privilege不包含满减运费和限时折扣的优惠
//	mtOrderReq.OrderPromotion = orderPromots
//
//	ocClient := oc.GetOrderServiceClient()
//
//	//金额验证：总金额=商品总价+配送费+服务费+包装费-优惠金额-配送费优惠
//	glog.Info("阿闻商城订单：统一下单：", kit.JsonEncode(mtOrderReq))
//
//	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)
//	grpcRes, err := ocClient.Cart.MtSubmitOrder(ctx, &mtOrderReq)
//	if err != nil {
//		glog.Error(model.ReceiverPhone, "，调用MtSubmitOrder失败，", err, "，参数：", kit.JsonEncode(mtOrderReq))
//		return r.NewHTTPError(400, "提交订单失败，请稍后重试")
//	}
//	if grpcRes.Code != 200 {
//		return r.NewHTTPError(400, "提交订单失败，"+grpcRes.Message)
//	}
//
//	//限时折扣活动锁单日库存
//	if len(Promotionids) > 0 {
//		code, msg := timeDiscountLockDayLimit(model, timeDiscountLimitCounts, grpcRes.OrderSn)
//		if code == 400 {
//			glog.Error("限时折扣活动锁单日库存失败，" + msg + "，" + model.ReceiverPhone)
//			return r.NewHTTPError(400, msg)
//		}
//	}
//
//	out.Code = 200
//	out.OrderSn = grpcRes.OrderSn
//	return c.JSON(200, out)
//}

// OrderDoSubmit
// @Summary 电商和小程序订单通用下单
// @Tags 订单
// @Description Header头部添加group_id参数，社区团购id
// @Accept json
// @Produce json
// @Param OrderSubmitRequest body models.OrderSubmitRequest true " "
// @Success 200 {object} models.OrderSubmitResponse
// @Failure 400 {object} models.OrderSubmitResponse
// @Router /order-api/order/docommit [post]
func OrderDoSubmit(c echo.Context) error {

	//思路：
	//1:记录请求参数
	//补全订单信息，下单人，订单类型，配送类型，预计送达时间
	//2:验证
	//i:订单常规验证：金额大于0，商品数量大于0
	//小程序验证一下步骤，电商单不需要
	//ii:限时折扣活动单日库存验证
	//iii:下单金额，运费金额是否变化
	//iii:是否在配送范围
	//iiii:店铺设置是否营业状态
	//：验证商品信息及返回商品快照
	//3:均摊商品
	//4:主订单落地(order-center通用下单会锁通用库存，落地在途库存)
	//5:锁限时折扣活动单日库存
	model := new(models.OrderSubmitRequest)
	if err := c.Bind(model); err != nil {
		glog.Error("解析参数错误，", err)
		return r.NewHTTPError(400, "解析参数错误")
	}

	if model.Order.ChannelId == ChannelDigitalHealth {
		c.Request().Header.Set("channel_id", cast.ToString(model.Order.ChannelId))
	}
	channel_id := c.Request().Header.Get("channel_id")
	user_agent := c.Request().Header.Get("user_agent")
	clickId := c.Request().Header.Get("click_id")
	lng := c.Request().Header.Get("lng")
	lat := c.Request().Header.Get("lat")

	org_id := ""
	if model.Order.ChannelId == 5 {
		org_id = model.Order.ShopId //c.Request().Header.Get("org_id")
	} else {
		//不是电商的就从请求头取
		org_id = c.Request().Header.Get("org_id")
	}
	//如果是空的，默认就是瑞鹏
	if org_id == "" {
		org_id = "1"
	}
	model.Order.OrgId = cast.ToInt32(org_id)
	// 社区团购id
	groupId := cast.ToInt64(c.Request().Header.Get("group_id"))
	var isPush = false

	openId := ""
	val := c.Get("open_id").(string)
	if org_id == "6" {
		val = c.Request().Header.Get("open_id")
	}
	if len(val) > 0 {
		openId = val
		isPush = true
		//go AddOrdersToTencent(model, grpcRes.OrderSn, openId)
	}
	// 不给下0元的订单
	if model.Order.Total <= 0 && model.Order.OrderType != 19 {
		return r.NewHTTPError(400, "订单支付金额不能小于等于0")
	}
	if model.Order.ChannelId == 1 {
		mappro := make(map[string]string)
		for _, v := range model.OrderProducts { // Changed from model.Order.OrderProductModel
			if _, ok := mappro[v.Sku]; ok {
				return r.NewHTTPError(400, "订单中存在重复商品")
			} else {
				mappro[v.Sku] = "1"
			}
		}
	}

	//返回结构体

	var out models.OrderSubmitResponse

	//1：记录请求参数
	tip := "阿闻小程序订单：订单参数："
	if model.Order.ChannelId == ChannelMallId {
		tip = "阿闻电商订单：订单参数："
	} else if model.Order.ChannelId == ChannelDigitalHealth {
		tip = "互联网医院订单：订单参数："
	}
	glog.Infof(tip+"%s，请求头：%s", kit.JsonEncode(model), kit.JsonEncode(c.Request().Header))

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()
	//互联网医疗订单过滤
	if model.Order.ChannelId == ChannelDigitalHealth {
		if len(model.Order.ConsultOrderSn) <= 0 {
			return r.NewHTTPError(400, "互联网医疗订单号为空")
		}
		checkRes, err := ocClient.RPC.DigitalHealthOrderCheck(context.Background(), &oc.DigitalHealthOrderCheckRequest{
			ConsultOrderSn: model.Order.ConsultOrderSn,
		})
		if err != nil {
			return r.NewHTTPError(400, "查询互联网订单信息异常")
		}
		if checkRes.IsHad > 0 {
			return r.NewHTTPError(400, "该处方单已经存在订单，请到我的订单查看")
		}
	}
	//2:验证：
	//阿闻到家订单 补全订单部分信息：预计送达时间（预计送达时间是否在一个半小时后）,订单类型，订单配送类型
	if model.Order.ChannelId == ChannelAwenId || model.Order.ChannelId == ChannelDigitalHealth {
		dcClient := dac.GetDataCenterClient()
		defer dcClient.Close()
		deliveryServiceReq := &dac.ShopDeliveryServiceListRequest{
			ChannelId:    cast.ToInt32(model.Order.ChannelId),
			FinanceCodes: model.Order.ShopId,
		}
		deliveryRes, err := dcClient.RPC.ShopDeliveryServiceList(dcClient.Ctx, deliveryServiceReq)
		if err != nil {
			return r.NewHTTPError(400, "门店信息异常，请稍后再试！")
		}

		now := time.Now().Local()
		m, _ := time.ParseDuration("90m")
		date := now.Add(m) //配送时间增加一个半小时，判断业务逻辑

		var orderType int32 = 1         //订单类型：1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送
		if model.Order.OrderType == 3 { //前端如果直接传过来就直接取值，没有按照原先的逻辑
			orderType = model.Order.OrderType
		} else if groupId < 1 {
			//互联网订单不参与社区团购
			if model.Order.ChannelId != ChannelDigitalHealth {
				// groupId < 1时，判断门店是否参加社区团购，参加了不允许下单
				gaRes, err := dcClient.CG.GroupActivityGet(dcClient.Ctx, &dac.GroupActivityGetRequest{
					StoreFinanceCode: model.Order.ShopId,
					Status:           1,
				})
				if err != nil {
					glog.Error("订单提交docommit查询店铺社团活动出错：", kit.JsonEncode(model), err.Error())
				}
				// app社区团购屏蔽
				if (user_agent == "1" || user_agent == "2") && gaRes.Id > 0 {
					glog.Error("app端订单提交docommit查询店铺社团活动：", kit.JsonEncode(model))
					return r.NewHTTPError(400, "抱歉，当前app暂不支持下单，请到阿闻宠物小程序下单")
				}
				if gaRes.Id > 0 {
					glog.Error("订单提交docommit查询店铺社团活动：", kit.JsonEncode(model))
					return r.NewHTTPError(400, "店铺已开启社区团购，请参团下单")
				}
			}

			expectedTime, err := time.ParseInLocation(kit.DATETIME_LAYOUT, model.Order.ExpectedTime, time.Local)
			if err != nil {
				return r.NewHTTPError(400, "预计送达时间参数错误")
			}
			if expectedTime.Before(time.Now()) {
				return r.NewHTTPError(400, "请重新选择送达时间")
			}
			if expectedTime.After(date) {
				orderType = 2
			} else {
				// 新增判断，检测配送时间是否在门店运营时间内
				if deliveryRes.DataList != nil {
					if deliveryRes.DataList[0].AdvanceorderStatus == 2 && orderType != 3 {
						closeTime := time.Unix(int64(deliveryRes.DataList[0].BusinessClosetime), 0)
						// 当天闭店后下单
						if closeTime.Format(kit.DATE_LAYOUT) == expectedTime.Format(kit.DATE_LAYOUT) &&
							expectedTime.After(closeTime) {
							return r.NewHTTPError(400, "店铺已打烊，请重新选择时间")
						}
						// 早上开店前下单
						orderType = 2
						openTime := time.Unix(int64(deliveryRes.DataList[0].BusinessOpentime), 0)
						model.Order.ExpectedTime = openTime.Add(45 * time.Minute).Format(kit.DATETIME_LAYOUT)
					}

					if deliveryRes.DataList[0].AdvanceorderStatus == 3 {
						return r.NewHTTPError(4002, "店铺已打烊")
					}
					if deliveryRes.DataList[0].AdvanceorderStatus == 4 {
						return r.NewHTTPError(4003, "店铺已闭店")
					}
				}
			}
		}

		if len(deliveryRes.DataList) > 0 {
			if len(user_agent) > 0 && cast.ToInt32(user_agent) == 3 && deliveryRes.DataList[0].IsSelfLiftingApp == false && orderType == 3 {
				return r.NewHTTPError(4008, "没有开启到店自提。")
			}

			if len(user_agent) > 0 && cast.ToInt32(user_agent) == 7 && deliveryRes.DataList[0].IsSelfLifting == false && orderType == 3 {
				return r.NewHTTPError(4008, "没有开启到店自提。")
			}
		}

		model.Order.OrderType = orderType
	}

	glog.Info("阿闻商城订单：获取用户信息，", model.Order.ReceiverPhone)
	// 读取用户信息
	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户信息不存在")
	}

	//验证逻辑
	var timeDiscountLimitCounts []*mk.PromotionProductLimitCountDto
	out.Code, out.Message, timeDiscountLimitCounts = checkOrder(model, memberInfo, user_agent)
	if out.Code != 200 {
		return c.JSON(200, out)
	}

	//3:均摊商品(包含商品验证：上下架状态，快照验证，商品价格)
	glog.Info("均摊：计算金额，邮费，优惠信息 保存订单、明细, ", model.Order.ReceiverPhone)

	var AddOrder *oc.MtAddOrderRequest
	if model.Order.ChannelId == ChannelAwenId || model.Order.ChannelId == ChannelDigitalHealth { //阿闻到家
		glog.Info(model.Order.ReceiverPhone, "阿闻均摊前入参", kit.JsonEncode(model))
		out.Code, out.Message, AddOrder, out.CannotProducts = JunTan(model)
		glog.Info(model.Order.ReceiverPhone, "阿闻均摊结果", kit.JsonEncode(AddOrder), out.Code, out.Message, kit.JsonEncode(out.CannotProducts))
	} else if model.Order.ChannelId == ChannelMallId { //阿闻商城（电商）
		out.Code, out.Message, AddOrder = MallJunTan(model)
	}

	if AddOrder == nil {
		SubmitOrderFail(model)
		if out.Code != 200 {
			return c.JSON(200, out)
		} else {
			out.Message = "订单信息不全，请重新下单"
			return c.JSON(400, out)
		}
	}

	// 社区团购相关逻辑
	if groupId > 0 {
		glog.Info("社区团购提交订单OrderDoSubmit入参：", c.Request().Header.Get("group_id"), kit.JsonEncode(model.Order))
		g, err := communityGroupValidate(groupId, model.Order, user_agent)
		if err != nil {
			return r.NewHTTPError(400, err.Error())
		}
		AddOrder.GroupId = groupId
		AddOrder.GroupName = model.Order.ExtraInfo.GroupName
		AddOrder.GroupMobile = model.Order.ExtraInfo.GroupMobile
		AddOrder.GroupAddress = model.Order.ExtraInfo.GroupAddress
		AddOrder.NickName = model.Order.ExtraInfo.NickName
		AddOrder.AvatarUrl = model.Order.ExtraInfo.AvatarUrl
		// 预计送达时间：团结束时间+后台N天
		endTime, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, g.Data.EndTime, time.Local)
		AddOrder.ExpectedTime = endTime.AddDate(0, 0, cast.ToInt(g.Data.DeliverDays)).Format(kit.DATE_LAYOUT) + " 23:59:59" // 预计送达时间，成团结束时间+N天后
	}

	// 店铺分销海报记录，需求说明如下
	// 该店铺设置了推广海报，所有人都可推广门店
	// 从店铺海报进店开团的，团内所有订单业绩归分享海报的人和门店（若分享海报的人不是分销员，则无业绩），佣金归团长。
	AddOrder.ShopDisMemberId = model.Order.ExtraInfo.DisMemberId
	AddOrder.ShopDisMemberFrom = model.Order.ExtraInfo.DisMemberFrom

	//支付方式
	AddOrder.OrderPayType = model.Order.OrderPayType
	if out.Code != 200 {
		SubmitOrderFail(model)
		return c.JSON(200, out)
	}

	//补全订单下单人信息
	if len(memberInfo.Id) > 0 {
		AddOrder.MemberId = memberInfo.Id
		//AddOrder.MemberTel = utils.GetTelFromHeader(c)
		AddOrder.MemberTel = memberInfo.ScrmUserMobile
		AddOrder.MemberName = memberInfo.Name
	}
	AddOrder.Lng = lng
	AddOrder.Lat = lat
	AddOrder.PickupStationId = model.Order.PickupStationId

	//判定时候需要加上推送腾讯有数的标识
	if isPush {
		AddOrder.IsPushTencent = 1
	}

	//4:提交订单信息到order-center保存订单

	ctx := utils.AppendToOutgoingContextLoginUserInfo(kit.SetTimeoutCtx(context.Background()), c)

	//todo 现在直接用电商对应好的机构ID，二期从reids里面转
	AddOrder.OrgId = cast.ToInt32(org_id)
	glog.Info(model.Order.ReceiverPhone, "===========下单请求参数===========", kit.JsonEncode(AddOrder))
	//	ocClient1 := GetOrderServiceClient1()
	//defer ocClient.Close()
	grpcRes, err := ocClient.Cart.MtSubmitOrder(ctx, AddOrder)

	glog.Info(model.Order.ReceiverPhone, "===========返回的grpcres===========", kit.JsonEncode(grpcRes))

	//失败处理 v2.9.10 秒杀版本添加
	if err != nil || grpcRes.Code != 200 {
		SubmitOrderFail(model)
	}

	if err != nil {
		glog.Error(model.Order.ReceiverPhone, "，调用MtSubmitOrder失败，", err, "，参数：", kit.JsonEncode(AddOrder))
		if strings.Contains(err.Error(), "已超秒杀活动商品下单上限，可选择其它活动商品") {
			return r.NewHTTPError(400, "已超秒杀活动商品下单上限，可选择其它活动商品")
		}
		return r.NewHTTPError(400, "提交订单失败，请稍后重试")
	}

	if grpcRes.Code != 200 {
		glog.Error(model.Order.ReceiverPhone, "，======MtSubmitOrder失败=========，", grpcRes.Error)
		if strings.Contains(grpcRes.Message, "库存不足") {
			out.Code = 4004
			reg, _ := regexp.Compile(`\d+`)
			//skuId := reg.FindString(grpcRes.Message)
			skuIds := reg.FindAllString(grpcRes.Message, -1)
			out.CannotProducts = make([]*models.CannotSumbitProduct, 0)

			for _, skuId := range skuIds {
				//多个组合商品存在相同sku且该sku库存不足时随机移除一个组合商品
				//组合商品和单商品存在相同sku且该sku库存不足时优先提示移除单商品
				var stockLessSku string //库存不足的sku
				for _, v := range AddOrder.OrderProductModel {
					if skuId != v.Sku {
						continue
					}
					//单商品sku库存不足直接跳出返回给前端
					if len(v.ParentSkuId) == 0 {
						stockLessSku = v.Sku
						break
					}
					stockLessSku = v.ParentSkuId
				}
				mod := models.CannotSumbitProduct{}
				mod.SkuId = stockLessSku
				mod.Status = 1
				out.CannotProducts = append(out.CannotProducts, &mod)
			}

			////多个组合商品存在相同sku且该sku库存不足时随机移除一个组合商品
			////组合商品和单商品存在相同sku且该sku库存不足时优先提示移除单商品
			//var stockLessSku string //库存不足的sku
			//for _, v := range AddOrder.OrderProductModel {
			//	if skuId != v.Sku {
			//		continue
			//	}
			//	//单商品sku库存不足直接跳出返回给前端
			//	if len(v.ParentSkuId) == 0 {
			//		stockLessSku = v.Sku
			//		break
			//	}
			//	stockLessSku = v.ParentSkuId
			//}
			//out.CannotProducts = []*models.CannotSumbitProduct{
			//	{
			//		SkuId:  stockLessSku,
			//		Status: 1,
			//	},
			//}
			out.Message = grpcRes.Message
			glog.Info("=========提交的返回参数===========", kit.JsonEncode(out))
			return c.JSON(200, out)
		}

		outMsg := "提交订单失败，" + grpcRes.Message
		code := 400
		if strings.Contains(grpcRes.Message, "账号存在异常") {
			outMsg = grpcRes.Message
			code = 4005
		}
		return c.JSON(200, &struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}{Code: code, Message: outMsg})
	}

	glog.Info("==Promotionids==========")
	//5:锁限时折扣活动单日库存
	var Promotionids []string
	for _, item := range model.OrderPromotions {
		if item.PromotionType == 2 { //限时折扣活动
			PromotionIdStr := strconv.Itoa(int(item.PromotionId))
			Promotionids = append(Promotionids, PromotionIdStr)
		}
	}

	glog.Info("==len(Promotionids)==========")
	if len(Promotionids) > 0 {
		out.Code, out.Message = newTimeDiscountLockDayLimit(model, timeDiscountLimitCounts, grpcRes.OrderSn)
		if out.Code != 200 {
			glog.Error("限时折扣活动锁单日库存失败，" + out.Message + "，" + model.Order.ReceiverPhone)
			return r.NewHTTPError(400, out.Message)
		}
	}

	glog.Info("===============successful=============")
	out.Code = 200
	out.OrderSn = grpcRes.OrderSn
	out.PaySn = grpcRes.PaySn
	out.OrderId = grpcRes.OrderId
	//判定时候需要加上推送腾讯有数的标识
	// todo 配置订单类型不推送有数
	orderTypeStr := config.GetString("Not_Push_TencentSrdata")
	orderTypeStrNew := fmt.Sprintf(",%s,", orderTypeStr)
	if isPush && (model.Order.ChannelId == ChannelMallId && !strings.Contains(orderTypeStrNew, fmt.Sprintf(",%d,", model.Order.OrderType)) || model.Order.ChannelId == ChannelAwenId || model.Order.ChannelId == ChannelDigitalHealth) {
		orderRes := getOrderMainInfo(grpcRes.OrderSn)
		glog.Info("记录下根据订单号反查的订单信息参数：", kit.JsonEncode(grpcRes), ";返回结果：", kit.JsonEncode(orderRes))
		if orderRes.Code == 200 {
			//增加一个非空判断
			if orderRes.Order != nil {
				if len(orderRes.Order.CreateTime) > 0 {
					// ======= 将时间字符串转换为时间戳 =======
					stamp, _ := time.ParseInLocation("2006-01-02 15:04:05", orderRes.Order.CreateTime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
					out.CreateTime = stamp.UnixNano() / 1e6
					openId = val
					//本地生活的订单没有订单号
					if model.Order.ChannelId == ChannelAwenId || model.Order.ChannelId == ChannelDigitalHealth {
						model.Order.OldOrderSn = grpcRes.OrderSn
					}
					glog.Info("腾讯有数提交订单请求参数：", kit.JsonEncode(model))
					go AddOrdersToTencent(model, AddOrder, openId, out.CreateTime)
				} else {
					glog.Info("腾讯有数提交订单请求参数 err1：", kit.JsonEncode(orderRes.Order.CreateTime))
				}
			} else {
				glog.Info("腾讯有数提交订单请求参数  err2：", kit.JsonEncode(orderRes.Order))
			}
		} else {
			glog.Info("腾讯有数提交订单请求参数  err3：", kit.JsonEncode(orderRes))
		}
	}

	//如果是广告点击的则推送转化数据
	if len(clickId) > 0 {
		mpRequest := oc.AddAdvertisementMpRecordRequest{
			OrderSn:    grpcRes.OrderSn,
			UserId:     memberInfo.Id,
			ActionType: 2,
			Url:        "",
			ClickId:    clickId,
			//目前只有广告渠道只有WEB
			UserAgent:  5,
			ActionTime: time.Now().Local().Unix(),
			ChannelId:  cast.ToInt32(channel_id),
			OrderType:  model.Order.OrderType,
		}
		//ocClient := oc.GetOrderServiceClient()
		go AddAdvertisementMpRecord(&mpRequest)
	}

	//}

	return c.JSON(200, out)

}

// 社区团购相关验证
// 1、先判断团是否到结束时间，2、成团结束时间未到，可继续参团，3、团长能参加自己的团
func communityGroupValidate(groupId int64, order *models.Order, userAgent string) (*oc.CommunityGroupDetailResponse, error) {
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	group, err := ocClient.CG.Detail(ocClient.Ctx, &oc.CommunityGroupDetailRequest{
		Id: cast.ToInt32(groupId),
	})
	if err != nil {
		return group, err
	}
	if group.Code != 200 {
		return group, errors.New(group.Message)
	}
	if group.Data.Time <= 0 {
		return group, errors.New("当前团购活动已结束")
	}
	// 团长代收相关信息判断，1、必须使用团长代收地址，2、必须填写自己的配送地址
	if group.Data.FinalTakeType == 1 {
		if group.Data.ReceiverAddress != order.ReceiverAddress {
			return group, errors.New("已开启团长代收，请选择团长代收地址")
		}
		if order.ExtraInfo.GroupName == "" || order.ExtraInfo.GroupMobile == "" || (order.ExtraInfo.GroupAddress == "" && userAgent != "1" && userAgent != "2") {
			return group, errors.New("我的收货地址，姓名、电话、地址均为必填")
		}
		if len([]rune(order.ExtraInfo.GroupName)) > 15 {
			return group, errors.New("我的收货地址,姓名过长，请修改")
		}
	}

	return group, nil
}

func AddAdvertisementMpRecord(mpRequest *oc.AddAdvertisementMpRecordRequest) {
	glog.Info("添加广告MP记录请求参数：", mpRequest.OrderSn, kit.JsonEncode(mpRequest))
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	for index := 1; index < 5; index++ {
		res, err := ocClient.MP.AddAdvertisementMpRecord(context.Background(), mpRequest)
		if err != nil {
			glog.Error("添加广告MP记录异常："+mpRequest.OrderSn, err.Error())
		}
		if res != nil {
			if res.Code == 200 {
				return
			} else {
				glog.Error("添加广告MP记录失败："+mpRequest.OrderSn, kit.JsonEncode(res))
			}
		}

	}
}

func getOrderMainInfo(orderSn string) *oc.GetOneOrderResponse {
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.GetOneOrderRequest{
		OrderSn: orderSn,
	}
	glog.Info("根据订单号查询订单信息，参数：", kit.JsonEncode(in))
	orderRes, err := ocClient.RPC.GetOneOrder(ocClient.Ctx, in)
	glog.Info("根据订单号查询订单信息，参数：", kit.JsonEncode(in), ";返回结果：", kit.JsonEncode(orderRes))
	if err != nil {
		glog.Error(orderSn, ", 调用GetOneOrder失败, ", err)
		orderRes.Code = 400
		orderRes.Message = err.Error()
		return orderRes
		//return nil
	}
	return orderRes
}

// 推送订单到腾讯有数
func AddOrdersToTencent(model *models.OrderSubmitRequest, mtAddOrderReq *oc.MtAddOrderRequest, openId string, createTime int64) {
	defer func() {
		if err := recover(); err != nil {
			glog.Error("AddOrdersToTencent有数推送 -- 腾讯有数订单推送错误", err)
		}
	}()
	//productOrderSn := model.Order.OldOrderSn
	//if model.Order.ChannelId != 5 {
	//	productOrderSn = mtAddOrderResponse.OrderSn
	//}
	//ocClient := oc.GetOrderServiceClient()
	//var createTime int64
	//in := &oc.GetOneOrderRequest{
	//	OrderSn: productOrderSn,
	//}
	//order, err := ocClient.RPC.GetOneOrder(ocClient.Ctx, in)
	//glog.Info("有数推送 -- 订单号：", kit.JsonEncode(order))
	//if err != nil {
	//	glog.Error("有数推送 -- 调用GetOneOrder失败，", err, "，参数：", kit.JsonEncode(in), "，订单信息：", kit.JsonEncode(order))
	//	return
	//}
	//if order.Code != 200 {
	//	return
	//}
	//if len(order.Order.CreateTime) > 0 {
	//	// ======= 将时间字符串转换为时间戳 =======
	//	stamp, _ := time.ParseInLocation("2006-01-02 15:04:05", order.Order.CreateTime, time.Local) //使用parseInLocation将字符串格式化返回本地时区时间
	//	createTime = stamp.UnixNano() / 1e6
	//} else {
	//	glog.Error("有数推送 --  调用AddOrdersToTencent失败，", "，参数：", kit.JsonEncode(model))
	//	return
	//}
	//
	////获取订单商品信息
	//productIn := &oc.GetOneOrderRequest{
	//	OrderSn: productOrderSn,
	//}
	//orderProduct, err := ocClient.RPC.GetOrderProducts(ocClient.Ctx, productIn)
	//glog.Info("有数推送 -- 获取商品信息：", kit.JsonEncode(orderProduct))
	//if err != nil {
	//	glog.Error("有数推送 -- 调用GetOrderProducts失败，", err, "，参数：", kit.JsonEncode(productIn))
	//	return
	//}

	var goods []*ext.TenGoodsInfo
	for _, v := range mtAddOrderReq.OrderProductModel {
		goodsInfo := &ext.TenGoodsInfo{
			ExternalSkuId:   v.Sku,
			PrimaryImageUrl: v.Image,
			SkuNameChinese:  v.ProductName,
			GoodsAmount:     float32(kit.FenToYuan(v.Price)),       //float32(kit.FenToYuan(int64(v.Price)))  //商品原价
			PaymentAmount:   float32(kit.FenToYuan(v.SkuPayTotal)), //float32(kit.FenToYuan(int64(v.DiscountPrice * v.Number))), //商品折扣价
			ExternalSpuId:   v.Sku,
			SpuNameChinese:  v.ProductName,
			GoodsNum:        int64(v.Number),
		}
		goods = append(goods, goodsInfo)
	}

	//主订单每种支付方式的支付信息 payment_info
	var paymentInfo []*ext.TenPaymentInfo
	payment := &ext.TenPaymentInfo{
		PaymentType: "00009",
		TransId:     model.Order.OldOrderSn,
		TransAmount: float32(kit.FenToYuan(int64(model.Order.GoodsTotal + model.Order.Freight))),
	}
	paymentInfo = append(paymentInfo, payment)

	TOOrder := &ext.TencentOrders{
		ExternalOrderId:  model.Order.OldOrderSn,
		CreateTime:       cast.ToString(createTime),
		OrderSource:      "wxapp",
		OrderType:        1,
		GoodsNumTotal:    int64(len(model.OrderProducts)),
		GoodsAmountTotal: float32(kit.FenToYuan(int64(model.Order.GoodsTotal))),
		FreightAmount:    float32(kit.FenToYuan(int64(model.Order.Freight))),
		OrderAmount:      float32(kit.FenToYuan(int64(model.Order.GoodsTotal + model.Order.Freight))),
		PayableAmount:    float32(kit.FenToYuan(int64(model.Order.Total))),
		PaymentAmount:    float32(kit.FenToYuan(int64(model.Order.Total))),
		OrderStatus:      "1110",
		StatusChangeTime: cast.ToString(createTime),
		UserInfo: &ext.TenUserInfo{
			OpenId:             openId,
			AppId:              config.GetString("xcx-app-id"),
			UserFirstOrderTime: strconv.FormatInt(time.Now().UnixNano()/1e6, 10),
		},
		GoodsInfo:   goods,
		PaymentInfo: paymentInfo,
	}

	var orders []*ext.TencentOrders
	orders = append(orders, TOOrder)
	request := ext.AddOrdersToTencentReq{
		Orders: orders,
	}
	glog.Info("有数推送 -- 腾讯有数提交订单请求参数：", kit.JsonEncode(request))
	extClient := ext.GetExternalTencentClient()
	defer extClient.Close()
	result, _ := extClient.RPC.AddOrdersToTencent(extClient.Ctx, &request)
	glog.Info("有数推送 -- 腾讯有数提交订单请求参数：", kit.JsonEncode(request), "；返回结果：", kit.JsonEncode(result))
	if result.Code != 0 {
		glog.Error("有数推送 -- 腾讯有数订单推送失败，" + result.Error + "，" + kit.JsonEncode(request))
		return
	}
}

// @Summary 订单取消
// @Tags 订单
// @Param AwenOrderCancleRequest body oc.AwenOrderCancleRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/order/cancle [post]
func AwenOrderCancle(c echo.Context) error {
	model := new(oc.AwenOrderCancleRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.AwenOrderCancleRequest{
		OrderId:      model.OrderId,
		CancelReason: model.CancelReason,
	}
	grpcRes, err := ocClient.RPC.AwenOrderCancle(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用AwenOrderCancle失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}
	//推送退款订单到腾讯有数
	//go AddReturnOrderToTencent(model.OrderId)

	return c.JSON(200, grpcRes)
}

// 推送退款订单到腾讯有数
func AddReturnOrderToTencent(orderSn string) {
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.GetOneOrderRequest{
		OrderSn: orderSn,
	}
	//查询退款订单商品信息
	product, err := ocClient.RPC.GetRefundOrderProducts(ocClient.Ctx, in)
	if err != nil || product.Code != 200 || len(product.RefundOrderProducts) == 0 {
		glog.Error("调用GetRefundOrderProducts失败，", err, "，参数：", kit.JsonEncode(in))
		return
	}

	var refundSn string
	var goods []*ext.ReturnGoodsInfo
	var returnFreightAmount float64 //退款运费金额
	refundTotalAmount := 0          //退款金额

	for _, v := range product.RefundOrderProducts {
		refundSn = v.RefundSn
		refundAmount, _ := strconv.ParseFloat(v.RefundAmount, 64)
		returnFreightAmount, _ = strconv.ParseFloat(v.Freight, 64)
		refundTotalAmount += kit.YuanToFen(refundAmount)
		goodsInfo := &ext.ReturnGoodsInfo{
			ExternalSkuId:     v.SkuId,
			SkuNameChinese:    v.ProductName,
			IsGift:            0,
			ExternalSpuId:     v.SkuId,
			SpuNameChinese:    v.ProductName,
			ReturnGoodsNum:    v.Tkcount,
			ReturnGoodsAmount: float32(refundAmount),
		}
		goods = append(goods, goodsInfo)
	}

	returnOrders := &ext.TencentReturnOrders{
		ExternalReturnOrderId: refundSn,
		ExternalOrderId:       orderSn,
		ReturnCreateTime:      strconv.FormatInt(time.Now().UnixNano()/1e6, 10),
		ReturnNum:             int64(len(goods)),
		ReturnAmount:          float32(kit.FenToYuan(refundTotalAmount)),
		ReturnFreightAmount:   float32(returnFreightAmount),
		ReturnOrderAmount:     float32(kit.FenToYuan(refundTotalAmount + kit.YuanToFen(returnFreightAmount))),
		ReturnOrderStatus:     "1290",
		StatusChangeTime:      strconv.FormatInt(time.Now().UnixNano()/1e6, 10),
		ReturnGoodsInfo:       goods,
	}

	var orders []*ext.TencentReturnOrders
	orders = append(orders, returnOrders)

	request := ext.AddReturnOrderToTencentReq{
		Orders: orders,
	}

	extClient := ext.GetExternalTencentClient()
	defer extClient.Close()

	result, _ := extClient.RPC.AddReturnOrderToTencent(extClient.Ctx, &request)
	if result.Code != 0 {
		glog.Error("腾讯有数退款订单推送失败，" + result.Error + "，" + kit.JsonEncode(request))
	}
}

// @Summary 物流公司列表
// @Tags 订单物流
// @Accept json
// @Produce json
// @Param page_size  query int true "每页显示数量"
// @Param page  query int true "当前页码"
// @Param keyword  query string false "物流公司名称"
// @Success 200 {object} oc.ExpressCompanyListResponse
// @Failure 400 {object} oc.ExpressCompanyListResponse
// @Router /order-api/order/expresscompanylist [get]
func ExpressCompanyList(c echo.Context) error {
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.ExpressCompanyListRequest{
		Page:     cast.ToInt32(c.QueryParam("page")),
		PageSize: cast.ToInt32(c.QueryParam("page_size")),
		Keyword:  c.QueryParam("keyword"),
	}
	grpcRes, err := ocClient.RPC.ExpressCompanyList(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用ExpressCompanyList失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, grpcRes)
}

// @Summary 物流信息更新
// @Tags 订单物流
// @Param ExpressInfoUpdateRequest body oc.ExpressInfoUpdateRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/order/expressinfoupdate [post]
func ExpressInfoUpdate(c echo.Context) error {
	model := new(oc.ExpressInfoUpdateRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	model.ExpressInfo = memberInfo.Name
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	grpcRes, err := ocClient.RPC.ExpressInfoUpdate(ocClient.Ctx, model)
	if err != nil {
		glog.Error("调用ExpressInfoUpdate失败，", err, "，参数：", kit.JsonEncode(model))
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, grpcRes)
}

// @Summary 物流路由对接
// @Tags 订单物流
// @Accept json
// @Produce json
// @Param model query oc.ExpressInfoRequest  true " "
// @Success 200 {object} oc.ExpressInfoResponse
// @Failure 400 {object} oc.ExpressInfoResponse
// @Router /order-api/order/expressinfo [get]
func ExpressInfo(c echo.Context) error {
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.ExpressInfoRequest{
		ExpressNo:  c.QueryParam("express_no"),
		OrderSn:    c.QueryParam("order_sn"),
		RefundSn:   c.QueryParam("refund_sn"),
		SearchType: c.QueryParam("search_type"),
	}

	if in.SearchType == "1" {
		memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
		if !ok || memberInfo == nil {
			return r.NewHTTPError(400, "用户不存在")
		}
	}

	grpcRes, err := ocClient.RPC.ExpressInfo(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用ExpressInfo失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, grpcRes)
}

// @Summary 获取美团骑手当前所在的地址
// @Tags 订单物流
// @Accept json
// @Produce json
// @Param ExpressInfoUpdateRequest body oc.RiderLocationRequest true " "
// @Success 200 {object} oc.ExternalResponse
// @Failure 400 {object} oc.ExternalResponse
// @Router /order-api/order/rider-location/get [get]
func GetOrderRiderLocation(c echo.Context) error {
	model := new(oc.RiderLocationRequest)

	model.DeliveryId = cast.ToInt64(c.QueryParam("delivery_id_str"))
	model.DeliveryServiceCode = cast.ToInt32(c.QueryParam("delivery_service_code"))
	if model.DeliveryServiceCode <= 0 {
		return r.NewHTTPError(400, "delivery_service_code 必传")
	}

	model.MtPeisongId = c.QueryParam("mt_peisong_id")
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	grpcRes, err := ocClient.RPC.MpOrderRiderLocation(ocClient.Ctx, model)
	if err != nil {
		glog.Error("调用MpOrderRiderLocation失败，", err, "，参数：", kit.JsonEncode(model))
		return r.NewHTTPError(400, err.Error())
	}
	return c.JSON(200, grpcRes)

}

// @Summary 预售订单提醒尾款支付订阅消息
// @Tags 订单
// @Accept plain
// @Produce json
// @Param mallPushPreSaleRequest body models.MallPushPreSaleMessageRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/order/pre-sale/push-message [post]
func MallPushPreSaleMessage(c echo.Context) error {
	out := new(oc.BaseResponse)
	out.Code = 400
	model := new(models.MallPushPreSaleMessageRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	glog.Info("MallPushPreSaleMessage 预售订单消息：请求参数", kit.JsonEncode(model))
	ocClient := GetOrderServiceClient()

	defer ocClient.Close()

	pushRequest := new(oc.PushTemplateRequest)
	pushRequest.OrderSn = model.OrderSn
	pushRequest.PushType = 4
	pushRequest.PreSalePay = &oc.PreSalePay{
		StartTime: model.SendMessage.StartTime,
		EndTime:   model.SendMessage.EndTime,
		Remarks:   model.SendMessage.Remarks,
		IsVirtual: model.SendMessage.IsVirtual,
	}

	// 获取主体标识
	orgId := c.Request().Header.Get("org_id")
	if len(orgId) == 0 {
		orgId = "1"
	}
	pushRequest.OrgId = cast.ToInt32(orgId)

	rpcRes, err := ocClient.SM.PushTemplate(ocClient.Ctx, pushRequest)
	glog.Info("MallPushPreSaleMessage 推送订阅消息返回：", rpcRes)
	if err != nil {
		glog.Error("MallPushPreSaleMessage 推送订阅消息异常:", err)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("MallPushPreSaleIntegral 推送订阅消息失败:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// @Summary 预售订单定金积分
// @Tags 订单
// @Accept plain
// @Produce json
// @Param mallPushPreSaleRequest body models.MallPushPreSaleIntegralRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/order/pre-sale/push-integral [post]
func MallPushPreSaleIntegral(c echo.Context) error {
	out := new(oc.BaseResponse)
	out.Code = 400
	model := new(models.MallPushPreSaleIntegralRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	glog.Info("MallPushPreSale 预售积分消息：请求参数", kit.JsonEncode(model))
	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	//当PayType == 0时，退定金积分
	//当PayType == 1时，加定金积分
	pushIntegralRequest := new(oc.PushIntegralRequest)
	pushIntegralRequest.OrderSn = model.OrderSn
	pushIntegralRequest.PayPrice = model.PayPrice
	pushIntegralRequest.PayType = model.PayType
	glog.Info("MallPushPreSaleIntegral 预售积分消息：推送积分", kit.JsonEncode(pushIntegralRequest))
	rpcRes, err := ocClient.RPC.PushIntegral(ocClient.Ctx, pushIntegralRequest)
	if err != nil {
		glog.Error("MallPushPreSaleIntegral 推送积分异常:", err)
		out.Message = "推送失败"
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("MallPushPreSaleIntegral 推送积分失败:", rpcRes.Message)
		out.Message = rpcRes.Message
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// @Summary 重推OMS，有无库存都可以调用
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.PushOmsRequest body models.PushOmsRequest true " "
// @Success 200 {object} oc.BaseResponse
// @Failure 400 {object} oc.BaseResponse
// @Router /order-api/order/re-push-oms [post]
func RePushOms(c echo.Context) error {
	out := new(oc.BaseResponse)
	out.Code = 400
	model := new(models.PushOmsRequest)
	if err := c.Bind(model); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	glog.Info("重新推送OMS：请求参数", kit.JsonEncode(model))
	pushIntegralRequest := new(oc.MtAddOrderResponse)
	pushIntegralRequest.OrderSn = model.OrderSn
	pushIntegralRequest.PaySn = model.PaySn

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()
	glog.Error("RePushOms 重推OMS入参:", pushIntegralRequest)
	rpcRes, err := ocClient.RPC.RePushOms(ocClient.Ctx, pushIntegralRequest)
	glog.Error("RePushOms 重推OMS入参:", pushIntegralRequest, "结果", rpcRes)
	if err != nil {
		glog.Error("RePushOms 重推OMS异常:", err)
		out.Error = "推送失败"
		return c.JSON(http.StatusBadRequest, out)
	}
	if rpcRes.Code != http.StatusOK {
		glog.Error("RePushOms 重推OMS异常:", rpcRes.Error)
		out.Error = rpcRes.Error
		return c.JSON(http.StatusBadRequest, out)
	}
	out.Code = 200
	return c.JSON(http.StatusOK, out)
}

// GetVerifyCodeByOldOrderSn
// @Summary 根据第三方订单号查询核销码信息-v6.0
// @Tags 虚拟订单核销
// @Accept plain
// @Accept json
// @Produce json
// @Param pageIndex query int false "当前多少页 从1开始 不传默认为1 "
// @Param pageSize query int false "每页多少条数据 不传默认为15"
// @Param orderSn query string true "第三方（美团 京东 饿了么）订单号"
// @Param productName query string false "商品名称（支持模糊搜索）"
// @Param verifyStatus query int false "核销码状态 -1 所有 0未核销, 1已核销, 2已退款 不传默认为0 "
// @Success 200 {object} models.GetVerifyCodeByOldOrderSnRes
// @Failure 400  {object} models.GetVerifyCodeByOldOrderSnRes
// @Router /order-api/order/verify-codes [GET]
func GetVerifyCodeByOldOrderSn(c echo.Context) error {
	var (
		out    models.GetVerifyCodeByOldOrderSnRes //返回参数
		rpcRes *oc.GetVerifyCodesResponse          //rpc请求结果
		err    error
	)
	out.StatusCode = http.StatusBadRequest
	request := new(models.GetVerifyCodeByOldOrderSnReq)

	rpcRequestParam := new(oc.GetVerifyCodesRequest)

	if err = c.Bind(request); err != nil {
		out.Message = "参数解析失败"
		return c.JSON(http.StatusBadRequest, out)
	}
	if request.OrderSn == "" {
		out.Message = "第三方订单号参数不能为空"
		return c.JSON(http.StatusBadRequest, out)
	}

	jsonRpcRequestParam := kit.JsonEncode(rpcRequestParam)
	rpcRequestParam.OldOrderSn = request.OrderSn
	rpcRequestParam.PageIndex = request.PageIndex
	rpcRequestParam.PageSize = request.PageSize
	rpcRequestParam.VerifyStatus = request.VerifyStatus
	rpcRequestParam.ProductName = request.ProductName

	glog.Info("GetVerifyCodeByOldOrderSn rpc rpcRequestParam:", jsonRpcRequestParam)
	client := GetOrderServiceClient()
	defer client.Close()

	if rpcRes, err = client.RPC.GetVerifyCodes(client.Ctx, rpcRequestParam); err != nil {
		glog.Error("GetGroupBuyList rpc error:", err, jsonRpcRequestParam)
		out.Message = "获取失败"
		return c.JSON(http.StatusBadRequest, out)
	}

	if rpcRes.Code != http.StatusOK {
		glog.Error("GetGroupBuyList rpc fail:", rpcRes.Message, jsonRpcRequestParam)
		out.Message = "获取失败"
		return c.JSON(http.StatusBadRequest, out)
	}

	out.Total = rpcRes.Total
	out.StatusCode = http.StatusOK
	out.Message = "获取成功"

	for _, v := range rpcRes.Data {
		item := new(models.VerifyCodes)
		item.VerifyCode = v.VerifyCode
		item.VerifyCodeExpiryDate = v.VerifyCodeExpiryDate
		item.OrderSn = v.OrderSn
		item.VerifyShop = v.VerifyShop
		item.VerifyTime = v.VerifyTime
		item.VerifyMemberId = v.VerifyMemberId
		item.VerifyStatus = v.VerifyStatus

		item.OrderStatus = v.OrderStatus
		item.MemberId = v.MemberId
		item.CreateTime = v.CreateTime
		item.PayTime = v.PayTime
		item.Total = v.Total
		item.PayMode = v.PayMode
		item.SkuId = v.SkuId
		item.ThirdSkuId = v.ThirdSkuId
		item.PayPrice = v.PayPrice
		item.Image = v.Image
		item.ProductName = v.ProductName
		item.Number = v.Number
		item.PaymentTotal = v.PaymentTotal
		item.MarkingPrice = v.MarkingPrice
		out.Result = append(out.Result, item)
	}

	return c.JSON(http.StatusOK, out)
}

// StandardPay
// @Summary 订单标准支付
// @Tags 订单
// @Accept json
// @Produce json
// @Param models.PayRequest body models.PayRequest true " "
// @Success 200 {object} models.StandardPayResponse
// @Failure 400 {object} models.StandardPayResponse
// @Router /order-api/order/pay/standard [post]
func StandardPay(c echo.Context) error {
	var res models.StandardPayResponse
	params := new(models.PayRequest)
	if err := c.Bind(params); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()
	in := &oc.GetOneOrderRequest{
		OrderSn:   params.OrderNo,
		OrderType: params.OrderType,
	}
	orderPayInfo, err := ocClient.RPC.GetOrderPayInfo(ocClient.Ctx, in)
	if err != nil {
		glog.Error(params.OrderNo, ", 调用GetOrderPayInfo失败, ", err)
		return r.NewHTTPError(400, err.Error())
	}
	payInfo := new(models.StandardPayRequest)
	// v6.23.0
	payInfo = &models.StandardPayRequest{
		AppId:        params.AppId,
		ClientIp:     utils.GetClientIp(),
		Discount:     orderPayInfo.Privilege,
		MerchantId:   orderPayInfo.MerchantId,
		NotifyURL:    orderPayInfo.NotifyUrl,
		OrderName:    "商品套餐-" + params.OrderNo,
		OrderNo:      params.OrderNo,
		OrderPayType: orderPayInfo.OrderPayType,
		PayAmount:    orderPayInfo.PayPrice,
		PayTotal:     orderPayInfo.PayPrice + orderPayInfo.Privilege,
		Sign:         "",
		SubAppId:     orderPayInfo.SubAppId,
		ValidTime:    15,
		Timestamp:    time.Now().UnixNano() / 1e6,
		ProductID:    "RP0000001",
		ProductDesc:  "商品描述",
	}

	if params.OrderType == 4 {
		payInfo.ValidTime = 5
	}

	sign, err := utils.PaySign(payInfo, orderPayInfo.SecretKey)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	payInfo.Sign = sign
	res.Code = 200
	res.Data = payInfo
	return c.JSON(200, res)
}

// OrderStepLog
// @Summary 下单前浏览链路统计
// @Tags 订单
// @Accept json
// @Produce json
// @Param dac.OrderStepLogRequest body dac.OrderStepLogRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router /order-api/order/step-log [post]
func OrderStepLog(c echo.Context) error {
	in := new(dac.OrderStepLogRequest)
	if err := c.Bind(in); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	in.Type = "1"

	// scrm数据接收
	var scrmStep dto.OrderStepScrm
	json.Unmarshal([]byte(in.Step), &scrmStep)
	if cast.ToInt(scrmStep.ChannelCode) > 0 || cast.ToInt(scrmStep.ActivityCode) > 0 { // scrm收集不需要单号
		in.Type = "2"
	} else if in.OrderSn == "" {
		return r.NewHTTPError(400, "订单号参数缺失")
	}

	client := dac.GetDataCenterClient()
	out, err := client.RPC.OrderStepLog(client.Ctx, in)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, out)
}

// OrderSubscribeLog
// @Summary 提交订单后收集订阅消息
// @Tags 订单
// @Accept json
// @Produce json
// @Param dac.OrderSubscribeLogRequest body dac.OrderSubscribeLogRequest true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router /order-api/order/subscribe-log [post]
func OrderSubscribeLog(c echo.Context) error {
	in := new(dac.OrderSubscribeLogRequest)
	if err := c.Bind(in); err != nil {
		return r.NewHTTPError(400, err.Error())
	}
	if in.OrderSn == "" {
		return r.NewHTTPError(400, "订单号参数缺失")
	}

	client := dac.GetDataCenterClient()
	out, err := client.RPC.OrderSubscribeLog(client.Ctx, in)
	if err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	return c.JSON(200, out)
}

// @Summary 新订单链路值保存
// @Tags 订单
// @Accept json
// @Produce json
// @Param model body dac.OrderLinkStoreReq true " "
// @Success 200 {object} dac.Response
// @Failure 400 {object} dac.Response
// @Router /order-api/order/link [post]
func OrderLinkStore(c echo.Context) error {
	in := new(dac.OrderLinkStoreReq)
	if err := c.Bind(in); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	if len(in.OrderSn) == 0 {
		return r.NewHTTPError(400, "订单号不能为空")
	}

	in.ScrmId = cast.ToString(c.Get("scrm_id"))
	if len(in.ScrmId) == 0 {
		return r.NewHTTPError(400, "用户未登录")
	}

	client := dac.GetDataCenterClient()
	if out, err := client.RPC.OrderLinkStore(client.Ctx, in); err != nil {
		return r.NewHTTPError(400, err.Error())
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 检查是否开过处方单
// @Tags 订单
// @Accept json
// @Produce json
// @Param model body oc.OrderPrescribeCheckReq true " "
// @Success 200 {object} oc.OrderPrescribeCheckRes
// @Failure 400 {object} oc.OrderPrescribeCheckRes
// @Router /order-api/order/prescribe-check [POST]
func OrderPrescribeCheck(c echo.Context) error {
	req := new(oc.OrderPrescribeCheckReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.OrderPrescribeCheckRes{Code: 400, Message: err.Error()})
	}

	req.ScrmId = cast.ToString(c.Get("scrm_id"))
	if len(req.ScrmId) == 0 {
		return c.JSON(401, &oc.OrderPrescribeCheckRes{Code: 401, Message: "用户未登录"})
	}

	if len(req.Skus) == 0 {
		return c.JSON(400, &oc.OrderPrescribeCheckRes{Code: 400, Message: "药品不能为空"})
	}
	if len(req.FinanceCode) == 0 {
		return c.JSON(400, &oc.OrderPrescribeCheckRes{Code: 400, Message: "财务编码不能为空"})
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.PrescribeCheck(client.Ctx, req); err != nil {
		return c.JSON(400, &oc.OrderPrescribeCheckRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}

// @Summary 开处方单
// @Tags 订单
// @Accept json
// @Produce json
// @Param model body models.OrderPrescribeReq true " "
// @Success 200 {object} oc.OrderPrescribeRes
// @Failure 400 {object} oc.OrderPrescribeRes
// @Router /order-api/order/prescribe [POST]
func OrderPrescribe(c echo.Context) error {
	req := new(models.OrderPrescribeReq)
	if err := c.Bind(req); err != nil {
		return c.JSON(400, &oc.OrderPrescribeRes{Code: 400, Message: err.Error()})
	}
	//校验参数
	if err := c.Validate(req); err != nil {
		err := validate.Translate(err)
		return r.NewHTTPError(400, err.One())
	}

	if len(req.Skus) == 0 {
		return c.JSON(400, &oc.OrderPrescribeRes{Code: 400, Message: "药品不能为空"})
	}
	if len(req.Diagnose) == 0 {
		return c.JSON(400, &oc.OrderPrescribeRes{Code: 400, Message: "诊断信息不能为空"})
	}
	if req.PetInfo.PetId == "" {
		return c.JSON(400, &oc.OrderPrescribeRes{Code: 400, Message: "宠物信息不能为空"})
	}
	req.PetInfo.MemberId = cast.ToString(c.Get("scrm_id"))
	if len(req.PetInfo.MemberId) == 0 {
		return c.JSON(401, &oc.OrderPrescribeRes{Code: 401, Message: "宠物信息不能为空"})
	}
	params := &oc.OrderPrescribeReq{
		FinanceCode:  req.FinanceCode,
		HospitalName: req.HospitalName,
		PetWeight:    req.PetWeight,
		PetInfo:      req.PetInfo,
		Diagnose:     req.Diagnose,
		Skus:         req.Skus,
	}

	client := oc.GetOrderServiceClient()
	if out, err := client.RPC.Prescribe(client.Ctx, params); err != nil {
		return c.JSON(400, &oc.OrderPrescribeRes{Code: 400, Message: err.Error()})
	} else {
		return c.JSON(int(out.Code), out)
	}
}
