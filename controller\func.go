package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	kit "github.com/tricobbler/rp-kit"
	"math"
	"net/http"
	"order-api/models"
	"order-api/proto/dac"
	"order-api/proto/mk"
	"order-api/proto/oc"
	"order-api/proto/pc"
	"order-api/utils"
	"strconv"
	"strings"
	"time"

	"github.com/maybgit/glog"
	"github.com/spf13/cast"
)

//整理退款单状态
func formatRefundStatus(refundType, refundState int32, expressnNum string) (status int32, statusText string, canRevoke int32) {
	//退款订单状态映射，根据refund_order表的refundType和refund_state组合判断
	//refundType = 1    退款
	//status
	//1	等待商家审核
	//2	已撤销
	//3	退款成功
	//
	//refundType = 2    退货
	//status
	//1	等待商家审核
	//2	已撤销
	//3	退货退款成功
	//4	等待买家退货
	//5	买家已退货
	statusMap := map[int32]map[int32]string{
		1: {
			1: "等待商家审核",
			2: "已撤销",
			3: "退款成功",
			8: "退款失败",
		}, //退款
		2: {
			1: "等待商家审核",
			2: "已撤销",
			3: "退货退款成功",
			4: "等待买家退货",
			5: "买家已退货",
		}, //退货
	}

	if refundType == 1 {
		//退款
		status = refundState
		if status == 9 {
			status = 2
		}
	} else {
		//退货
		status = refundState

		if status == 6 {
			//买家已发货
			if len(expressnNum) > 0 {
				status = 5
			} else {
				status = 4
			}
		}

		if status == 7 {
			status = 3
		}
		if status == 9 {
			status = 2
		}
	}

	statusText = statusMap[refundType][status]

	//如果是退款，在商家审核完成前可撤销；如果是退货，在买家退货前可撤销
	if status == 1 || (refundType == 2 && status == 4) {
		canRevoke = 1
	}

	return
}

//整理退款流程日志
func formatRefundLog(out *models.RefundDetail, in *oc.OrderRetrunInfo) {
	//反转退款流程，接口是按时间倒序返回，这里需要正序
	for i, j := 0, len(in.Loglist)-1; i < j; i, j = i+1, j-1 {
		in.Loglist[i], in.Loglist[j] = in.Loglist[j], in.Loglist[i]
	}

	//日志流程文案映射关系
	logDescMap := getLogDescMap()

	out.RefundLogs = append(out.RefundLogs, models.RefundLog{
		Title: "退款申请已提交",
		Time:  in.Loglist[0].Ctime,
		Desc:  "退款原因：" + in.Loglist[0].Reason,
	})

	if in.Refundtype == 1 {
		//退款
		if in.RefundState == 1 {
			t, _ := time.ParseInLocation("2006-01-02 15:04:05", in.Createtime, time.Local)
			sub := t.Unix() + 12*3600 - time.Now().Unix()
			//退款申请审核时间3小时
			if sub > 0 {
				out.RemainingTime = int32(sub)
			}
		}

		//整理退款流程
		for k, v := range in.Loglist {
			log := models.RefundLog{
				Title: logDescMap[in.Refundtype][v.NotifyType].Title,
				Time:  v.Ctime,
				Desc:  logDescMap[in.Refundtype][v.NotifyType].Desc,
			}

			if len(log.Title) == 0 {
				continue
			}

			//如果申请状态不是最后的状态，将描述隐藏
			if v.NotifyType == "apply" && k != len(in.Loglist)-1 {
				log.Desc = ""
			}
			if v.NotifyType == "apply" && v.OperationType == "商家申请售后单" {
				out.RefundLogs = []models.RefundLog{}
				log.Title = "商家发起退款"
				log.Desc = "退款原因：" + v.Reason
			}
			if v.NotifyType == "reject" || v.NotifyType == "first-reject" {
				log.Desc = "拒绝原因：" + v.Reason
			}
			if v.NotifyType == "agree" && (v.OperationType == "商家取消订单" || v.OperationType == "商家后台取消" || v.OperationType == "取消订单") {
				out.RefundLogs = []models.RefundLog{}
				log.Title = "发起取消订单申请"
				log.Desc = "系统审核通过后将为您退款"
			}
			if v.NotifyType == "agree" && v.OperationType == "超过3小时自动同意" {
				log.Title = "商家未处理"
				log.Desc = ""
			}

			out.RefundLogs = append(out.RefundLogs, log)

			logAfter := models.RefundLog{
				Time: v.Ctime,
			}

			if v.NotifyType == "apply" && v.OperationType == "商家申请售后单" {
				logAfter.Title = "支付账户受理退款"
				logAfter.Desc = "您的退款已被受理"
				out.RefundLogs = append(out.RefundLogs, logAfter)
				break
			}
			if v.NotifyType == "agree" {
				if v.OperationType == "超过3小时自动同意" || v.OperationType == "商家取消订单" || v.OperationType == "商家后台取消" || v.OperationType == "取消订单" {
					logAfter.Title = "系统自动审核通过"
					logAfter.Desc = "已将退款申请提交至原支付账户"
					out.RefundLogs = append(out.RefundLogs, logAfter)
				}
				logAfter.Title = "支付账户受理退款"
				logAfter.Desc = "您的退款已被受理"
			}
			if v.NotifyType == "reject" || v.NotifyType == "first-reject" {
				logAfter.Title = "退款已撤销"
			}

			if len(logAfter.Title) == 0 {
				continue
			}

			out.RefundLogs = append(out.RefundLogs, logAfter)
		}
	} else {
		//退货
		if in.RefundState == 1 {
			t, _ := time.ParseInLocation("2006-01-02 15:04:05", in.Createtime, time.Local)
			sub := t.Unix() + 24*3600 - time.Now().Unix()
			//退货申请审核时间24小时
			if sub > 0 {
				out.RemainingTime = int32(sub)
			}
		} else if in.RefundState == 6 {
			t, _ := time.ParseInLocation("2006-01-02 15:04:05", in.Createtime, time.Local)
			sub := t.Unix() + 7*24*3600 - time.Now().Unix()
			//退货申请审核成功后，买家退货时间7天
			if sub > 0 {
				out.RemainingTime = int32(sub)
			}
		}

		//整理退款流程
		for k, v := range in.Loglist {
			logBefore := models.RefundLog{
				Time: v.Ctime,
			}

			if v.NotifyType == "cancelRefund" && v.OperationType == "超时未处理系统自动取消" {
				logBefore.Title = "买家未退货"
				out.RefundLogs = append(out.RefundLogs, logBefore)
			}

			log := models.RefundLog{
				Title: logDescMap[in.Refundtype][v.NotifyType].Title,
				Time:  v.Ctime,
				Desc:  logDescMap[in.Refundtype][v.NotifyType].Desc,
			}

			if len(log.Title) == 0 {
				continue
			}

			//如果申请状态不是最后的状态，将描述隐藏
			if (v.NotifyType == "apply" || v.NotifyType == "returnExpress") && k != len(in.Loglist)-1 {
				log.Desc = ""
			}
			if v.NotifyType == "first-reject" || v.NotifyType == "reject" {
				log.Desc = "拒绝原因：" + v.Reason
			}
			if v.NotifyType == "apply" && v.OperationType == "商家申请售后单" {
				out.RefundLogs = []models.RefundLog{}
				log.Title = "商家发起退款"
				log.Desc = "退款原因：" + v.Reason
			}
			if v.NotifyType == "agree" && (v.OperationType == "商家取消订单" || v.OperationType == "商家后台取消" || v.OperationType == "取消订单") {
				out.RefundLogs = []models.RefundLog{}
				log.Title = "发起取消订单申请"
				log.Desc = "系统审核通过后将为您退款"
			}
			if v.NotifyType == "agree" && v.OperationType == "超过3小时自动同意" {
				log.Title = "商家未处理"
			}

			out.RefundLogs = append(out.RefundLogs, log)

			logAfter := models.RefundLog{
				Time: v.Ctime,
			}
			if v.NotifyType == "apply" && v.OperationType == "商家申请售后单" {
				logAfter.Title = "支付账户受理退款"
				logAfter.Desc = "您的退款已被受理"
				out.RefundLogs = append(out.RefundLogs, logAfter)
				break
			}
			if v.NotifyType == "first-agree" {
				logAfter.Title = "等待买家退货"
				logAfter.Desc = "请将退货商品快递给商家"
				//不是最后状态时隐藏描述
				if k != len(in.Loglist)-1 {
					logAfter.Desc = ""
				}
			}
			if v.NotifyType == "agree" {
				if v.OperationType == "超过3小时自动同意" || v.OperationType == "商家取消订单" || v.OperationType == "商家后台取消" || v.OperationType == "取消订单" {
					logAfter.Title = "系统自动审核"
					logAfter.Desc = "已将退款申请提交至原支付账户"
					out.RefundLogs = append(out.RefundLogs, logAfter)
				}
				logAfter.Title = "支付账户受理退款"
				logAfter.Desc = "您的退款已被受理"
			}
			if v.NotifyType == "first-reject" || v.NotifyType == "reject" {
				logAfter.Title = "退款已撤销"
			}

			if len(logAfter.Title) == 0 {
				continue
			}

			out.RefundLogs = append(out.RefundLogs, logAfter)
		}
	}

	//返回给前端是按时间倒序排列
	for i, j := 0, len(out.RefundLogs)-1; i < j; i, j = i+1, j-1 {
		out.RefundLogs[i], out.RefundLogs[j] = out.RefundLogs[j], out.RefundLogs[i]
	}
}

//skuId转productId
func skuIdToProductId(skuId []int32) map[int32]int32 {
	client := pc.GetDcChannelProductClient()
	defer client.Close()

	if res, err := client.RPC.GetProductIdBySkuId(client.Ctx, &pc.GetProductIdBySkuIdRequest{
		SkuId: skuId,
	}); err != nil {
		glog.Error(err)
		return map[int32]int32{}
	} else {
		return res.Data
	}
}

//退款日志描述文案
func getLogDescMap() map[int32]map[string]models.RefundLog {
	return map[int32]map[string]models.RefundLog{
		1: {
			"apply": models.RefundLog{
				Title: "等待商家处理",
				Desc:  "商家同意后将退款到您的支付账户。如商家在买家发起申请退款后12小时未处理申请，系统将自动退款",
			},
			"reject": models.RefundLog{
				Title: "商家不同意退款",
			},
			"cancelRefund": models.RefundLog{
				Title: "退款已撤销",
			},
			"agree": models.RefundLog{
				Title: "商家同意退款",
				Desc:  "已将退款申请提交至原支付账户",
			},
		}, //退款
		2: {
			"apply": models.RefundLog{
				Title: "等待商家处理",
				Desc:  "商家同意后，请买家退货。如商家超过24小时未处理，系统将自动为您审核通过",
			},
			"first-reject": models.RefundLog{
				Title: "商家审核不通过",
			},
			"reject": models.RefundLog{
				Title: "商家不同意退款",
			},
			"first-agree": models.RefundLog{
				Title: "商家已审核",
			},
			"agree": models.RefundLog{
				Title: "商家同意退款",
				Desc:  "已将退款申请提交至原支付账户",
			},
			"returnExpress": models.RefundLog{
				Title: "买家已退货",
				Desc:  "等待商家收货后，审核并退款",
			},
			"cancelRefund": models.RefundLog{
				Title: "退款已撤销",
			},
		}, //退货
	}
}

//限时折扣活动商品是否超单日库存
func newCheckTimeDiscountLockDayLimit(model *models.OrderSubmitRequest, limits []*mk.PromotionProductLimitCountDto) (code int32, msg string) {
	code, msg = 200, ""

	redisClient := utils.NewRedisClient()
	defer redisClient.Close()

	for _, item := range limits {
		if item.LimitCountByStock <= 0 { //如果没有设置库存则跳出验证
			continue
		}

		for _, product := range model.OrderProducts {
			if item.ProductSkuId == product.Sku {
				if product.DiscountCount > 0 { //参与限时折扣的商品数量大于0
					date := time.Now().Format("20060102") //年月日格式
					//已使用
					lockKey := fmt.Sprintf("order:limitstockcount:%s:%d:%s:%s", model.Order.ShopId, item.PromotionId, date, item.ProductSkuId)
					usedcount, _ := strconv.Atoi(redisClient.Get(lockKey).Val()) //单日下单已使用数量

					if int(item.LimitCountByStock-product.DiscountCount) < usedcount { //可用库存量（单日库存量-已用库存量）<本此参与折扣的商品数量
						code = 400
						msg = "商品参与折扣活动的当日可用库存不足或已发生变化"
						return code, msg
					}
				}
			}
		}
	}

	return code, msg
}

//限时折扣活动锁单日库存
//限时折扣活动，一个店铺可能存在多个限时折扣，但一个折扣活动只能对应一个商品
func newTimeDiscountLockDayLimit(model *models.OrderSubmitRequest, limits []*mk.PromotionProductLimitCountDto, OrderSn string) (code int32, msg string) {
	code, msg = 200, ""

	//redis锁定操作
	redisClient := utils.NewRedisClient()
	defer redisClient.Close()
	for _, item := range limits {
		for _, product := range model.OrderProducts {
			if item.ProductSkuId == product.Sku {
				if product.DiscountCount > 0 { //参与限时折扣的商品数量大于0
					date := time.Now().Format("20060102") //年月日格式
					//已使用
					lockKey := fmt.Sprintf("order:limitstockcount:%s:%d:%s:%s", model.Order.ShopId, item.PromotionId, date, item.ProductSkuId)
					usedcount, _ := strconv.Atoi(redisClient.Get(lockKey).Val()) //单日下单已使用数量
					usedcount += int(product.DiscountCount)
					redisClient.Set(lockKey, usedcount, time.Hour*24)
					//if r := redisClient.SetNX(lockKey, usedcount, time.Hour*24); !r.Val() {
					//	glog.Error(fmt.Sprintf("未获取到redis单日库存锁,key：%s"), lockKey)
					//}

					//订单限时折扣使用的数量
					orderLockKey := fmt.Sprintf("order:limitstockcount:%s:%d:%s:%s:%s", model.Order.ShopId, item.PromotionId, date, item.ProductSkuId, OrderSn)
					redisClient.Set(orderLockKey, product.DiscountCount, time.Minute*30)
				}
			}
		}
	}

	return code, msg
}

//验证订单
//i:订单常规验证：金额大于0，商品数量大于0
//小程序验证一下步骤，电商单不需要
//ii:限时折扣活动单日库存验证
//iii:下单金额，运费金额是否变化
//iii:是否在配送范围
//iiii:店铺设置是否营业状态
func checkOrder(model *models.OrderSubmitRequest, member *models.MemberInfo, userAgent string) (code int32, msg string, timeDiscountLimitCounts []*mk.PromotionProductLimitCountDto) {
	//i:订单支付金额小于0，订单商品数量小于0不给下单
	if model.Order.Total <= 0 && model.Order.OrderType != 19 {
		return 400, "异常单:订单支付金额小于等于0", nil
	}
	if len(model.OrderProducts) <= 0 {
		return 400, "异常单:订单未购买任何商品", nil
	}

	if model.Order.ChannelId != ChannelAwenId && model.Order.ChannelId != ChannelDigitalHealth {
		//商城活动验证
		//v2.9.10
		if model.Order.ChannelId == ChannelMallId {
			//TODO  下个版本让前端传类型为12过来 之前秒杀的订单类型定为9  与周期购冲突了  发了热修版本fix-0831 改为12 仅从后端做了处理
			if model.Order.OrderType == 9 && len(model.OrderProducts) > 0 && model.OrderProducts[0].PromotionType == 11 {
				model.Order.OrderType = 12
			}

			//普通商城订单参数检测
			checkRes, checkMsg, err := UPetOrderCheck(model)
			if err != nil {
				glog.Error(model.Order.ReceiverPhone, "checkOrder-UPetOrderCheck-fail 商城活动订单参数检测失败"+checkMsg, err, kit.JsonEncode(model))
				return 400, checkMsg, nil
			}
			if !checkRes {
				return 400, checkMsg, nil
			}

			checkRes, checkMsg, err = UPetActivityCheck(model)
			if err != nil {
				glog.Error(model.Order.ReceiverPhone, "checkOrder-UPetActivityCheck-fail 商城活动订单检测失败"+checkMsg, err, kit.JsonEncode(model))
				return 400, checkMsg, nil
			}
			if !checkRes {
				return 400, checkMsg, nil
			}
			//通过之后 高并发下可以做限流

		}
		return 200, "", timeDiscountLimitCounts
	}

	//以下为到家相关验证，电商与其他渠道不需要不
	//ii:判断参与限时折扣商品是否超过单日库存数量
	var PromotionIds []string
	for _, item := range model.OrderPromotions {
		if item.PromotionType == 2 { //限时折扣活动
			PromotionIdStr := strconv.Itoa(int(item.PromotionId))
			PromotionIds = append(PromotionIds, PromotionIdStr)
		}
	}
	var skuids []string
	for _, item := range model.OrderProducts {
		skuids = append(skuids, item.Sku)
	}
	if len(PromotionIds) > 0 { //没有参与限时折扣，跳出检查
		mkClient1 := mk.GetTimeDiscountServiceClient()
		defer mkClient1.Close()

		in := &mk.QueryLimitCountRequest{
			Shopid:       model.Order.ShopId,
			Promotionids: strings.Join(PromotionIds, ","),
			Skuids:       strings.Join(skuids, ","),
		}
		limitRes, err := mkClient1.TimeDiscount.QueryLimitCountBySkus(mkClient1.Ctx, in)
		if err != nil {
			glog.Error("调用QueryLimitCountBySkus失败，", err, "，参数：", kit.JsonEncode(in))
			return 400, "查询是否参与限时折扣异常：" + err.Error(), nil
		}

		timeDiscountLimitCounts = limitRes.PromotionLimits
		code, msg = newCheckTimeDiscountLockDayLimit(model, timeDiscountLimitCounts)
		if code == 400 {
			return 400, msg, nil
		}
	}

	//iii:下单金额，运费金额是否变化
	//活动中心
	promotionProducts := make([]*mk.PromotionCalcProductDto, 0)
	for _, item := range model.OrderProducts {
		promotionProducts = append(promotionProducts, &mk.PromotionCalcProductDto{
			SkuId:    item.Sku,
			SumMoney: int64(item.Price * item.Number),
			Price:    int64(item.Price),
			Count:    int64(item.Number),
		})
	}
	mkClient := mk.GetMarketingCenterClient()
	defer mkClient.Close()

	in := &mk.PromotionCalcRequest{
		ShopId:           model.Order.ShopId,
		ChannelId:        cast.ToInt32(model.Order.ChannelId),
		DestinationX:     model.Order.Longitude,
		DestinationY:     model.Order.Latitude,
		PromotionProduct: promotionProducts,
		UserId:           member.ScrmUserId,
	}
	//获取减免信息
	promotionReduceRes, err := mkClient.RPC.CalcPromotionMoney(mkClient.Ctx, in)
	if err != nil {
		glog.Error(model.Order.ReceiverPhone, "，调用CalcPromotionMoney失败，", err, "，参数：", kit.JsonEncode(in))
		return 400, "获取优惠信息异常，请稍后重试", nil
	}

	//todo 许成鹏 需要修改code编码
	if promotionReduceRes.ActualMoneyByMinUnit != model.Order.GoodsTotal || (promotionReduceRes.ReduceMoneyByMinUnit) != model.Order.Privilege {
		return 4007, "订单应付金额或优惠金额已更新", nil
	}

	//验证商品参与活动：如果有满减活动或者限时折扣活动，但是前端传的商品未绑定活动类型和活动id，则补全
	glog.Info("阿闻商城订单：CalcPromotionMoney返回结果:" + kit.JsonEncode(promotionReduceRes))
	checkProductMap := make(map[string]*mk.PromotionCalcProductDto)
	for _, p := range promotionReduceRes.PromotionProduct {
		checkProductMap[p.SkuId] = p
	}
	for _, product := range model.OrderProducts {
		if promotionCalcProduct, ok := checkProductMap[product.Sku]; ok {
			product.DiscountCount = int32(promotionCalcProduct.DiscountCount)
			product.DiscountPrice = promotionCalcProduct.DiscountPrice
			product.VipPrice = promotionCalcProduct.OnlyVipDiscountPrice
			product.PromotionType = promotionCalcProduct.PromotionType
			product.PromotionId = promotionCalcProduct.PromotionId
			product.Price = int32(promotionCalcProduct.Price) //用计算后的价格
		}
	}

	products := make([]*oc.UpetDjMoneyCalcProductDto, 0)
	for _, item := range model.OrderProducts {
		products = append(products, &oc.UpetDjMoneyCalcProductDto{
			SkuId: item.Sku,
			Count: item.Number,
		})
	}

	//验证配送范围
	//数据中心
	dcClient := dac.GetDataCenterClient()

	//非自提单
	if model.Order.OrderType != 3 {
		if promotionReduceRes.UpetActualDjMoneyByMinUnit != model.Order.Freight {
			return 400, "订单运费已改变，请重新下单", nil
		}

		//add by csf修改bug:原来的运费多少，前端取的是优惠后的运费，自己取总运费
		model.Order.Freight = promotionReduceRes.UpetDjMoneyByMinUnit

		glog.Info("阿闻商城订单：获取配送范围，", model.Order.ReceiverPhone)
		var canOrder = false
		if model.Order.OrgId == 6 {
			in := &dac.SaaSPolygonRequest{
				FinancialCode: model.Order.ShopId,
				Lat:           cast.ToString(model.Order.Latitude),
				Lng:           cast.ToString(model.Order.Longitude),
			}
			//是否在配送范围内
			esRes, err := dcClient.RPC.IsPointInPolygon(dcClient.Ctx, in)
			if err != nil || esRes == nil {
				glog.Error(model.Order.ReceiverPhone, "，调用IsPointInPolygon失败，", err, "，参数：", kit.JsonEncode(in))
				return 4001, "配送范围查询异常，请稍后重试", nil
			}
			if esRes.Code != 200 {
				glog.Error(model.Order.ReceiverPhone, "，调用IsPointInPolygon失败，", "，参数：", kit.JsonEncode(in), " 返回：", esRes)
				return 4001, "配送范围查询异常，请稍后重试", nil
			}
			canOrder = esRes.IsIn

		} else {
			in := &dac.EsSearchRequest{
				LonLat:    cast.ToString(model.Order.Longitude) + "," + cast.ToString(model.Order.Latitude),
				Type:      "geo_shape",
				PageIndex: 1,
				PageSize:  1000,
			}
			//是否在配送范围内
			esRes, err := dcClient.RPC.QueryEsStorePointShape(dcClient.Ctx, in)
			if err != nil || esRes == nil {
				glog.Error(model.Order.ReceiverPhone, "，调用QueryEsStorePointShape失败，", err, "，参数：", kit.JsonEncode(in))
				return 4001, "配送范围查询异常，请稍后重试", nil
			}

			if esRes.Code == 200 && len(esRes.Hits.Hits) > 0 {
				for _, item := range esRes.Hits.Hits {
					if item.XId == model.Order.ShopId {
						canOrder = true
						break
					}
				}
			}
		}
		if canOrder == false {
			return 400, "收货地址不在配送范围内", nil
		}
		glog.Info("阿闻商城订单：店铺信息，", model.Order.ReceiverPhone)
	}

	// 店铺是否打烊
	deliveryServiceReq := &dac.ShopDeliveryServiceListRequest{
		ChannelId:    cast.ToInt32(model.Order.ChannelId),
		FinanceCodes: model.Order.ShopId,
	}
	deliveryRes, err := dcClient.RPC.ShopDeliveryServiceList(dcClient.Ctx, deliveryServiceReq)
	if err != nil {
		glog.Error(model.Order.ReceiverPhone, "，调用ShopDeliveryServiceList失败，", err, "，参数：", kit.JsonEncode(deliveryServiceReq))
		return 400, "查询门店服务状态异常，请稍后重试", nil
	}
	if deliveryRes.DataList != nil {
		if deliveryRes.DataList[0].AdvanceorderStatus == 2 && model.Order.OrderType != 3 {
			//orderType = 2
		}

		if len(userAgent) > 0 && cast.ToInt32(userAgent) == 3 && deliveryRes.DataList[0].IsSelfLiftingApp == false && model.Order.OrderType == 3 {
			return 4008, "店铺关闭了自提服务", nil
		}

		if len(userAgent) > 0 && cast.ToInt32(userAgent) == 7 && deliveryRes.DataList[0].IsSelfLifting == false && model.Order.OrderType == 3 {
			return 4008, "店铺关闭了自提服务", nil
		}

		if deliveryRes.DataList[0].AdvanceorderStatus == 3 {
			return 4002, "店铺已打烊", nil
		}

		if deliveryRes.DataList[0].AdvanceorderStatus == 4 {
			return 4003, "店铺已闭店", nil
		}
	}

	return 200, "", timeDiscountLimitCounts
}

//小程序均摊(实付金额，商品优惠，sku金额)及生成订单
func JunTan(model *models.OrderSubmitRequest) (code int32, msg string, addOrder *oc.MtAddOrderRequest, failProductList []*models.CannotSumbitProduct) {
	var (
		err                         error
		allPrivilegeAmount          int32 //所有活动的总优惠金额
		freightPrivilegeAmount      int32 //总运费优惠金额
		allCombinePrivilegeAmount   int32 //所有组合优惠
		manJanPromotionProductCount int32 //记录参与满减活动的商品个数
		totalMoneyManJan            int32 //满减商品总金额
		privilegeManJan             int32 //满减商品总优惠金额，用于计算最后一个商品的均摊优惠金额（总优惠-最后一个商品之前的累计的优惠金额=最后一个商品的优惠金额）
		skuPayTotalPull             int32 //累计订单所有sku实付金额，用于计算最后一个sku商品的实际支付金额（订单总的实付金额-最后一个商品之前的累计的实付金额=最后一个商品的实付金额）-- 满减适用
	)

	//zilongId := cast.ToInt32(utils.HashGet("store:relation:dctozl", model.Order.ShopId))
	//if zilongId == 0 {
	//	glog.Error("下单未找到店铺子龙id, ", model.Order.ReceiverPhone)
	//	return 400, "店铺信息查询异常，请稍后重试", nil, nil
	//}
	//1：通用下单
	addOrder = model.Order.TransToProtoData()

	glog.Info("阿闻商城订单：Sku信息，", model.Order.ReceiverPhone)
	//商品ID集合
	productIDs := make([]int32, len(model.OrderProducts))
	//满减商品总金额 = 满减商品的数量*相应的商品价格
	skuIDs := make([]int32, len(model.OrderProducts))
	for i, item := range model.OrderProducts {
		productIDs[i] = cast.ToInt32(item.ProductId)
		skuIDs[i] = cast.ToInt32(item.Sku)
		if item.PromotionType == 1 {
			if item.VipPrice > 0 {
				totalMoneyManJan += item.Number * item.VipPrice
			} else {
				totalMoneyManJan += item.Number * item.Price
			}
			manJanPromotionProductCount++
		}
	}

	glog.Info("阿闻商城订单：查询商品信息，", model.Order.ReceiverPhone)

	//商品中心 查询商品信息
	pcClient := pc.GetDcProductClient()
	defer pcClient.Close()

	//检测spu是否存在
	storeProductReq := &pc.ChannelStoreProductRequest{
		ChannelId:   cast.ToInt32(model.Order.ChannelId),
		FinanceCode: []string{model.Order.ShopId},
		//ProductId:   productIDs,
		SkuId:       skuIDs,
		UpDownState: -1,
	}
	var storeProductRes *pc.ChannelStoreProductResponse
	if model.Order.OrgId == 6 {
		storeProductReq.ProductId = productIDs
		storeProductRes, err = pcClient.RPC.QuerySaaSChannelStoreProduct(pcClient.Ctx, storeProductReq)
	} else {
		storeProductRes, err = pcClient.RPC.QueryChannelStoreProduct(pcClient.Ctx, storeProductReq)
	}

	if err != nil {
		glog.Error(model.Order.ReceiverPhone, "，调用QueryChannelStoreProduct失败，", err, "，参数：", kit.JsonEncode(storeProductReq))
		return 400, "商品信息查询异常，请稍后重试", nil, nil
	}
	if len(storeProductRes.Details) == 0 {
		return 400, "商品信息不存在", nil, nil
	}

	//todo v6.0 快照改版
	productSnapsIds := make([]int32, len(storeProductRes.Details))
	var porductIds []int32
	for i, item := range storeProductRes.Details {
		if item.UpDownState == 0 { //下架状态
			failProductList = append(failProductList, &models.CannotSumbitProduct{Status: 3, SkuId: cast.ToString(item.SkuId)})
		}
		productSnapsIds[i] = item.SnapshotId
		porductIds = append(porductIds, item.ProductId)
	}

	if len(failProductList) > 0 {
		return 4004, "下单失败，存在无货、下架商品", nil, failProductList
	}

	in2 := &pc.ChannelProductSnapshotRequest{
		ChannelId: cast.ToInt32(model.Order.ChannelId),
		Ids:       productSnapsIds,
	}

	var skuRes *pc.ChannelProductSnapshotResponse
	//商品与快照的map todo v6.0 使用skuid 代替productId 下个版本快照支持skuId时再改
	skuProducts := make(map[int32]pc.ChannelProductSnapshot)

	if model.Order.OrgId == 6 {
		skuRes, err = querySaaSChannelProductSnapshot(pcClient, model.Order.ChannelId, model.Order.ShopId, porductIds)
		if err != nil {
			glog.Error(model.Order.ReceiverPhone, "，调用querySaaSChannelProductSnapshot，", err, "，参数：", model.Order.ChannelId, model.Order.ShopId, productSnapsIds)
			return 400, "查询商品信息异常，请稍后重试", nil, nil
		}
	} else {
		//订单商品信息处理 查询快照信息 todo v6.0 快照改版
		skuRes, err = pcClient.RPC.QueryChannelProductSnapshot(pcClient.Ctx, in2)
		if err != nil {
			glog.Error(model.Order.ReceiverPhone, "，调用QueryChannelProductSnapshot失败，", err, "，参数：", kit.JsonEncode(in2))
			return 400, "查询商品信息异常，请稍后重试", nil, nil
		}

	}

	for _, v := range skuRes.Details {
		if _, ok := skuProducts[v.ProductId]; !ok {
			skuProducts[v.ProductId] = *v
		}
	}

	//从订单参与的活动提取活动信息
	orderPromotions := make([]*oc.OrderPromotionModel, 0)
	for _, item := range model.OrderPromotions {
		if model.Order.OrderType == 3 && item.PromotionType == 3 {
			//自提单没有满减运费
			continue
		}
		orderPromotions = append(orderPromotions, item.TransToProtoData())
		//加总总的优惠金额以及总的运费优惠金额
		allPrivilegeAmount += item.PromotionFee
		if item.PromotionType == 3 {
			freightPrivilegeAmount += item.PromotionFee
		}
	}

	glog.Info("阿闻商城订单：Sku处理，", model.Order.ReceiverPhone)

	orderProducts := make([]*oc.OrderProductModel, 0)
	//统计订单折扣总金额
	for _, item := range model.OrderProducts {
		product, ok := skuProducts[cast.ToInt32(item.ProductId)]
		if !ok {
			failProductList = append(failProductList, &models.CannotSumbitProduct{
				SkuId:  item.Sku,
				Status: 2,
			})
			return 4005, "商品信息不存在，下单失败", nil, failProductList
		}

		//解码快照并检测sku 与第三方sku是否正确 todo v6.0 不再使用json_data
		productSnapshotResponse := models.ProductSnapshotResponse{}
		err = json.Unmarshal([]byte(product.JsonData), &productSnapshotResponse)
		if err != nil {
			glog.Error("json解析失败，", err, "，json：", product.JsonData)
			return 400, "下单失败，商品" + item.ProductId + "快照信息异常", nil, nil
		}

		if len(productSnapshotResponse.SkuInfo) == 0 || (len(productSnapshotResponse.SkuInfo) > 0 && len(productSnapshotResponse.SkuInfo[0].SnapsThirdSku) == 0) {
			glog.Error("商品第三方Sku信息不存在，" + model.Order.ReceiverPhone)
			return 400, "下单失败，商品第三方Sku信息不存在", nil, nil
		}
		//商品价格取快照内的MarketPrice，不取用户传的
		//v 6.0添加 此处需要支持多sku的逻辑 只获取相同skuId的价格 不能使用proDuctId判断
		var skuExist bool
		//插入价格 此处重置的价格 如果是组合商品 则这个价格来自于子商品的价格
		for _, v := range productSnapshotResponse.SkuInfo {
			strSkuId := cast.ToString(v.SkuId)
			if strSkuId != item.Sku {
				continue
			}
			item.Price = v.MarketPrice
			skuExist = true
			if len(item.ChildProductList) == 0 {
				break
			}
			//子商品价格与商品类型也进行一次修正
			//有子商品 但是快照里没有子商品的快照信息
			if len(v.SkuGroup) == 0 {
				glog.Error("组合商品子商品快照信息不存在，" + model.Order.ReceiverPhone)
				return 400, "下单失败，组合商品子商品信息不存在", nil, nil
			}
			groupSkuMap := make(map[string]*models.SkuGroup)
			for _, k := range v.SkuGroup {
				strGroupSku := cast.ToString(k.GroupSkuId)
				groupSkuMap[strGroupSku] = k
			}

			for m, n := range item.ChildProductList {
				if groupSkuInfo, gok := groupSkuMap[n.Sku]; gok {
					item.ChildProductList[m].Price = groupSkuInfo.MarketPrice
					item.ChildProductList[m].ProductType = groupSkuInfo.ProductType
				}
			}
		}
		//目前前端没有传组合商品的组合类型 后端取
		item.CombineType = productSnapshotResponse.Product.GroupType

		if !skuExist {
			glog.Error("商品信息中不存在sku信息，"+model.Order.ReceiverPhone, item.Sku)
			return 400, "下单失败，sku信息不存在", nil, nil
		}
		//item.Price = productSnapshotResponse.SkuInfo[0].MarketPrice

		discountProduct := *item.TransToProtoData()

		//拷贝一份 拷贝不能拷贝指针类型否则 改动会互相影响 discountProduct只能事值类型
		orderProduct := discountProduct

		price := item.Price
		if item.VipPrice > 0 {
			price = item.VipPrice
		}

		//均摊逻辑：满减运费和限时折扣活动不参与均摊，只有满减活动参与均摊
		var skuPayTotal int32
		//todo 不同活动的均摊 抽象出来 以方便扩展与维护
		if item.PromotionType == 1 { //满减活动 可能叠加付费会员价格
			var payPrice int32  //优惠后的单价
			var privilege int32 //商品优惠价格

			if manJanPromotionProductCount == 1 {
				n := cast.ToFloat64(model.Order.Privilege-privilegeManJan) / cast.ToFloat64(item.Number-item.DiscountCount)
				payPrice = price - cast.ToInt32(n)
				if price <= cast.ToInt32(n) { //add by csf保证不能小于等于0
					payPrice = 1 //????? 等于1 ？？？？ 0.01元
				}
				privilege = model.Order.Privilege - privilegeManJan
				//实付金额 = 满减商品总金额 - 之前满减商品的支付总金额 - 总优惠
				skuPayTotal = totalMoneyManJan - skuPayTotalPull - model.Order.Privilege
			} else if manJanPromotionProductCount > 1 {
				//计算当前 item.Number-item.DiscountCount 成立的前提条件是 所有的商品仅参加限时折扣与满减
				//model.Privilege只是满减活动的优惠，同个商品即参与了满减 也参与了限时折扣
				//当前商品金额 / 总的满减金额 * 优惠总价
				//向上取整 得到商品的总优惠价格
				n := math.Ceil(cast.ToFloat64(price*(item.Number-item.DiscountCount)) / cast.ToFloat64(totalMoneyManJan) * cast.ToFloat64(model.Order.Privilege))
				//优惠后的单价 = 原单价 - 单个优惠价
				payPrice = price - cast.ToInt32(n/cast.ToFloat64(item.Number-item.DiscountCount))
				//优惠价格
				privilege = cast.ToInt32(n)
				privilegeManJan += privilege //加总满减的优惠

				if privilegeManJan > model.Order.Privilege { //add by csf@累计的优惠金额不能超过实际的优惠总金额
					privilegeManJan = model.Order.Privilege
					privilege = privilege - (privilegeManJan - model.Order.Privilege) //如果上次已超出，则本商品需要减去已超出的优惠金额
				}
				//该商品的实际支付价格 = 原价 - 优惠
				skuPayTotal += price*item.Number - privilege
			}
			manJanPromotionProductCount-- //减去均摊活动的次数

			orderProduct.PayPrice = payPrice
			orderProduct.Privilege = privilege + item.Price - price
			orderProduct.PrivilegeTotal = orderProduct.Privilege
			orderProduct.PaymentTotal = skuPayTotal //支付金额
			skuPayTotalPull += skuPayTotal          //记录实付金额
		} else if item.PromotionType == 4 {
			orderProduct.Number = item.Number
			orderProduct.PayPrice = item.DiscountPrice //折后价
			orderProduct.PaymentTotal = item.Number * item.DiscountPrice
			orderProduct.Privilege = item.Number * (item.Price - item.DiscountPrice)
			orderProduct.PrivilegeTotal = orderProduct.Privilege
			skuPayTotal += orderProduct.PaymentTotal //记录实付金额
		} else if item.PromotionType == 2 { //折扣活动
			//购买的商品数量超过限制的折扣数量
			if item.DiscountCount > 0 { //如果有参与限时折扣活动的商品，则不参与均摊
				discountProduct.Number = item.DiscountCount
				discountProduct.PayPrice = item.DiscountPrice //折后价
				discountProduct.PaymentTotal = item.DiscountCount * item.DiscountPrice
				discountProduct.Privilege = item.DiscountCount * (item.Price - item.DiscountPrice)
				discountProduct.PrivilegeTotal = discountProduct.Privilege
				skuPayTotal += discountProduct.PaymentTotal //记录实付金额
			}
			//如果还有未打折商品-走原价处理
			if item.Number-item.DiscountCount > 0 {
				orderProduct.Number = item.Number - item.DiscountCount
				orderProduct.PayPrice = price
				orderProduct.PaymentTotal = orderProduct.Number * price
				orderProduct.Privilege = (item.Price - price) * orderProduct.Number
				orderProduct.PrivilegeTotal = orderProduct.Privilege
				orderProduct.PromotionType = 4
				skuPayTotal += orderProduct.PaymentTotal //记录实付金额
			}
		} else {
			//无任何活动的商品
			skuPayTotal += (item.Number) * item.Price
			orderProduct.PaymentTotal = (item.Number) * item.Price
			orderProduct.Privilege = 0
			orderProduct.PrivilegeTotal = 0
		}

		//最后处理保存订单的商品集合
		if item.DiscountCount > 0 {
			//限时折扣为什么单独处理处理呢 因为前端传过来的数据 限时折扣可能存在与其他活动重叠使用的情况 进行拆开
			discountProduct.SkuPayTotal = skuPayTotal //包含了满减的支付额
			discountProduct.PromotionType = 2
			orderProducts = append(orderProducts, &discountProduct)

			//如果此商品是组合商品，还要把里面的子商品拆分均摊好
			if item.ProductType == 3 && len(item.ChildProductList) > 0 {
				var combinePrivilege int32
				orderProducts, combinePrivilege = DealChildProduct(orderProducts, item.ChildProductList, &discountProduct)
				allCombinePrivilegeAmount += combinePrivilege
			}
		}

		// 可能会走到这里来
		if item.Number-item.DiscountCount > 0 {
			orderProduct.SkuPayTotal = skuPayTotal
			orderProducts = append(orderProducts, &orderProduct)

			//如果此商品是组合商品，还要把里面的子商品拆分均摊好
			if item.ProductType == 3 && len(item.ChildProductList) > 0 {
				var combinePrivilege int32
				orderProducts, combinePrivilege = DealChildProduct(orderProducts, item.ChildProductList, &orderProduct)
				allCombinePrivilegeAmount += combinePrivilege
			}
		}
	}

	//sku_pay_total重新统计 同个商品存在多条数据提交吗？
	skuPayTotalMap := make(map[string]int32, 0)
	for _, item := range orderProducts {
		skuPayTotalMap[item.Sku] += item.PaymentTotal
	}

	//sku_pay_total重新统计 将相同的sku的支付金额进行加总
	for _, item := range orderProducts {
		if _, ok := skuPayTotalMap[item.Sku]; ok {
			item.SkuPayTotal = skuPayTotalMap[item.Sku]
		}
	}

	glog.Info("阿闻商城订单：优惠活动处理，", model.Order.ReceiverPhone)

	addOrder.OrderProductModel = orderProducts
	addOrder.OrderPromotion = orderPromotions
	//所有组合商品优惠
	addOrder.CombinePrivilege = allCombinePrivilegeAmount
	//前端给过来的优惠金额没有包含限时折扣和满减运费活动的优惠金额，导致李强那边下单有问题，所以修改加上限时活动优惠，但是自提单是要去掉满减运费活动的
	addOrder.Privilege = allPrivilegeAmount
	//前端商品总金额GoodsTotal已加了运费并减去了优惠金额，商品总价要加上商品的除运费外的优惠
	//商品原应该付的金额
	addOrder.GoodsTotal = model.Order.GoodsTotal + allPrivilegeAmount - freightPrivilegeAmount
	//前端商品总金额已减去优惠金额,Privilege不包含满减运费和限时折扣的优惠 ？？
	//优惠后的总金额 优惠活动后的总金额 + 运费
	addOrder.Total = model.Order.GoodsTotal + model.Order.Freight - freightPrivilegeAmount

	return 200, msg, addOrder, nil
}

// 转写上面的方法
func querySaaSChannelProductSnapshot(client *pc.Client, channelId int32, financeCode string, productIds []int32) (*pc.ChannelProductSnapshotResponse, error) {
	out := new(pc.ChannelProductSnapshotResponse)
	out.Code = 400
	resp, err := client.RPC.GetEshopProductSnapshotBySpuOrSku(context.Background(), &pc.GetChannelProductSnapshotBySpuOrSkuRequest{
		ChannelId:   channelId,
		FinanceCode: financeCode,
		ProductId:   productIds,
	})
	if err != nil {
		glog.Errorf("rpc调用QueryChannelProductSnapshot异常,channelId:%d,financeCode:%s,productIds:%v,err:%+v", channelId, financeCode, productIds, err)
		return nil, err
	}

	if resp.Code != http.StatusOK {
		glog.Errorf("rpc调用QueryChannelProductSnapshot业务处理失败(%d),channelId:%d,financeCode:%s,productIds:%v,msg:%s,err:%s", resp.Code, channelId, financeCode, productIds, resp.Message, resp.Error)
		return nil, errors.New(resp.Message)
	}

	for _, v := range resp.Details {

		productSnapshot := pc.ChannelProductSnapshot{}
		productSnapshot.ProductId = v.Product.Id
		productSnapshot.ChannelId = channelId
		productSnapshot.FinanceCode = financeCode
		productSnapshotResponse := models.ProductSnapshotResponse{}
		// 创建 SnapsProduct 对象
		product := models.SnapsProduct{
			Pic:         v.Product.Pic,
			ProductType: v.Product.ProductType,
			GroupType:   v.Product.GroupType,
		}
		productSnapshotResponse.Product = product
		//补全SKU信息
		for _, itemSkuInfo := range v.SkuInfo {

			skuInfo := models.SnapshotSkuInfo{
				SkuId:       itemSkuInfo.SkuId,
				MarketPrice: itemSkuInfo.MarketPrice,
				BarCode:     itemSkuInfo.BarCode,
			}

			for _, itemSkuv := range itemSkuInfo.Skuv {
				skuv := models.SnapshotSkuv{
					Pic:       itemSkuv.Pic,
					SpecName:  itemSkuv.SpecName,
					SpecValue: itemSkuv.SpecValueValue,
				}
				skuInfo.SnapsSkuv = append(skuInfo.SnapsSkuv, skuv)
			}

			for _, itemSkuThird := range itemSkuInfo.SkuThird {
				thirdSku := models.SnapsSkuThird{
					ThirdSkuId: itemSkuThird.ThirdSkuId,
					ErpId:      2,
				}
				skuInfo.SnapsThirdSku = append(skuInfo.SnapsThirdSku, thirdSku)
			}
			//SAAS平台暂时没有组合商品
			skuInfo.SkuGroup = make([]*models.SkuGroup, 0)
			productSnapshotResponse.SkuInfo = append(productSnapshotResponse.SkuInfo, skuInfo)
		}
		josnbyt, err := json.Marshal(productSnapshotResponse)
		if err != nil {
			glog.Error("阿闻商品结构转SAAS失败", productSnapshotResponse)
		}
		productSnapshot.JsonData = string(josnbyt)
		out.Details = append(out.Details, &productSnapshot)
	}
	out.Code = 200
	return out, nil

}

//DealChildProduct 均摊子商品
//orderProducts:商品列表
//item：一级商品
//orderProduct：一级预生成折扣商品或原价商品
//返回此项组合商品组合优惠金额
func DealChildProduct(orderProducts []*oc.OrderProductModel, childProducts []*models.OrderProductModel, orderProduct *oc.OrderProductModel) ([]*oc.OrderProductModel, int32) {
	//均摊组合商品实付金额
	var (
		combineSum           int32                   //此组合商品的原总金额=(a子商品原价*a数量)+ (b子商品原价*b数量)
		combinePrivilege     int32                   //此组合商品总优惠金额=组合商品原总金额-组合商品实付金额
		combinePaymentTotal  int32                   //此组合商品总实付金额
		combineChildProducts []*oc.OrderProductModel //组合商品均摊好的商品列表
	)
	combinePaymentTotal = orderProduct.PaymentTotal //实付金额 子商品使用实际支付金额进行均摊

	//step 1:根据子商品 的优惠类型 得出每个子商品的优惠前的商品单价 构建均摊商品实体，且统计原总金额
	for _, combineProduct := range childProducts {
		combineChildNumber := combineProduct.Number * orderProduct.Number
		//2:创建均摊好的子商品
		combineChildProduct := combineProduct.TransToProtoDataChild(orderProduct)
		//计算子商品在组合商品内的优惠价格
		switch combineProduct.GroupDiscountType {
		case 1: //按折扣优惠
			//组合商品中的子商品价格四舍五入
			var discountPrice = decimal.NewFromInt(int64(combineChildProduct.Price)).
				Mul(decimal.NewFromInt(int64(combineProduct.GroupDiscountValue))).
				DivRound(decimal.NewFromInt(100), 0).IntPart()
			combineChildProduct.Price = int32(discountPrice) //折扣价 对应DiscountPrice
			combineChildProduct.PayPrice = combineChildProduct.Price
		case 2: //按固定价格优惠
			combineChildProduct.Price = combineProduct.GroupDiscountValue //组合价 对应DiscountPrice
			combineChildProduct.PayPrice = combineChildProduct.Price
		}

		combineChildProducts = append(combineChildProducts, combineChildProduct)

		combineSum += combineChildProduct.PayPrice * combineChildNumber //组合价*数量
	}

	//step 2 根据原价与实际支付价格 得出总的优惠 并均摊优惠到各个子商品 更新子商品的支付单价与优惠金额相关字段
	//此组合商品总优惠金额=组合商品原总金额 - 组合商品实付金额
	combinePrivilege = combineSum - combinePaymentTotal
	orderProduct.Privilege = combinePrivilege //组合商品的优惠金额
	orderProduct.PrivilegeTotal = combinePrivilege

	var sumItemAmount int32 //统计已减金额
	var sumVipPrice int32

	vipRate := decimal.NewFromInt32(orderProduct.VipPrice).Div(decimal.NewFromInt32(orderProduct.Price))

	for index, childProduct := range combineChildProducts {
		if orderProduct.PromotionType > 0 { //有活动，均摊组合商品，直接用组合商品的原组合价
			if len(combineChildProducts)-1 == index {
				//最后一个需要倒减
				childProduct.PaymentTotal = combinePaymentTotal - sumItemAmount
				if childProduct.PaymentTotal < 0 {
					childProduct.PaymentTotal = 0
				}
				childProduct.Privilege = (childProduct.PayPrice * childProduct.Number) - childProduct.PaymentTotal
				childProduct.PrivilegeTotal = childProduct.Privilege

				childProduct.VipPrice = (orderProduct.VipPrice - sumVipPrice) * childProduct.Number / orderProduct.Number
				if childProduct.VipPrice < 0 {
					childProduct.VipPrice = 0
				}
			} else {
				//商品项优惠 = 总优惠*优惠占比（商品项原金额原金额/总优惠）
				//商品项实付金额 =商品项原金额（原价*数量）-商品项优惠
				ratio := math.Ceil(cast.ToFloat64(childProduct.PayPrice*childProduct.Number)) / cast.ToFloat64(combineSum)
				childProduct.Privilege = cast.ToInt32(math.Ceil(ratio * cast.ToFloat64(combinePrivilege)))
				childProduct.PrivilegeTotal = childProduct.Privilege
				childProduct.PaymentTotal = childProduct.PayPrice*childProduct.Number - childProduct.Privilege

				childProduct.VipPrice = int32(decimal.NewFromInt32(childProduct.Price).Mul(vipRate).Round(0).IntPart())
				sumVipPrice += childProduct.VipPrice * childProduct.Number / orderProduct.Number

				sumItemAmount += childProduct.PaymentTotal
			}
			//优惠均摊后的单价
			childProduct.PayPrice = cast.ToInt32(cast.ToFloat64(childProduct.PaymentTotal) / cast.ToFloat64(childProduct.Number)) //实付单价
		} else {
			//如果没有活动，则不均摊组合商品，直接用组合商品的原组合价
			childProduct.PaymentTotal = childProduct.Number * childProduct.PayPrice
			childProduct.Privilege = 0                           //组合自己的优惠
			childProduct.PrivilegeTotal = childProduct.Privilege //单商品总优惠
		}

		childProduct.SkuPayTotal = childProduct.PaymentTotal

		orderProducts = append(orderProducts, childProduct)
	}

	return orderProducts, combinePrivilege
}

//MallJunTan 电商均摊商品（优惠金额），运费不均摊，生成主订单
//注：区别于小程序：
//1:电商商品实付金额，已传过来；
//2:电商商品，验证已在电商验证过了，不需要在这验证
//3:eg:组合商品1包含子商品（a:原价10，打5折后组合价5；b：原价10，打5折后组合价5），组合商品1组合价为10，电商后台编辑售卖价为9，再参与各种活动。
//所有电商传过来的一级商品price为9，子商品a,b的price为组合价5
func MallJunTan(model *models.OrderSubmitRequest) (code int32, msg string, addOrder *oc.MtAddOrderRequest) {
	//订单主数据转换
	addOrder = model.Order.TransToProtoDataMall()
	glog.Info("阿闻电商订单：Sku信息，", model.Order.ReceiverPhone)

	var (
		beforePrivilegeTotal int32 //原总售卖金额
		sumPrivilege         int32 //统计所有商品优惠，用于倒减得出最后一个商品优惠
	)
	//统计商品原总价，实物商品原总价
	for _, item := range model.OrderProducts {
		beforePrivilegeTotal += item.Number * item.Price //一级商品售卖价
	}

	orderProducts := make([]*oc.OrderProductModel, 0)        //所有商品 包括子商品
	OrderPayProducts := make([]*models.OrderProductModel, 0) //所有非赠品
	//step 1:过滤赠品，赠品不参与均摊
	for _, item := range model.OrderProducts {
		var realFlag, virtualFlag int32
		if item.Price == 0 { //赠品
			product := item.TransToProtoDataMall()
			//如果赠品是组合商品,那么子商品也是赠品，也需要拆单
			if item.ProductType == 3 {
				for _, child := range item.ChildProductList {
					//价格置为0 确保 计算出来的相关价格字段 都为0  因为所有的相关价格都是基于price计算的
					child.Price = 0
					childProduct := child.TransToProtoDataMallChild(item)
					orderProducts = append(orderProducts, childProduct)
					if item.ProductType == 1 && realFlag == 0 {
						realFlag = 1
					}
					if item.ProductType == 2 && virtualFlag == 0 {
						virtualFlag = 2
					}
				}
				product.CombineType = realFlag + virtualFlag
			}
			orderProducts = append(orderProducts, product)

		} else {
			ChildOrderPayProducts := make([]*models.OrderProductModel, 0) //需要支付子商品
			//组合
			if item.ProductType == 3 {
				for _, child := range item.ChildProductList {
					if child.Price == 0 { //赠品
						childProduct := child.TransToProtoDataMallChild(item)
						orderProducts = append(orderProducts, childProduct)
					} else {
						ChildOrderPayProducts = append(ChildOrderPayProducts, child)
					}
					if child.ProductType == 1 && realFlag == 0 {
						realFlag = 1
					}
					if child.ProductType == 2 && virtualFlag == 0 {
						virtualFlag = 2
					}
				}
				item.CombineType = realFlag + virtualFlag
			}
			//子商品置为需要支付的子商品 过滤掉赠品
			item.ChildProductList = ChildOrderPayProducts
			//非赠品商品列表
			OrderPayProducts = append(OrderPayProducts, item)
		}
	}

	//step 2: 商品均摊优惠
	//对所有的非赠品进行均摊
	//非组合商品子商品根据商品优惠前总金额占比分摊优惠金额到每个商品上 再根据每个商品分摊到的优惠金额 得出分摊后实际支付的价格
	//SkuPayTotal=PaymentTotal ；Price = PayPrice
	//组合商品子商品 根据父商品的总实际支付金额/优惠金额 = 子商品优惠前总金额占比*父商品实际支付金额/优惠金额
	for index, item := range OrderPayProducts {
		product := item.TransToProtoDataMall()
		//计算分摊到每个商品上的优惠金额
		if len(OrderPayProducts)-1 == index {
			//最后一个商品的优惠金额  = 剩余未分摊的优惠金额
			product.Privilege = model.Order.Privilege - sumPrivilege
			product.PrivilegeTotal = product.Privilege
		} else {
			//所有的商品按照商品价格占比参与均摊总优惠
			// 商品的优惠额 = 总优惠额 * 商品价格占比  商品价格占比 = 商品的优惠前总价/订单的优惠前总价
			product.Privilege = cast.ToInt32(math.Ceil(cast.ToFloat64(model.Order.Privilege) * cast.ToFloat64(item.Price*item.Number) / cast.ToFloat64(beforePrivilegeTotal)))
			product.PrivilegeTotal = product.Privilege
			sumPrivilege += product.Privilege //总的优惠
		}
		//实际支付的金额 = 原实际支付金额-优惠金额
		product.PaymentTotal = product.PaymentTotal - product.PrivilegeTotal
		product.SkuPayTotal = product.PaymentTotal
		//单个商品支付价格 = 实际支付总金额 / 商品的数量
		product.PayPrice = cast.ToInt32(cast.ToFloat64(product.PaymentTotal) / cast.ToFloat64(item.Number))
		product.Price = product.PayPrice

		//组合商品继续通过组合商品的实付金额，优惠金额，运费进行均摊到子商品
		if product.ProductType == 3 {
			var (
				childBeforePrivilegeTotal int32 //此组合商品优惠前的组合总金额=(a子商品组合价*a数量)+ (b子商品组合价*b数量)
				childSumPrivilege         int32 //统计所有商品优惠，用于倒减得出最后一个商品优惠
				childSumPayTotal          int32 //统计所有商品实付金额，用于倒减得出最后一个商品实付金额
			)
			for _, child := range item.ChildProductList {
				childBeforePrivilegeTotal += child.Number * item.Number * child.Price //单个子商品总购买金额
			}

			//对子商品 进行优惠以及支付金额的均摊
			for childIndex, child := range item.ChildProductList {
				childProductCount := child.Number * item.Number //子商品购买数量
				childProduct := child.TransToProtoDataMallChild(item)

				//最后一个
				if len(item.ChildProductList)-1 <= childIndex {
					childProduct.PaymentTotal = product.PaymentTotal - childSumPayTotal
					childProduct.Privilege = product.PrivilegeTotal - childSumPrivilege
					childProduct.PrivilegeTotal = childProduct.Privilege
				} else {
					//均摊商品优惠
					//均摊后单个支付金额 = 子商品金额占比 * 组合商品总支付金额
					ratio := cast.ToFloat64(child.Price*childProductCount) / cast.ToFloat64(childBeforePrivilegeTotal)
					//实际支付金额 = 父商品支付金额 * 子商品金额占比
					childProduct.PaymentTotal = cast.ToInt32(cast.ToFloat64(product.PaymentTotal) * ratio)
					childProduct.Privilege = cast.ToInt32(cast.ToFloat64(product.PrivilegeTotal) * ratio) //本商品均摊优惠=商品金额占比*组合商品总优惠
					childProduct.PrivilegeTotal = childProduct.Privilege
					childSumPrivilege += childProduct.Privilege
					childSumPayTotal += childProduct.PaymentTotal
				}

				childProduct.PayPrice = cast.ToInt32(cast.ToFloat64(childProduct.PaymentTotal) / cast.ToFloat64(childProductCount)) //实际支付价
				childProduct.Price = childProduct.PayPrice
				childProduct.SkuPayTotal = childProduct.PaymentTotal
				//非赠品子商品的商品写入order_product
				orderProducts = append(orderProducts, childProduct)
			}
		}
		orderProducts = append(orderProducts, product)
	}

	//活动数据转换
	orderPromotions := make([]*oc.OrderPromotionModel, len(model.OrderPromotions))
	for i, item := range model.OrderPromotions {
		orderPromotions[i] = item.TransToProtoDataMall()
	}

	//赋值活动数据与商品数据
	addOrder.OrderPromotion = orderPromotions
	addOrder.OrderProductModel = orderProducts

	return 200, msg, addOrder
}

//SubmitOrderFail 提交订单失败处理
func SubmitOrderFail(addOrder *models.OrderSubmitRequest) {
	//秒杀订单 如果失败则释放虚拟库存
	if addOrder.Order.OrderType == 12 {
		FreedSecKillStock(
			addOrder.OrderProducts[0].Sku,
			addOrder.OrderProducts[0].PromotionId,
			addOrder.OrderProducts[0].Number,
		)
	}
}

//FreedSecKillStock 释放秒杀库存
//调用场景：1：秒杀活动商品与活动检测失败 2：秒杀订单下单失败
//benchmark 平均在17ms/op
//@version v2.9.10
func FreedSecKillStock(sku string, secKillId int32, number int32) {
	strSecKillId := cast.ToString(secKillId)
	redisConn := utils.GetRedisConn()
	keys := []string{"stock:seckill:" + strSecKillId + ":" + sku}
	scriptStr := `
	local number = tonumber(ARGV[1])
	local sec_kill_stock_key = KEYS[1]

	-- 判断虚拟库存是否存在 不存在返回 存在则增加虚拟库存
	local sec_kill_stock_value = tonumber(redis.call('GET',sec_kill_stock_key))
	if sec_kill_stock_value == nil then 
		return 2
	end

	-- 增加虚拟库存
	redis.call('INCRBY',sec_kill_stock_key,number)
	return 1
	`
	result := redisConn.Eval(scriptStr, keys, number)
	resultVal := result.Val()

	if resultVal == nil {
		glog.Error("freedSecKillStock-释放库存失败，lua脚本有报错，错误内容："+result.Err().Error(), keys, number, kit.RunFuncName(2))
		return
	}

	res, ok := resultVal.(int64)
	if !ok {
		glog.Info("freedSecKillStock-释放库存时失败-断言错误", keys, number, kit.RunFuncName(2))
		return
	}
	if res == 2 {
		glog.Info("freedSecKillStock-释放库存时未获取到缓存信息", keys, number, kit.RunFuncName(2))
		return
	}
	//todo 测试稳定后可删除
	glog.Info("freedSecKillStock-释放库存成功", keys[0], number, kit.RunFuncName(2))
	return
}

// 通过payType获取payMode，`pay_mode`支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支付，18百度支付',
// 1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准） 13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付 17：标准支付宝小程序支付 18：百度小程序支付
func GetPayModelByPayType(payType string) (payMode int32) {
	switch cast.ToInt32(payType) {
	case 8:
		payMode = 8
	case 11, 17:
		payMode = 1
	case 18:
		payMode = 18
	default:
		payMode = 2
	}
	return
}
