package controller

import (
	"fmt"
	kit "github.com/tricobbler/rp-kit"
	"order-api/models"
	"order-api/proto/dac"
	"order-api/proto/oc"
	"order-api/proto/pc"
	"strings"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
	"github.com/tricobbler/echo-tool/validate"
)

// @Summary 售后申请单列表
// @Tags 订单售后相关
// @Accept plain
// @Produce json
// @Param order_sn query string false "订单编号"
// @Success 200 {object} models.ApplyListResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order-api/aftersale/apply/list [get]
func ApplyList(c echo.Context) error {
	type params struct {
		OrderSn string `query:"order_sn" label:"订单编号"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	channelId := c.Get("channel_id").(int32)
	userAgent := c.Get("user_agent").(int32)

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.ApplyOrderListRequest{
		ChannelId:    channelId,
		UserAgent:    userAgent,
		MemberId:     memberInfo.Id,
		Keywords:     p.OrderSn,
		CreateTimeGt: time.Now().AddDate(0, 0, -15).Format(kit.DATETIME_LAYOUT), //只返回最近7天的订单
	}
	res, err := ocClient.RPC.ApplyOrderList(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用ApplyOrderList失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}
	if res.Code != 200 {
		return r.NewHTTPError(400, res.Message)
	}

	out := models.ApplyListResponse{}
	out.Code = 200

	var orderSnSlice []string
	//批量查询
	for _, v := range res.Details {
		applyList := &models.ApplyList{
			ShopId:           v.ShopId,
			ShopName:         v.ShopName,
			OrderSn:          v.OrderSn,
			Total:            fmt.Sprintf("%.2f", kit.FenToYuan(v.Total)),
			GoodsTotal:       fmt.Sprintf("%.2f", kit.FenToYuan(v.GoodsTotal)),
			Freight:          fmt.Sprintf("%.2f", kit.FenToYuan(v.Freight)),
			OrderStatusChild: v.OrderStatusChild,
			IsVirtual:        v.IsVirtual,
		}

		confirmT, _ := time.ParseInLocation(kit.DATETIME_LAYOUT, v.ConfirmTime, time.Local)
		//商家接单后到订单完成24小时内可申请退款
		if v.OrderStatusChild >= 20102 && (len(v.ConfirmTime) == 0 || confirmT.Unix() > time.Now().AddDate(0, 0, -1).Unix()) {
			applyList.CanRefund = 1
		} else if v.OrderStatus == 30 {
			//已完成订单，完成超过24小时，提示超过售后期
			applyList.IsOutDate = 1
		}

		for _, pro := range v.Orderproductmodel {
			surplusNumber := pro.Number - pro.RefundNum - pro.UsedNum
			surplusRefundAmount := int32(0)
			if surplusNumber > 0 {
				surplusRefundAmount = pro.PaymentTotal - (pro.PayPrice * (pro.RefundNum + pro.UsedNum))
			}
			applyList.Product = append(applyList.Product, models.Product{
				OrderProductId:      cast.ToInt64(pro.Id),
				ProductId:           cast.ToInt32(pro.ProductId),
				ProductName:         pro.ProductName,
				Number:              pro.Number,
				RefundNumber:        pro.RefundNum,
				Image:               pro.Image,
				Specs:               pro.Specs,
				Price:               fmt.Sprintf("%.2f", kit.FenToYuan(pro.Price)),
				PayPrice:            fmt.Sprintf("%.2f", kit.FenToYuan(pro.PayPrice)),
				SkuId:               cast.ToInt32(pro.Sku),
				ParentSkuId:         pro.ParentSkuId,
				UsedNumber:          pro.UsedNum,
				SurplusNumber:       surplusNumber,
				SurplusRefundAmount: surplusRefundAmount,
			})
		}

		out.Data = append(out.Data, applyList)
		orderSnSlice = append(orderSnSlice, v.OrderSn)
	}

	//批量查询订单是否有退款记录
	in2 := &oc.RefundOrderServiceListRequest{
		OrderSn:   orderSnSlice,
		MemberId:  memberInfo.Id,
		ChannelId: channelId,
	}
	res2, err := ocClient.AfterSale.RefundOrderServiceList(ocClient.Ctx, in2)
	if err != nil {
		glog.Error("调用RefundOrderServiceList失败，", err, "，参数：", kit.JsonEncode(in2))
		return r.NewHTTPError(400, err.Error())
	}
	if res2.Code != 200 {
		return r.NewHTTPError(400, res2.Message)
	}

	for _, v := range out.Data {
		//购买数量
		buyNum := int32(0)
		usedNum := int32(0)
		for _, p := range v.Product {
			buyNum += p.Number
			usedNum += p.UsedNumber
		}

		//已退数量
		refundNum := int32(0)
		for _, vv := range res2.Data {
			//有退款记录
			if vv.OrderSn == v.OrderSn {
				v.HasRefundRecord = 1

				//用户自身原因申请的全单退货被驳回的不能再申请退款
				if vv.Fullrefund == 1 && vv.Refundtype == 1 && (vv.Refundreason == `计划有变，我不想要了` || vv.Refundreason == `我买错了/填错了`) {
					v.CanRefund = 0
				}

				//在24小时内售后期，还需判断是否还有可退商品（退款失败不计入退款商品数）
				if v.CanRefund == 1 && vv.RefundState != 2 {
					for _, vvv := range vv.Product {
						refundNum += vvv.Tkcount
					}
				}
			}
		}
		//虚拟商品需要考虑是否使用
		if v.IsVirtual > 0 {
			//商品全部退完不能再申请退款
			if buyNum > 0 && buyNum == (refundNum+usedNum) {
				v.CanRefund = 0
			}
		} else {
			//商品全部退完不能再申请退款
			if buyNum > 0 && buyNum == refundNum {
				v.CanRefund = 0
			}
		}
	}

	return c.JSON(200, out)
}

// @Summary 售后服务单列表
// @Tags 订单售后相关
// @Accept plain
// @Produce json
// @Param order_refund_sn query string false "订单/服务单编号"
// @Success 200 {object} models.RefundListResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order-api/aftersale/refund/list [get]
func RefundList(c echo.Context) error {
	type params struct {
		OrderRefundSn string `query:"order_refund_sn" label:"订单/服务单编号"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	channelId := c.Get("channel_id").(int32)
	memberInfo, ok := c.Get("member_info").(*models.MemberInfo)
	if !ok || memberInfo == nil {
		return r.NewHTTPError(400, "用户不存在")
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.RefundOrderServiceListRequest{
		MemberId: memberInfo.Id,
		Keywords: p.OrderRefundSn,
	}
	res, err := ocClient.AfterSale.RefundOrderServiceList(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用RefundOrderServiceList失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}
	if res.Code != 200 {
		return r.NewHTTPError(400, res.Message)
	}

	var (
		skuIdSlice     []int32
		productIds     []int32
		productIdSlice []int32
	)
	var skuIdMap = make(map[int32]int32)
	for _, v := range res.Data {
		for _, vv := range v.Product {
			skuIdSlice = append(skuIdSlice, cast.ToInt32(vv.SkuId))
			productIds = append(productIds, vv.ProductId)
			skuIdMap[cast.ToInt32(vv.SkuId)] = vv.ProductId
		}
	}

	clt := pc.GetDcProductClient()
	defer clt.Close()

	//查询所有商品详情
	var res2 *pc.ChannelProductDetailResponse
	if memberInfo.OrgId == 6 {
		ShopId := cast.ToString(c.Get("shop_id"))
		res2, _ = clt.RPC.GetEshopProductSnapshotBySpuOrSku(clt.Ctx, &pc.GetChannelProductSnapshotBySpuOrSkuRequest{
			ChannelId:   channelId,
			FinanceCode: ShopId,
			SkuId:       skuIdSlice,
		})
	} else {
		//根据sku_id查询product_id
		skuIdMap := skuIdToProductId(skuIdSlice)
		for _, v := range skuIdMap {
			productIdSlice = append(productIdSlice, v)
		}
		in2 := &pc.OneofIdRequest{ChannelId: channelId, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: productIdSlice}}}
		res2, err = clt.RPC.QueryChannelProductDetail(clt.Ctx, in2)
		if err != nil {
			glog.Error("调用QueryChannelProductDetail失败，", err, "，", in2)
			return r.NewHTTPError(400, err.Error())
		}
	}

	out := models.RefundListResponse{}
	out.Code = 200
	out.Data = []*models.RefundList{}

	for _, v := range res.Data {
		var pro []models.Product
		for _, vv := range v.Product {
			for _, vvv := range res2.Details {
				if skuIdMap[cast.ToInt32(vv.Goodsid)] == vvv.Product.Id {
					pic := ""
					if len(vvv.Product.Pic) > 0 {
						pic = strings.Split(vvv.Product.Pic, ",")[0]
					}
					pro = append(pro, models.Product{
						ProductId:   vvv.Product.Id,
						ProductName: vvv.Product.Name,
						Number:      vv.Quantity,
						Image:       pic,
						Specs:       vv.Spec,
						SkuId:       cast.ToInt32(vv.SkuId),
					})
				}
			}
		}

		//获取退款订单状态
		status, statusText, canRevoke := formatRefundStatus(v.Refundtype, v.RefundState, v.Expressnum)

		//是否需要填快递单号
		needExpress := 0
		//等待买家退货阶段需要填写快递单号
		if status == 4 {
			needExpress = 1
		}

		out.Data = append(out.Data, &models.RefundList{
			RefundSn:    v.Refundsn,
			OrderSn:     v.OrderSn,
			Status:      status,
			StatusText:  statusText,
			Refundtype:  v.Refundtype, //申请类型: 1为退款,2为退货，默认为1
			CanRevoke:   canRevoke,
			NeedExpress: int32(needExpress),
			Product:     pro,
		})
	}

	return c.JSON(200, out)
}

// @Summary 服务单详情
// @Tags 订单售后相关
// @Accept plain
// @Produce json
// @Param refund_sn query string true "退款单号"
// @Success 200 {object} models.RefundDetailResponse
// @Failure 400 {object} models.BaseResponse
// @Router /order-api/aftersale/refund/detail [get]
func RefundDetail(c echo.Context) error {
	type params struct {
		RefundSn string `query:"refund_sn" validate:"required" label:"退款单号"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//校验参数
	if err := c.Validate(p); err != nil {
		err := validate.Translate(err)
		return r.NewHTTPError(400, err.One())
	}

	channelId := c.Get("channel_id").(int32)

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	in := &oc.OrderRetrunGetRequest{
		Refundsn: p.RefundSn,
	}
	res, err := ocClient.AfterSale.OrderRetrunGet(ocClient.Ctx, in)
	if err != nil {
		glog.Error("调用OrderRetrunGet失败，", err, "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, err.Error())
	}
	if res.Code != 200 {
		glog.Error("调用OrderRetrunGet失败，", kit.JsonEncode(res), "，参数：", kit.JsonEncode(in))
		return r.NewHTTPError(400, res.Message)
	}

	out := models.RefundDetailResponse{}
	out.Code = 200
	out.Data = &models.RefundDetail{
		RefundAmount:  fmt.Sprintf("%.2f", cast.ToFloat32(res.Data.Refundamount)),
		RefundAccount: "原支付账户",
		Refundtype:    res.Data.Refundtype,
		RefundReason:  res.Data.Refundreason,
		OrderSn:       res.Data.OrderSn,
		RefundProduct: []models.Product{},
		ExpressNo:     res.Data.Expressnum,
		ExpressName:   res.Data.Expressname,
	}

	//获取退款订单状态
	out.Data.Status, out.Data.StatusText, out.Data.CanRevoke = formatRefundStatus(res.Data.Refundtype, res.Data.RefundState, res.Data.Expressnum)

	//等待买家退货阶段需要填写快递单号
	if out.Data.Status == 4 {
		out.Data.NeedExpress = 1

		//获取商家退货地址
		dacClient := dac.GetDataCenterClient()
		defer dacClient.Close()

		wg := new(sync.WaitGroup)
		wg.Add(2)

		go func() {
			defer wg.Done()

			res, err := dacClient.RPC.ShopDeliveryServiceList(dacClient.Ctx, &dac.ShopDeliveryServiceListRequest{
				FinanceCodes: res.Data.ShopId,
			})
			if err == nil && res.Code == 200 && len(res.DataList) > 0 {
				out.Data.ReturnAddress.Phone = res.DataList[0].Mobile
				out.Data.ReturnAddress.Address = res.DataList[0].ReturnAddress
			}
		}()

		go func() {
			defer wg.Done()

			res, err := dacClient.RPC.QueryStoreInfo(dacClient.Ctx, &dac.StoreInfoRequest{
				FinanceCode: []string{res.Data.ShopId},
			})
			if err == nil && res.Code == 200 && len(res.Details) > 0 {
				out.Data.ReturnAddress.Name = res.Details[0].Name
			}
		}()

		wg.Wait()
	}

	//整理退款流程日志
	formatRefundLog(out.Data, res.Data)
	var skuIdSlice []int32
	for _, v := range res.Data.RefundGoodsOrders {
		skuIdSlice = append(skuIdSlice, cast.ToInt32(v.SkuId))
	}

	//根据sku_id查询product_id
	skuIdMap := skuIdToProductId(skuIdSlice)
	var productIdSlice []int32
	for _, v := range skuIdMap {
		productIdSlice = append(productIdSlice, v)
	}

	pcClient := pc.GetDcProductClient()
	defer pcClient.Close()

	//查询所有商品详情
	//TODO V6.0  QueryChannelProductDetail支持skuId查询 直接通过skuId获取
	in2 := &pc.OneofIdRequest{ChannelId: channelId, Id: &pc.OneofIdRequest_ProductId{ProductId: &pc.ArrayIntValue{Value: productIdSlice}}}
	res2, err := pcClient.RPC.QueryChannelProductDetail(pcClient.Ctx, in2)
	if err != nil {
		glog.Error("调用QueryChannelProductDetail失败，", err, "，参数：", kit.JsonEncode(in2))
		return r.NewHTTPError(400, err.Error())
	}

	for _, v := range res.Data.RefundGoodsOrders {
		for _, vv := range res2.Details {
			if vv.Product.Id == skuIdMap[cast.ToInt32(v.SkuId)] {
				pic := ""
				if len(vv.Product.Pic) > 0 {
					pic = strings.Split(vv.Product.Pic, ",")[0]
				}

				out.Data.RefundProduct = append(out.Data.RefundProduct, models.Product{
					ProductId:    vv.Product.Id,
					ProductName:  vv.Product.Name,
					Number:       v.Quantity,
					RefundNumber: v.Tkcount,
					Image:        pic,
					Specs:        v.Spec,
					Price:        cast.ToString(v.RefundPrice),
					PayPrice:     cast.ToString(v.FoodPrice),
					SkuId:        vv.SkuInfo[0].SkuId,
					PromotionId:  v.PromotionId,
				})
			}
		}
	}

	return c.JSON(200, out)
}

// @Router /order-api/aftersale/refund/test [get]
func TestRefundDetail(c echo.Context) error {
	type params struct {
		RefundSn string `query:"refund_sn" validate:"required" label:"退款单号"`
	}

	//绑定参数
	p := new(params)
	if err := c.Bind(p); err != nil {
		return r.NewHTTPError(400, err.Error())
	}

	//校验参数
	if err := c.Validate(p); err != nil {
		err := validate.Translate(err)
		return r.NewHTTPError(400, err.One())
	}

	ocClient := GetOrderServiceClient()
	defer ocClient.Close()

	res, err := ocClient.AfterSale.OrderRetrunGet(ocClient.Ctx, &oc.OrderRetrunGetRequest{
		Refundsn: p.RefundSn,
	})
	if err != nil {
		glog.Error(err)
		return r.NewHTTPError(400, err.Error())
	}
	if res.Code != 200 {
		return r.NewHTTPError(400, res.Message)
	}

	return c.JSON(200, res)
}
