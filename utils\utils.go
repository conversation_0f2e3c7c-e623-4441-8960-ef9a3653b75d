package utils

import (
	"bytes"
	"context"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"order-api/proto/cc"
	"strconv"
	"strings"
	"time"

	"order-api/models"

	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	kit "github.com/tricobbler/rp-kit"
	"github.com/tricobbler/rp-kit/cast"
	"google.golang.org/grpc/metadata"
)

//通过手机号获取会员信息
func GetMemberInfoByMobile(mobile string) (*models.MemberInfo, error) {
	//读取缓存数据
	redisClient := NewRedisClient()
	defer redisClient.Close()

	key := "member:info:mobile"
	keyResultStr := redisClient.HGet(key, mobile).Val()
	if len(keyResultStr) > 0 {
		memberInfo := new(models.MemberInfo)
		if err := json.Unmarshal([]byte(keyResultStr), memberInfo); err != nil {
			redisClient.Del(key, mobile)
			return nil, err
		}
		if memberInfo.CacheTime < time.Now().Unix() {
			redisClient.Del(key, mobile)
		}
		return memberInfo, nil
	}

	client := cc.GetCustomerCenterClient()
	defer client.Close()

	res,err := client.User.UserInfo(context.Background(),&cc.UserInfoRequest{
		Mobile:               mobile,
	})

	if err !=nil{
		return nil, err
	}
	if len(res.UserId)<=0{
		return nil, errors.New("未查询到该用户信息，mobile：" + mobile)
	}
	resStruct := &models.MemberInfo{
		Id:             res.UserId,
		Name:           res.UserName,
	}
	resStruct.CacheTime = time.Now().Add(24 * time.Hour).Unix() //缓存24小时
	redisClient.HSet(key, mobile, resStruct)
	return resStruct, nil
}

//请求header添加到grpc上下文
func AppendToOutgoingContextLoginUserInfo(ctx context.Context, c ...echo.Context) context.Context {
	if len(c) == 0 {
		return context.Background()
	}

	grpcContext := models.GrpcContext{}

	//if claims, err := GetPayloadDirectly(c[0]); err != nil {
	//	//TODO 注释，错误频繁
	//	//glog.Error(err)
	//} else {
	//	grpcContext.UserInfo.UserNo = InterfaceToString(claims["userno"])
	//}
	//
	//ctx = metadata.AppendToOutgoingContext(ctx, "login_user_info", kit.JsonEncode(grpcContext.UserInfo))

	//channel,useragent
	grpcContext.Channel.ChannelId = cast.ToInt(c[0].Request().Header.Get("channel_id"))
	grpcContext.Channel.UserAgent = cast.ToInt(c[0].Request().Header.Get("user_agent"))

	return metadata.AppendToOutgoingContext(ctx, "grpc_context", kit.JsonEncode(grpcContext))
}

//url ： /base/area/all
//dataJson : 数据对象转化成json字符串
func HttpPostToBJ(url string, dataJson []byte, Headers string) ([]byte, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	client := http.Client{Timeout: time.Second * 60, Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}}
	req.Header.Set("Content-Type", "application/json")
	//req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	if len(Headers) > 0 {
		strlist := strings.Split(Headers, "&")
		for i := 0; i < len(strlist); i++ {
			v := strlist[i]
			valuelist := strings.Split(v, "|")
			req.Header.Set(valuelist[0], valuelist[1])
		}
	}

	for k, v := range BjSignMap(url) {
		req.Header.Set(k, v)
	}

	res, err := client.Do(req)
	if err != nil {
		glog.Error(err)
		return []byte(""), err
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	return body, err
}

func BjSignMap(url string) map[string]string {
	domainUrl := strings.Split(url, "//")[1]
	baseUrl := strings.Split(domainUrl, "/")[0]
	method := strings.Split(url, baseUrl)[1]
	Timestamp := strconv.Itoa(int(time.Now().Unix()))
	sign := fmt.Sprintf("AppId=%s&Secret=%s&Url=%s&Timestamp=%s&Version=%s", config.GetString("bj.auth.appid"), config.GetString("bj.auth.secret"), method, Timestamp, config.GetString("bj.auth.version"))
	h := md5.New()
	h.Write([]byte(sign))
	md5sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	arr := make(map[string]string)
	arr["focus-auth-appid"] = config.GetString("bj.auth.appid")
	arr["focus-auth-userid"] = "0"
	arr["focus-auth-username"] = "0"
	arr["focus-auth-version"] = config.GetString("bj.auth.version")
	arr["focus-auth-url"] = method
	arr["focus-auth-timestamp"] = Timestamp
	arr["focus-auth-sign"] = md5sign

	ff, _ := json.Marshal(arr)

	fmt.Println(string(ff))
	return arr
}
