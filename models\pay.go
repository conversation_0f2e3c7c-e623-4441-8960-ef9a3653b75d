package models

type PayRequest struct {
	//订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购 10新人专享 11预售 12新秒杀 99助力订单
	OrderType int32 `json:"order_type"`
	//1：阿闻，2：子龙，3：R1，4：互联网
	AppId int32 `json:"app_id"`
	//订单号
	OrderNo string `json:"order_no"`
}

type StandardPayRequest struct {
	AppId int32 `json:"app_id"`
	BarCode string `json:"bar_code"`
	ClientIp string `json:"client_ip"`
	Discount int32 `json:"discount"`
	ExtendInfo string `json:"extend_info"`
	MerchantId string `json:"merchant_id"`
	NotifyURL string `json:"notify_url"`
	OpenId string `json:"open_id"`
	OrderName string `json:"order_name"`
	OrderNo string `json:"order_no"`
	OrderPayType string `json:"order_pay_type"`
	PayAmount int32 `json:"pay_amount"`
	PayTotal int32 `json:"pay_total"`
	ProductDesc string `json:"product_desc"`
	ProductID string `json:"product_id"`
	Sign string `json:"sign"`
	Timestamp int64 `json:"timestamp"`
	SubAppId string `json:"sub_app_id"`
	TransType int32 `json:"trans_type"`
	TrmId string `json:"trm_id"`
	TrmSn string `json:"trm_sn"`
	ValidTime int32 `json:"valid_time"`
}

type StandardPayResponse struct {
	Code int32 `json:"code"`
	Message string `json:"message"`
	Data *StandardPayRequest `json:"data"`
}

