package middleware

import (
	"fmt"
	"net/http"
	"order-api/models"
	"order-api/utils"
	"strings"

	"github.com/dgrijalva/jwt-go"
	"github.com/labstack/echo/v4"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	r "github.com/tricobbler/echo-tool/httpError"
)

// 校验渠道id和来源，并写入context
func CheckChannel() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {

			//排除swagger文档
			//if strings.Contains(c.Request().URL.Path, "/swagger/") {
			//	return next(c)
			//}

			// p := struct {
			// 	ChannelId string `query:"channel_id" validate:"required" label:"渠道id"`
			// 	UserAgent string `query:"user_agent" validate:"required" label:"来源id"`
			// }{
			// 	c.Request().Header.Get("channel_id"),
			// 	c.Request().Header.Get("user_agent"),
			// }
			//if err := c.Validate(p); err != nil {
			//	err := validate.Translate(err.(validator.ValidationErrors))
			//	return r.NewHTTPError(400, err.One())
			//}

			//如果传来手机号，根据用户手机号查询用户id
			if len(c.Request().Header.Get("Authorization")) > 0 {
				parseToken(c)
			}

			c.Set("channel_id", cast.ToInt32(c.Request().Header.Get("channel_id")))
			c.Set("user_agent", cast.ToInt32(c.Request().Header.Get("user_agent")))
			c.Set("open_id", cast.ToString(c.Request().Header.Get("open_id")))
			return next(c)
		}
	}
}

// 解析token信息
func parseToken(c echo.Context) error {
	jwtToken := c.Request().Header.Get("Authorization")

	if len(jwtToken) <= 0 {
		glog.Error("查看用户的数据的登录信息：", jwtToken)
		return r.NewHTTPError(http.StatusUnauthorized, "valid token required.")
	}
	var tel string
	index := strings.Index(jwtToken, " ")
	count := strings.Count(jwtToken, "")
	token := jwtToken[index+1 : count-1]

	publicKeyString := config.GetString("EnterpriseWechatPublicKey")

	publicKeyString = "-----BEGIN PUBLIC KEY-----\n" + publicKeyString + "\n" + "-----END PUBLIC KEY-----"

	publicKey, _ := jwt.ParseRSAPublicKeyFromPEM([]byte(publicKeyString))

	tk, err := utils.ValidateToken(token, publicKey)
	if err != nil {
		glog.Error("公钥解析登录信息失败，查看用户的数据的登录信息：", tk, err, token, jwtToken)
		return r.NewHTTPError(http.StatusUnauthorized, fmt.Sprintf("valid token required.%v", err))
	}
	claims, ok := tk.Claims.(jwt.MapClaims)
	if !ok {
		glog.Error("登录信息验证失败， 查看用户的数据的登录信息：", tk)
		return nil
	}
	tel = cast.ToString(claims["mobile"])
	//saas获取token相关用户信息
	TenantId := cast.ToString(claims["TenantId"])
	if TenantId != "" {
		memberInfo := new(models.MemberInfo)
		memberInfo.Id = cast.ToString(claims["scrmid"])
		memberInfo.ScrmUserId = cast.ToString(claims["scrmid"])
		memberInfo.ScrmUserName = cast.ToString(claims["name"])
		memberInfo.ScrmUserMobile = tel
		memberInfo.OrgId = cast.ToInt(claims["org_id"])
		c.Set("member_info", memberInfo)
		c.Set("shop_id", TenantId)

	} else {
		// 如果手机号码为空则表示用户未登录
		if tel == "" {
			return nil
		}
		memberInfo, err := utils.GetMemberInfoByMobile(tel)
		if err != nil {
			glog.Error("查询用户手机号失败，", err)
			return r.NewHTTPError(400, "用户不存在")
		}

		memberInfo.ScrmUserId = memberInfo.Id
		memberInfo.ScrmUserName = memberInfo.Name
		memberInfo.ScrmUserMobile = tel
		c.Set("member_info", memberInfo)
		c.Set("scrm_id", memberInfo.Id)
		c.Set("unionid", claims["unionid"])
	}

	return nil
}
