definitions:
  dac.AWStationNearestResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/dac.AWStationNearestResponse_Nearest'
      message:
        type: string
    type: object
  dac.AWStationNearestResponse_Nearest:
    properties:
      distance:
        description: 距离，自带单位，如km
        type: string
      expected_desc:
        description: 预计送达时间描述，如成团当日
        type: string
      expected_time:
        description: 预计送达时间
        type: string
      id:
        description: 站点id
        type: integer
      name:
        description: 站点名称
        type: string
      paid_count:
        description: 已支付订单计数
        type: integer
      progress_notice:
        description: 进度提醒 已支付x单，16:00前还需成交y单才可成团
        type: string
      remain:
        description: 还需数量
        type: integer
    type: object
  dac.AWStationStateResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/dac.AWStationStateResponse_State'
      message:
        type: string
    type: object
  dac.AWStationStateResponse_State:
    properties:
      expected_desc:
        description: 预计送达时间描述，如成团当日
        type: string
      expected_time:
        description: 预计送达时间
        type: string
      id:
        description: 站点id
        type: integer
      name:
        description: 站点名称
        type: string
      progress_notice:
        description: 进度提醒 已支付x单，16:00前还需成交y单才可成团
        type: string
    type: object
  dac.AWStationsResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/dac.AWStationsResponse_Station'
        type: array
      message:
        type: string
      total:
        description: 总数
        type: integer
    type: object
  dac.AWStationsResponse_Station:
    properties:
      address:
        description: 站点地址
        type: string
      distance:
        description: 距离，自带单位，如km
        type: string
      id:
        description: 站点id
        type: integer
      name:
        description: 站点名称
        type: string
    type: object
  dac.DeliveryDistance:
    properties:
      Fee:
        description: 价格，分
        type: integer
      begin:
        description: 起始距离
        type: number
      channel_Id:
        description: 渠道id
        type: integer
      end:
        description: 结束距离）
        type: number
      finance_Code:
        description: 财务ID
        type: string
      id:
        type: integer
    type: object
  dac.DeliverySpecialtimefee:
    properties:
      base_fee:
        description: 起送价，分
        type: integer
      begin_hour:
        description: 起始时间，小时
        type: integer
      begin_minute:
        description: 起始时间，分
        type: integer
      channel_Id:
        description: 渠道id
        type: integer
      delivery_fee:
        description: 配送费，分
        type: integer
      end_hour:
        description: 结束时间，小时
        type: integer
      end_minute:
        description: 结束时间，分
        type: integer
      finance_Code:
        description: 财务ID
        type: string
      id:
        type: integer
    type: object
  dac.DeliveryWeight:
    properties:
      Fee:
        description: 价格，分
        type: integer
      begin:
        description: 起始重量
        type: number
      channel_Id:
        description: 渠道id
        type: integer
      end:
        description: 结束重量
        type: number
      finance_Code:
        description: 财务ID
        type: string
      id:
        type: integer
      weight:
        description: 超出重量
        type: number
    type: object
  dac.OrderLinkStoreReq:
    properties:
      order_amount:
        description: 订单金额，单位分
        type: integer
      order_sn:
        description: 订单号
        type: string
      product_type:
        description: 商品类型，1实物、2虚拟、3到家
        type: integer
      scene:
        description: 微信场景值
        type: integer
      scrm_id:
        description: 用户id，前端不需要传
        type: string
      steps:
        items:
          $ref: '#/definitions/dac.OrderLinkStoreReq_Step'
        type: array
    type: object
  dac.OrderLinkStoreReq_Step:
    properties:
      path:
        description: 路径
        type: string
      query:
        additionalProperties:
          type: string
        description: 参数
        type: object
    type: object
  dac.OrderStepLogRequest:
    properties:
      order_sn:
        description: 订单号
        type: string
      step:
        description: 步骤json字符串
        type: string
      type:
        description: 1-微信下单，2-scrm用
        type: string
    type: object
  dac.OrderSubscribeLogRequest:
    properties:
      order_sn:
        description: 订单号
        type: string
      status:
        description: 用户订阅状态，默认0，1-允许，2-拒绝
        type: integer
    type: object
  dac.Response:
    properties:
      code:
        type: integer
      message:
        type: string
    type: object
  dac.ShopDeliveryServiceDetail:
    properties:
      Business_closetime:
        description: 营业结束时间
        type: integer
      Business_opentime:
        description: 营业开始时间
        type: integer
      Businessdate:
        description: 营业日期
        items:
          type: integer
        type: array
      basefee:
        description: 基础起送价,分
        type: integer
      channel_Id:
        description: 渠道id
        type: integer
      chargetype:
        description: 收费模式：0按距离收取配送费
        type: integer
      deliveryDistance:
        description: 动态配送费用,距离加价
        items:
          $ref: '#/definitions/dac.DeliveryDistance'
        type: array
      deliverySpecialtimefee:
        description: 特殊时段配送费用
        items:
          $ref: '#/definitions/dac.DeliverySpecialtimefee'
        type: array
      deliveryWeight:
        description: 动态配送费用,重量加价
        items:
          $ref: '#/definitions/dac.DeliveryWeight'
        type: array
      deliveryarea:
        description: 配送区域,经纬度坐标点
        type: string
      deliveryfee:
        description: 基础配送价，分
        type: integer
      enable_pickup:
        description: 当前店铺是否开启了社区配送
        type: integer
      finance_Code:
        description: 财务编码
        type: string
      id:
        description: id，新增传0
        type: integer
      image:
        description: 店铺头像
        type: string
      zilong_id:
        description: 子龙门店id
        type: string
    type: object
  dac.ShopDeliveryServiceDetailRequest:
    properties:
      channel_Id:
        description: 渠道ID，必填
        type: integer
      finance_Code:
        description: 财务编码
        type: string
    type: object
  dac.ShopDeliveryServiceDetailResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/dac.ShopDeliveryServiceDetail'
      error:
        type: string
      message:
        type: string
    type: object
  mk.Code:
    enum:
    - 0
    - 200
    - 400
    - 201
    - 404
    - 403
    - 300
    - 401
    - 402
    type: integer
    x-enum-varnames:
    - Code_default
    - Code_success
    - Code_serverException
    - Code_userNotAuthority
    - Code_grpcConnectionError
    - Code_parameterError
    - Code_businessError
    - Code_saveDbException
    - Code_queryDbException
  mk.PromotionCalcDto:
    properties:
      promotionFee:
        description: 金额
        type: integer
      promotionId:
        description: 促销活动
        type: integer
      promotionTitle:
        description: 名称
        type: string
      promotionType:
        description: 类型 1满减、2限时折扣、4会员价
        type: integer
    type: object
  mk.PromotionCalcProductDto:
    properties:
      count:
        description: 数量
        type: integer
      discountCount:
        description: 参与折扣的数量
        type: integer
      discountPrice:
        description: 折扣后商品的价格
        type: integer
      onlyVipDiscountPrice:
        description: 仅vip优惠价格，用于超出限购恢复原价，同时用于标识会员价
        type: integer
      price:
        description: 单价
        type: integer
      promotionId:
        description: 促销活动Id
        type: integer
      promotionType:
        description: 促销活动Id
        type: integer
      skuId:
        description: skuId
        type: string
      sumMoney:
        description: 总金额
        type: integer
    type: object
  mk.PromotionCalcRequest:
    properties:
      channelId:
        description: 渠道Id
        type: integer
      destinationX:
        description: 目的地坐标X 不传递或传递0，不计算运费
        type: number
      destinationY:
        description: 目的地坐标Y 不传递或传递0，不计算运费
        type: number
      isNewUser:
        description: 是否是新用户
        type: boolean
      promotionProduct:
        description: 相关商品
        items:
          $ref: '#/definitions/mk.PromotionCalcProductDto'
        type: array
      shopId:
        description: 用户所属店铺ID
        type: string
      user_id:
        description: 用户id
        type: string
    type: object
  mk.PromotionCalcResponse:
    properties:
      actualMoneyByMinUnit:
        description: 实付金额（商品的实收总金额,不包含运费） 以分为单位
        type: integer
      calcList:
        description: 优惠信息定义明细
        items:
          $ref: '#/definitions/mk.PromotionCalcDto'
        type: array
      code:
        allOf:
        - $ref: '#/definitions/mk.Code'
        description: 代码 非 200 取 message 错误信息
      message:
        description: 错误信息
        type: string
      promotionProduct:
        description: 相关商品
        items:
          $ref: '#/definitions/mk.PromotionCalcProductDto'
        type: array
      promotionReduceList:
        description: 符合条件的满减信息
        items:
          $ref: '#/definitions/mk.PromotionReduceDto'
        type: array
      reduceDelivery:
        allOf:
        - $ref: '#/definitions/mk.PromotionReduceDeliveryDto'
        description: 符合条件的满减运费信息
      reduceMoneyByMinUnit:
        description: 优惠金额（参与商品优惠的金额，不包含运费优惠） 以分为单位
        type: integer
      timeDiscount:
        description: 符合条件的限时优惠信息
        items:
          $ref: '#/definitions/mk.PromotionTimeDiscountDto'
        type: array
      timeDiscountCalcProductCount:
        description: 已经参与限时折扣的商品种类数量
        type: integer
      timeDiscountProductCount:
        description: 能参与限时折扣商品种类数量
        type: integer
      totalMoneyByMinUnit:
        description: 总金额(商品总金额,不包含运费) 以分为单位
        type: integer
      totalWeight:
        description: 总重量
        type: integer
      upetActualDjMoneyByMinUnit:
        description: 总运费金额实收,以分为单位
        type: integer
      upetDjMoneyByMinUnit:
        description: 总运费金额,以分为单位
        type: integer
    type: object
  mk.PromotionReduceDeliveryDto:
    properties:
      ReachMoney:
        description: 满足减免最小金额
        type: number
      ReduceDeliveryType:
        description: 0 普通阶梯递减 1 最高阶梯免配送费
        type: integer
      ReduceMoney:
        description: 减免金额
        type: number
      promotionId:
        description: 促销活动Id
        type: integer
    type: object
  mk.PromotionReduceDto:
    properties:
      promotionId:
        description: 促销活动Id
        type: integer
      reachMoney:
        description: 最小金额
        type: number
      reduceMoney:
        description: 减免金额
        type: number
    type: object
  mk.PromotionTimeDiscountDto:
    properties:
      ConfigBuyCount:
        description: 配置的活动购买数量
        type: integer
      DiscountValue:
        description: 为 0 时代表折扣 为 1 代表固定价格 (统一传浮点数)
        type: number
      DisountType:
        description: 折扣类型  0 按折扣 固定活动价格
        type: integer
      LimitCountByOrder:
        description: 单限购 0 不限制, 非0  限制多少数量
        type: integer
      LimitCountByStock:
        description: 当日限购 0 不限制,非0 限时库存数量
        type: integer
      UserType:
        description: 用户类型 0 全部 1 新客户
        type: integer
      VipDiscount:
        description: 会员额外折扣
        type: number
      promotionId:
        description: 促销活动Id
        type: integer
    type: object
  models.ApplyList:
    properties:
      can_refund:
        description: 能否退款，0否1是
        type: integer
      freight:
        description: 总运费
        type: string
      goods_total:
        description: 商品总金额（未加运费，未加包装费，服务费,减优惠金额）
        type: string
      has_refund_record:
        description: 是否有退款记录，0否1是
        type: integer
      is_out_date:
        description: 是否超过售后时间，0否1是
        type: integer
      is_virtual:
        description: 是否虚拟订单
        type: integer
      order_sn:
        description: 订单编号
        type: string
      order_status_child:
        description: 子状态： 20101(美团默认)未接单; 20102已接单; 20103配送中; 20104已送达; 20105已取货;
          20106已完成; 20107已取消;  10201(电商默认)未付款; 20201待发货; 20202全部发货; 20203确认收货; 20204部分发货;
        type: integer
      product:
        description: 订单商品信息
        items:
          $ref: '#/definitions/models.Product'
        type: array
      shop_id:
        description: 店铺id
        type: string
      shop_name:
        description: 店铺名称
        type: string
      total:
        description: 总金额（实际付款金额。加运费，加包装费，减优惠金额）
        type: string
    type: object
  models.ApplyListResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/models.ApplyList'
        type: array
      message:
        type: string
    type: object
  models.BaseResponse:
    properties:
      code:
        type: integer
      message:
        type: string
    type: object
  models.CannotSumbitProduct:
    properties:
      sku_id:
        description: 商品skuid
        type: string
      status:
        description: 1:无货 2:失效 3:下架
        type: integer
    type: object
  models.CompanyCategoryData:
    properties:
      applyAge:
        description: 适用年龄(1-幼年;2-成年;3-老年)
        type: integer
      cardType:
        description: 套餐类型
        type: integer
      cardTypeCapiton:
        description: 套餐类型说明
        type: string
      categoryAbbreviation:
        description: 简拼
        type: string
      categoryCode:
        description: 套餐编码
        type: string
      categoryName:
        description: 套餐名称
        type: string
      categoryStatus:
        description: 套餐状态 （1-待售；2-在售；3-停售）
        type: integer
      categoryStatusCaption:
        description: 套餐状态说明
        type: string
      costMoney:
        description: 金额
        type: number
      createName:
        description: 创建人
        type: string
      createTime:
        description: 创建时间
        type: string
      discountRate:
        description: 折扣率
        type: number
      effectTime:
        description: 套餐在售期间（生效时间）
        type: string
      id:
        description: id
        type: integer
      itemNO:
        description: 货号
        type: string
      kindof:
        description: 宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)
        type: integer
      loseEffectTime:
        description: 套餐在售期间（失效时间）
        type: string
      originalPrice:
        description: 原价
        type: number
      priceType:
        description: 价格类型 （1 一口价；2 折扣）
        type: integer
      priceTypeCapiton:
        description: 价格类型说明
        type: string
      renewal:
        description: 是否支持续费
        type: integer
      structCode:
        description: 所属组织
        type: string
      structName:
        description: 所属组织名称
        type: string
    type: object
  models.CompanyCategoryReq:
    properties:
      applyAge:
        description: 适用年龄(1-幼年;2-成年;3-老年)
        type: integer
      cardType:
        description: 套餐类别
        type: integer
      categoryCode:
        description: 套餐编码
        type: string
      kingof:
        description: 宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)
        type: integer
      pageIndex:
        description: |-
          适用医院
          EppCode []string `json:"eppCode"`
          页码
        type: integer
      pageSize:
        description: 每页条数
        type: integer
      payType:
        description: 支付方式(1-月付;2-季付;3-年付)
        type: integer
    type: object
  models.DYPayNotifyRequest:
    properties:
      orderNo:
        description: 订单号
        type: string
      payAmount:
        description: 支付金额
        type: integer
      payStatus:
        description: 支付状态
        type: integer
      payTime:
        description: 支付时间
        type: string
      tradeNo:
        description: 支付中心订单号
        type: string
    type: object
  models.EnsureCardsBatchDetails:
    properties:
      batchCode:
        description: 批次号
        type: string
      batchDetailCode:
        description: 批次明细号
        type: string
      categoryCode:
        description: 类别编码
        type: string
      categoryDetail:
        allOf:
        - $ref: '#/definitions/models.EnsureCategoryDetail'
        description: 类别
      categoryDetailCode:
        description: 类别明细编码
        type: string
      chargeTimes:
        description: 总次数
        type: integer
      corpusBalance:
        description: 余额
        type: number
      costMoney:
        description: 总价
        type: number
      discountLevelVoList:
        description: 预售包含的目录和产品
        items:
          $ref: '#/definitions/models.EnsureDiscountLevelVoList'
        type: array
      ensureCode:
        description: 卡号
        type: string
      id:
        type: integer
      leftTimes:
        description: 剩余次数
        type: integer
    type: object
  models.EnsureCardsList:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/models.EnsureCardsListResult'
        type: array
      message:
        type: string
    type: object
  models.EnsureCardsListCriteria:
    properties:
      cardType:
        description: |-
          EnsureCode   string   `json:"ensureCode"`
          BatchCode    string   `json:"batchCode"`
          CategoryCode string   `json:"categoryCode"`
          Status       int      `json:"status"`
          CompanyCode string `json:"companyCode"`
        type: integer
      petIds:
        description: PetId        string   `json:"petId"`
        items:
          type: string
        type: array
      userId:
        description: HospitalCode string   `json:"hospitalCode"`
        type: string
    type: object
  models.EnsureCardsListReq:
    properties:
      criteria:
        $ref: '#/definitions/models.EnsureCardsListCriteria'
      source:
        description: 来源 0子龙 1小暖 2瑞鹏
        type: integer
    type: object
  models.EnsureCardsListResult:
    properties:
      batchCode:
        description: 批次号
        type: string
      batchDetails:
        description: 批次明细
        items:
          $ref: '#/definitions/models.EnsureCardsBatchDetails'
        type: array
      batchStatus:
        description: 批次状态0：未生效 1：正常 2：过期 3：退卡
        type: integer
      categoryDiscounts:
        description: 类别折扣
        items:
          $ref: '#/definitions/models.EnsureCategoryDiscounts'
        type: array
      ensureCode:
        description: 卡号
        type: string
      expiryDate:
        type: string
      hospitalCode:
        description: 机构编码
        type: string
      id:
        type: integer
      petId:
        description: 宠物ID
        type: string
      petIdLists:
        description: 宠物列表
        items:
          type: string
        type: array
      startDate:
        type: string
      userId:
        description: 用户ID
        type: string
      userIdLists:
        description: 用户列表
        items:
          type: string
        type: array
    type: object
  models.EnsureCategoryDetail:
    properties:
      categoryCode:
        description: 类别编码
        type: string
      categoryDetailCode:
        description: 类别明细编码
        type: string
      corpusMoney:
        description: 金额
        type: number
      corpusTimes:
        description: 购买次数
        type: integer
      createId:
        type: integer
      createName:
        type: string
      createTime:
        type: string
      id:
        type: integer
      lastUpdateTime:
        type: string
      lastUpdateUserId:
        type: integer
      lastUpdateUserName:
        type: string
      originalPrice:
        description: 原价
        type: number
      productName:
        description: 项目名称
        type: string
      unitPrice:
        description: 单价
        type: number
    type: object
  models.EnsureCategoryDiscounts:
    properties:
      categoryCode:
        type: string
      dictId:
        type: string
      discount:
        type: string
      discountLevelVoList:
        items:
          $ref: '#/definitions/models.EnsureDiscountLevelVoList'
        type: array
      id:
        type: integer
      operatorId:
        type: integer
      operatorName:
        type: string
      operatorTime:
        type: string
      productTypeName:
        type: string
    type: object
  models.EnsureDiscountLevelVoList:
    properties:
      productCode:
        type: string
      type:
        type: integer
    type: object
  models.GetCompanyCategoryResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/models.CompanyCategoryData'
        type: array
      message:
        type: string
      pageCount:
        description: 每页条数
        type: integer
      pageIndex:
        description: 页码
        type: integer
      pageSize:
        description: 每页条数
        type: integer
      total:
        description: 总数
        type: integer
    type: object
  models.GetSetMealsRes:
    properties:
      code:
        type: integer
      data:
        additionalProperties:
          items:
            $ref: '#/definitions/models.SetMealData'
          type: array
        type: object
      message:
        type: string
    type: object
  models.GetVerifyCodeByOldOrderSnRes:
    properties:
      message:
        description: 提示信息
        type: string
      result:
        description: 核销码结果集
        items:
          $ref: '#/definitions/models.VerifyCodes'
        type: array
      statusCode:
        description: 状态码:200正常 异常
        type: integer
      total:
        description: 满足查询条件的总数据条数
        type: integer
    type: object
  models.MallPushPreSaleIntegralRequest:
    properties:
      order_sn:
        type: string
      pay_price:
        description: 支付金额
        type: integer
      pay_type:
        description: 支付类型 0：加定金积分 1：退定金积分
        type: integer
    type: object
  models.MallPushPreSaleMessageRequest:
    properties:
      order_sn:
        type: string
      send_message:
        allOf:
        - $ref: '#/definitions/oc.PreSalePay'
        description: 发送尾款消息
    type: object
  models.NotifyRequest:
    properties:
      addTime:
        description: 添加时间
        type: string
      channelNo:
        description: 扣款通道返回的流水号
        type: string
      clientIP:
        description: 客户端 IP
        type: string
      discount:
        description: 优惠金额 单位分
        type: string
      extendInfo:
        description: 扩展信息 预留字段，JSON 格式
        type: string
      merchantId:
        description: 商户号
        type: string
      offlineNotifyUrl:
        description: 后台回调地址
        type: string
      orderId:
        description: 订单号
        type: string
      orderTime:
        description: 订单日期格式：YYYYMMDD
        type: string
      outTradeNo:
        description: 商户流水号,仅能用大小写字母与数,字，且在商户系统具有唯一性
        type: string
      payPrice:
        description: 实付金额 单位分
        type: string
      payTime:
        description: 支付时间
        type: string
      payType:
        description: 支付方式  1：微信 JSAPI，2：C扫B，3：B扫C,8:储蓄卡支付
        type: string
      productDesc:
        description: 商品描述
        type: string
      productId:
        description: 商品编号
        type: string
      productName:
        description: 商品名称 最长 32 字节
        type: string
      sign:
        description: 签名
        type: string
      totalPrice:
        description: 订单金额 单位分
        type: string
      tradeNo:
        description: 支付平台流水号
        type: string
    type: object
  models.NotifyResponse:
    properties:
      result:
        type: string
    type: object
  models.OldOrderDetail:
    properties:
      address:
        description: 收件地址
        type: string
      belonghospitalid:
        description: 订单归属门店信息
        type: integer
      cancelltime:
        description: 取消时间
        type: string
      channel_id:
        description: 渠道id（datacenter.platform_channel表）,1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店
        type: integer
      createtime:
        description: 订单创建时间
        type: string
      expresscode:
        description: 物流公司编码
        type: string
      expressmoney:
        description: 油费
        type: integer
      expressno:
        description: 物流单号
        type: string
      expressstate:
        description: 物流状态
        type: integer
      goods:
        description: 商品信息
        items:
          $ref: '#/definitions/models.OrderDetailGoods'
        type: array
      isevaluate:
        description: 是否有价值
        type: integer
      isneedpost:
        description: 是否需要邮寄
        type: boolean
      isnotify:
        description: 是否通知标记
        type: integer
      ispostupet:
        description: 是否推送优宠
        type: integer
      lasttime:
        description: 最后更新时间
        type: string
      memberid:
        description: 用户id
        type: string
      memberintegrals:
        description: 积分明细，暂时不要
        items:
          type: string
        type: array
      membermobile:
        description: 用户手机号
        type: string
      mobile:
        description: 收件人联系电话
        type: string
      orderchildenstate:
        description: 订单子状态
        type: integer
      orderdetail:
        description: 订单详情
        type: string
      orderid:
        description: 订单号
        type: string
      ordermoney:
        description: 订单金额
        type: string
      orderstate:
        description: 订单状态
        type: integer
      ordertype:
        description: 订单类型
        type: integer
      ordertypedetail:
        description: 订单类型小类1-255
        type: integer
      ordertypename:
        description: 订单类型名称
        type: string
      paytime:
        description: 支付时间
        type: string
      payway:
        description: 支付方式
        type: string
      petid:
        description: 宠物id
        type: string
      platform:
        description: 平台名称
        type: string
      platformid:
        description: 平台ID
        type: integer
      recipient:
        description: 收件人
        type: string
      refundtime:
        description: 退款时间
        type: string
      sumquantity:
        type: integer
      useragent:
        description: 用户useragent
        type: integer
    type: object
  models.Order:
    properties:
      address_id:
        description: v2.9.10 添加 用户选择的收件以 原商城虚拟订单的address_id字段
        type: string
      buyer_memo:
        description: 买家留言
        type: string
      channel_id:
        description: 渠道ID：1阿闻到家,2美团,3饿了么,4京东到家,5阿闻电商,6门店,7百度,8H5,9医疗互联网
        type: integer
      consult_order_sn:
        description: 医疗互联网订单号\处方ID\推荐ID
        type: string
      device_current_time:
        description: 设备当前时间（竖屏排查问题用）
        type: string
      dis_id:
        description: v2.9.10 添加 分销id 原商城虚拟订单的dis_id字段
        type: string
      dis_type:
        description: v2.9.10 添加 1分享连接，2扫码 3 自己扫码自己 5 自主访问下单  商城分销用 原商城订单dis_type字段
        type: integer
      expected_time:
        description: 预计送达时间
        type: string
      extraInfo:
        description: 扩展字段
        properties:
          avatar_url:
            description: 参团头像完整地址
            type: string
          dis_member_from:
            description: 店铺分销员id来源 0默认 1
            type: integer
          dis_member_id:
            description: 店铺分销员id
            type: string
          dis_shop_id:
            description: 分销店铺
            type: string
          group_address:
            description: 团购人的地址
            type: string
          group_mobile:
            description: 团购人手机号
            type: string
          group_name:
            description: 团购人名称
            type: string
          nick_name:
            description: 参团昵称
            type: string
        type: object
      first_order:
        description: v2.9.10 添加 原商城虚拟订单first_order字段
        type: string
      freight:
        description: 总运费
        type: integer
      goods_total:
        description: 商品总金额（未加运费，不加包装费，减优惠金额，美团不减优惠金额）
        type: integer
      invoice:
        description: 发票信息
        type: string
      is_split:
        description: 是否有拆单，0否1是
        type: integer
      is_virtual:
        description: 是否是虚拟订单，0否1是
        type: integer
      latitude:
        description: 收货地址纬度
        type: number
      longitude:
        description: 收货地址经度
        type: number
      old_order_sn:
        description: 外部订单号
        type: string
      open_id:
        description: v2.9.10 添加 小城原商城虚拟订单的open_id字段
        type: string
      order_pay_type:
        type: string
      order_type:
        description: 订单类型,1普通订单(默认),2预定订单,3门店自提(电商订单专用),4拼团订单,5门店配送,6健康计划订单,7保险订单,8积分订单
          9秒杀订单 , 13在线问诊，15社区团购
        type: integer
      org_id:
        description: 组织ID
        type: integer
      pickup_station_id:
        description: 社区团购站点
        type: integer
      power_id:
        description: 助力订单id（电商使用）
        type: integer
      privilege:
        description: 总优惠金额
        type: integer
      receiver_address:
        description: 收件地址
        type: string
      receiver_city:
        description: 收件市
        type: string
      receiver_date_msg:
        description: v2.9.10 添加 希望送货时间 商城下单用 原商城订单receiver_date_msg字段
        type: string
      receiver_district:
        description: 收件区
        type: string
      receiver_name:
        description: 收件人
        type: string
      receiver_phone:
        description: 收件电话
        type: string
      receiver_state:
        description: 收件省
        type: string
      shop_id:
        description: 商户或门店id
        type: string
      shop_name:
        description: 商户名称
        type: string
      sk_freight_encrypt:
        description: 秒杀运费信息
        type: string
      source:
        description: v2.9.10 添加 1 小程序(阿闻智慧门店) 2:阿闻宠物(北京那边用) //3阿闻商城(自用) 商城用 原商城虚拟订单的source字段
        type: integer
      tel_phone:
        description: v2.9.10 添加 收件人座机 原商城订单tel_phone字段
        type: string
      total:
        description: 总金额（付款金额，加上运费，加包装费 减优惠金额）
        type: integer
      total_weight:
        description: 总重量(非必填)
        type: integer
      user_agent:
        description: 渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它 houduanzuo
        type: integer
    type: object
  models.OrderDetailGoods:
    properties:
      applyhospitalid:
        description: 申请门店id
        type: string
      barcode:
        description: 商品编码
        type: string
      chargeoff:
        description: 核销状态 -- 很重要 2-待核销，3-已核销，1-不需要核销，4-已过期
        type: integer
      chargeoffcode:
        description: 核销码
        type: string
      chargeoffhospitalid:
        description: 核销医院编号ID
        type: string
      chargeoffmemberid:
        description: 核销人信息
        type: string
      chargeoffobject:
        description: 核销对象
        type: string
      chargeoffobjectname:
        description: 核销对象名称
        type: string
      chargeofftime:
        description: 核销时间
        type: string
      createtime:
        description: 创建时间
        type: string
      expiredate:
        description: 过期时间
        type: string
      goodsid:
        description: 商品货号
        type: string
      goodsimage:
        description: 商品图片地址
        type: string
      id:
        description: 子订单号
        type: string
      isneedpush:
        description: 是否推送
        type: integer
      lasttime:
        description: 最后更新时间
        type: string
      markingPrice:
        description: 商品原价
        type: integer
      name:
        description: 商品名称
        type: string
      orderid:
        description: 订单号
        type: string
      quantity:
        description: 商品数量
        type: integer
      sellprice:
        description: 商品售价
        type: integer
      siglegoodsimage:
        description: 商品缩略图
        type: string
      sku:
        description: skuid
        type: string
      unit:
        description: 商品单位
        type: string
      univalence:
        description: 商品原价
        type: integer
    type: object
  models.OrderPayDyB2CRequest:
    properties:
      bar_code:
        description: 付款码
        type: string
      location:
        type: string
      merc_id:
        description: 商户号
        type: string
      order_sn:
        description: 订单id
        type: string
      org_id:
        description: 机构号
        type: string
      pay_type:
        description: '支付方式 1：微信 2：支付宝 3: 银联'
        type: integer
      source:
        description: 竖屏支付来源 1-商城 0-默认到家
        type: integer
      trm_id:
        description: 终端号
        type: string
      trm_sn:
        description: 机具编号
        type: string
    type: object
  models.OrderPayInfo:
    type: object
  models.OrderPayRequest:
    properties:
      app_id:
        description: appId 应用id，1：阿闻，2：子龙，3：R1，4：互联网，5：SAAS，6：上海兽丘，7：极宠家
        type: integer
      openid:
        description: 微信用户标识 JSAPI 支付时必传
        type: string
      order_sn:
        description: 订单id
        type: string
      orderType:
        description: 订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购
          10新人专享 11预售 12新秒杀 99助力订单
        type: integer
      transType:
        description: |-
          支付方式  1：微信 JSAPI，2：微信扫码C扫B，3：竖屏B扫C，8：储蓄卡支付，10：APP微信支付，11：app支付宝支付，12：B扫C（标准）
          13:微信 JSAPI  14:微信扫码C扫B  15:支付宝扫码C扫B 16：网银支付 17：标准支付宝小程序支付 18：百度小程序支付
        type: integer
    type: object
  models.OrderPayResponse:
    properties:
      code:
        description: 状态码
        type: integer
      create_time:
        description: 下单时间(13位，毫秒)
        type: integer
      data:
        description: 返回数据
        type: string
      error:
        description: 错误信息
        type: string
      message:
        description: 消息
        type: string
    type: object
  models.OrderPrescribeReq:
    properties:
      diagnose:
        items:
          $ref: '#/definitions/oc.PrescriptionDiagnose'
        type: array
      finance_code:
        type: string
      hospital_name:
        type: string
      operate_type:
        type: integer
      pet_info:
        $ref: '#/definitions/oc.ConsultMemberPetInfo'
      pet_weight:
        type: string
      skus:
        items:
          $ref: '#/definitions/oc.OrderPrescribeSkuNum'
        type: array
    type: object
  models.OrderProductModel:
    properties:
      bar_code:
        description: 商品编码
        type: string
      child_product_list:
        description: 子商品列表
        items:
          $ref: '#/definitions/models.OrderProductModel'
        type: array
      combine_type:
        description: 组合商品组合类型0-非组合31-实物实物32-实物虚拟33-虚拟虚拟
        type: integer
      discount_count:
        description: 参与限时折扣的商品数量
        type: integer
      discount_price:
        description: 折扣后的单价
        type: integer
      group_discount_type:
        description: 组合商品折扣类型(1-按折扣优惠，2-按固定价格优惠)
        type: integer
      group_discount_value:
        description: 组合商品折扣值（当折扣类型为1时，存百分比。折扣类型为2时，存具体设置的价格。）
        type: integer
      image:
        description: 商品图片
        type: string
      is_third_product:
        description: 是否是第三方商品信息 1：是  0：否 默认0
        type: integer
      number:
        description: 数量
        type: integer
      parent_sku:
        description: 组合商品父级sku
        type: string
      pay_price:
        description: 商品均摊后实际支付单价
        type: integer
      payment_total:
        description: sku实付总金额，discount_price*number
        type: integer
      price:
        description: 单价
        type: integer
      product_id:
        description: 商品id
        type: string
      product_name:
        description: 商品名称
        type: string
      product_type:
        description: 商品类型1-实物商品，2-虚拟商品，3-组合商品
        type: integer
      promotion_id:
        description: 促销活动Id
        type: integer
      promotion_type:
        description: 活动类型1-满减商品2限时折扣3-满减运费 11 秒杀
        type: integer
      rec_id:
        description: 电商对应的商品id(电商使用)
        type: integer
      sku:
        description: sku
        type: string
      source:
        description: 仓库所属,1:(a8 or 全渠道),2:管易,3:门店（子龙）
        type: integer
      specs:
        description: 规格
        type: string
      term_type:
        description: 只有虚拟商品才有值1-有效期至多少2-有效期天数
        type: integer
      term_value:
        description: 如果expire_type=1存时间戳,如果term_type=2存多少天
        type: integer
      use_virtual_stock:
        description: 是否使用了虚拟库存
        type: integer
      vip_price:
        description: 仅vip折扣价格，用于超出限购会员原价
        type: integer
      virtual_invalid_refund:
        description: 是否支持过期退款 1：是  0：否
        type: integer
      warehouse_type:
        description: 药品仓属性
        type: integer
    type: object
  models.OrderPromotion:
    properties:
      id:
        description: 主键Id
        type: integer
      promotion_fee:
        description: 活动优惠金额
        type: integer
      promotion_id:
        description: 促销活动Id
        type: integer
      promotion_name:
        description: 活动名称
        type: string
      promotion_title:
        description: 促销活动优惠
        type: string
      promotion_type:
        description: 活动类型 活动类型 1 满减活动 2 限时折扣 3 满减运费 11秒杀
        type: integer
    type: object
  models.OrderSubmitRequest:
    properties:
      order:
        allOf:
        - $ref: '#/definitions/models.Order'
        description: 订单
      order_products:
        description: 商品
        items:
          $ref: '#/definitions/models.OrderProductModel'
        type: array
      order_promotions:
        description: 参与优惠活动信息
        items:
          $ref: '#/definitions/models.OrderPromotion'
        type: array
      pay_info:
        allOf:
        - $ref: '#/definitions/models.OrderPayInfo'
        description: 支付信息
      warehouse_type:
        description: 药品仓属性
        type: integer
    type: object
  models.OrderSubmitResponse:
    properties:
      cannot_products:
        description: 无法下单商品
        items:
          $ref: '#/definitions/models.CannotSumbitProduct'
        type: array
      code:
        description: 状态码
        type: integer
      create_time:
        description: 创建订单时间
        type: integer
      doctor_code:
        description: 在线问诊订单医生Code，快速咨询为空
        type: string
      error:
        description: 错误信息
        type: string
      message:
        description: 消息
        type: string
      order_id:
        description: 提交订单成功后的订单id
        type: integer
      order_sn:
        description: 提交订单成功后，返回订单ID
        type: string
      pay_sn:
        description: 提交订单成功后的支付单号
        type: string
    type: object
  models.PayNotifyResponse:
    properties:
      code:
        type: integer
      message:
        type: string
    type: object
  models.PayRequest:
    properties:
      app_id:
        description: 1：阿闻，2：子龙，3：R1，4：互联网
        type: integer
      order_no:
        description: 订单号
        type: string
      order_type:
        description: 订单类型1普通订单(默认),2预定订单,3门店自提,4拼团订单,5门店配送,6健康计划,7保险订单,8积分订单 9周期购
          10新人专享 11预售 12新秒杀 99助力订单
        type: integer
    type: object
  models.Product:
    properties:
      image:
        type: string
      number:
        description: 购买数量
        type: integer
      order_product_id:
        type: integer
      parent_sku_id:
        description: 父商品sku
        type: string
      pay_price:
        description: 实际支付单价（美团）
        type: string
      price:
        description: 商品单价
        type: string
      product_id:
        type: integer
      product_name:
        type: string
      promotion_id:
        description: 折扣活动id
        type: integer
      refund_number:
        description: 已退数量
        type: integer
      sku_id:
        type: integer
      specs:
        type: string
      surplus_number:
        description: 可退数量
        type: integer
      surplus_refund_amount:
        description: 可退金额
        type: integer
      used_number:
        description: 虚拟商品已核销数量
        type: integer
    type: object
  models.PushOmsRequest:
    properties:
      order_sn:
        description: 订单号
        type: string
      pay_sn:
        description: 交易号
        type: string
    type: object
  models.RefundAmount:
    properties:
      costAmount:
        description: 总金额
        type: number
      refundAmount:
        description: 应退金额
        type: number
      usedOriginalAmount:
        description: 已使用项目原价总和
        type: number
    type: object
  models.RefundAmountReq:
    properties:
      batchCode:
        description: 批次号
        type: string
      ensureCode:
        description: 卡号
        type: string
      petId:
        description: 宠物编码
        type: string
      userId:
        description: 用户id
        type: string
    type: object
  models.RefundDetail:
    properties:
      can_revoke:
        description: 能否撤销，0否1是
        type: integer
      express_name:
        description: 退款物流名称
        type: string
      express_no:
        description: 退款物流号
        type: string
      need_express:
        description: 是否需要快递，0否1是
        type: integer
      order_sn:
        description: 订单编号
        type: string
      refund_account:
        description: 退款账户
        type: string
      refund_amount:
        description: 退款金额
        type: string
      refund_logs:
        description: 退款流程
        items:
          $ref: '#/definitions/models.RefundLog'
        type: array
      refund_product:
        description: 退款信息
        items:
          $ref: '#/definitions/models.Product'
        type: array
      refund_reason:
        description: 退款原因
        type: string
      refundtype:
        description: '售后类型: 1为退款,2为退货，默认为1'
        type: integer
      remaining_time:
        description: 剩余秒数
        type: integer
      return_address:
        allOf:
        - $ref: '#/definitions/models.ReturnAddress'
        description: 退货地址
      status:
        description: 售后状态
        type: integer
      status_text:
        description: 售后状态描述
        type: string
    type: object
  models.RefundDetailResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/models.RefundDetail'
      message:
        type: string
    type: object
  models.RefundHealthAmountResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/models.RefundAmount'
      message:
        type: string
    type: object
  models.RefundHealthPlanCardReq:
    properties:
      batchCode:
        description: 批次号
        type: string
      ensureCode:
        description: 卡号
        type: string
      petId:
        description: 宠物编码
        type: string
      refundAmount:
        description: 退款金额
        type: integer
    type: object
  models.RefundList:
    properties:
      can_revoke:
        description: 能否撤销，0否1是
        type: integer
      need_express:
        description: 是否需要快递，0否1是
        type: integer
      order_sn:
        description: 订单编号
        type: string
      product:
        description: 订单商品信息
        items:
          $ref: '#/definitions/models.Product'
        type: array
      refund_sn:
        description: 退款编号
        type: string
      refundtype:
        description: '申请类型: 1为退款,2为退货，默认为1'
        type: integer
      status:
        description: 售后状态:1等待商家审核 2已撤销 3退货退款成功 4等待买家退货 5买家已退货
        type: integer
      status_text:
        description: 售后状态描述
        type: string
    type: object
  models.RefundListResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/models.RefundList'
        type: array
      message:
        type: string
    type: object
  models.RefundLog:
    properties:
      desc:
        type: string
      time:
        type: string
      title:
        type: string
    type: object
  models.ReturnAddress:
    properties:
      address:
        type: string
      name:
        type: string
      phone:
        type: string
    type: object
  models.SetMealData:
    properties:
      categoryCode:
        description: 套餐编码
        type: string
      categoryDetailCode:
        description: 套餐明细编码
        type: string
      corpusTimes:
        description: 数量
        type: integer
      productName:
        description: 项目名
        type: string
    type: object
  models.SetMealReq:
    properties:
      applyAge:
        description: 适用年龄(1-幼年;2-成年;3-老年)
        type: integer
      kindOf:
        description: 宠物种类(1000 - 猫，1001 - 犬,1002 - 异宠)
        type: integer
    type: object
  models.StandardPayRequest:
    properties:
      app_id:
        type: integer
      bar_code:
        type: string
      client_ip:
        type: string
      discount:
        type: integer
      extend_info:
        type: string
      merchant_id:
        type: string
      notify_url:
        type: string
      open_id:
        type: string
      order_name:
        type: string
      order_no:
        type: string
      order_pay_type:
        type: string
      pay_amount:
        type: integer
      pay_total:
        type: integer
      product_desc:
        type: string
      product_id:
        type: string
      sign:
        type: string
      sub_app_id:
        type: string
      timestamp:
        type: integer
      trans_type:
        type: integer
      trm_id:
        type: string
      trm_sn:
        type: string
      valid_time:
        type: integer
    type: object
  models.StandardPayResponse:
    properties:
      code:
        type: integer
      data:
        $ref: '#/definitions/models.StandardPayRequest'
      message:
        type: string
    type: object
  models.VerifyCodes:
    properties:
      createTime:
        description: 订单下单时间
        type: string
      image:
        description: 商品图片
        type: string
      markingPrice:
        description: 市场价（单价 单位：分）
        type: integer
      memberId:
        description: 订单所属会员id（该订单是哪个会员买的）
        type: string
      number:
        description: 商品购买数量
        type: integer
      order_sn:
        description: 所属阿闻订单号(子订单)
        type: string
      orderStatus:
        description: 订单状态 已取消,10未付款,20已付款,30已完成
        type: integer
      payMode:
        description: 订单支付方式,1支付宝,2微信,3美团,4其他,5饿了么,6京东支付，8储值卡支
        type: integer
      payPrice:
        description: 商品均摊后实际支付单价
        type: integer
      payTime:
        description: 订单支付时间
        type: string
      paymentTotal:
        description: 购买金额（单位分）
        type: integer
      privilege:
        description: 优惠金额（暂无数值）
        type: integer
      productName:
        description: 商品名称
        type: string
      skuId:
        description: 商品sku id
        type: string
      thirdSkuId:
        description: 第三方的SkuId(货号)
        type: string
      total:
        description: 订单实际应付款金额（去掉优惠，包含运费，包装费，服务费等的金额)单位分
        type: integer
      verifyCode:
        description: 核销码
        type: string
      verifyCodeExpiryDate:
        description: 核销码有效期
        type: string
      verifyMemberId:
        description: 核销人的用户id（该订单是哪个会员核销的）
        type: string
      verifyShop:
        description: 核销门店的财务编码
        type: string
      verifyStatus:
        description: 核销状态 0未核销, 1已核销, 2已退款 不传默认为未核销
        type: integer
      verifyTime:
        description: 核销时间
        type: string
    type: object
  models.WriteOffReq:
    properties:
      member_id:
        type: string
      source:
        type: integer
      store_id:
        type: string
      user_agent:
        type: integer
      verify_code:
        type: string
    type: object
  oc.AwenOrderCancleRequest:
    properties:
      cancel_reason:
        description: 取消原因
        type: string
      order_id:
        description: 订单ID
        type: string
    type: object
  oc.AwenOrderPayInfoData:
    properties:
      add_time:
        description: 交易时间
        type: string
      order_id:
        description: 交易订单id
        type: string
      pay_price:
        description: 支付金额
        type: integer
      pay_time:
        description: 支付时间
        type: string
      status:
        description: 订单状态   0-交易中，1-交易完成，2-交易失败
        type: integer
      trade_no:
        description: 交易流水号
        type: string
    type: object
  oc.AwenOrderPayQueryResponse:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        allOf:
        - $ref: '#/definitions/oc.AwenOrderPayInfoData'
        description: 支付信息
      message:
        description: 消息
        type: string
    type: object
  oc.BadRequestResponse:
    properties:
      code:
        description: 代码 非 200 取 message 错误信息
        type: integer
      message:
        description: 错误消息
        type: string
    type: object
  oc.BaseResponse:
    properties:
      code:
        description: 状态码
        type: integer
      error:
        description: 错误信息
        type: string
      message:
        description: 消息
        type: string
    type: object
  oc.CardBaseResponse:
    properties:
      code:
        description: 状态码
        type: integer
      message:
        description: 消息
        type: string
    type: object
  oc.CardEquityReceiveReq:
    properties:
      create_time:
        description: 创建时间
        type: string
      equity_id:
        description: 权益id，type、equity_id二选一
        type: integer
      id:
        description: 权益值 优惠券id
        type: string
      is_month:
        description: 0开卡发券， 1月度领券
        type: integer
      order_sn:
        description: 会员卡权益领取 卡订单号
        type: string
      scrm_id:
        description: 前端不用传，用户id
        type: string
      sign_id:
        description: 家庭医生服务包 签约id
        type: integer
      type:
        description: 权益类型：1商城优惠券，2子龙门店券，8打折卡，与type、equity_id二选一
        type: integer
    type: object
  oc.CardNewByCodeReq:
    properties:
      code:
        description: 兑换码
        type: string
      scrm_id:
        description: 前端不用传，用户id
        type: string
      user_agent:
        description: 前端不用传，从请求头解析
        type: integer
      user_name:
        description: 前端不用传，用户名
        type: string
    type: object
  oc.CardNewReq:
    properties:
      card_id:
        description: 卡id
        type: integer
      dis_id:
        description: 分销id
        type: integer
      dis_type:
        description: 分销类型 1-链接 2-扫码
        type: integer
      scrm_id:
        description: 前端不用传，用户id
        type: string
      source:
        description: 来源，0默认，1兑换码激活，2门店开卡
        type: integer
      user_agent:
        description: 前端不用传，从请求头解析
        type: integer
      user_name:
        description: 前端不用传，用户名
        type: string
    type: object
  oc.CardNewRes:
    properties:
      code:
        description: 状态码
        type: integer
      deadline:
        description: 支付截止时间戳
        type: integer
      message:
        description: 消息
        type: string
      order_sn:
        description: 订单号
        type: string
    type: object
  oc.CardServicePackActivityReq:
    properties:
      member_id:
        description: 前端不用传，用户id
        type: string
      pet_age:
        description: 宠物年龄，记录宠物当时的年龄
        type: string
      pet_avatar:
        description: 宠物头像
        type: string
      pet_birthday:
        description: 生日例如：2021-10-01 00:00:00
        type: string
      pet_id:
        description: 宠物ID
        type: string
      pet_kind_of:
        description: 宠物种类大分类
        type: string
      pet_name:
        description: 宠物名字
        type: string
      pet_sex:
        description: 性别：1GG,2MM
        type: integer
      pet_variety:
        description: 种类
        type: string
      sign_id:
        description: 签约id
        type: integer
    type: object
  oc.Company:
    properties:
      corpAccount:
        description: 银行卡号
        type: string
      corpAddress:
        description: 企业地址
        type: string
      corpBank:
        description: 开户银行
        type: string
      corpName:
        description: 公司名称
        type: string
      corpTaxNo:
        description: 税号
        type: string
      corpTelephone:
        description: 企业电话
        type: string
      speedCode:
        description: 公司编码
        type: string
    type: object
  oc.CompanyList:
    properties:
      code:
        description: 公司编码
        type: string
      name:
        description: 公司名称
        type: string
    type: object
  oc.ConsultMemberPetInfo:
    properties:
      member_avatar:
        description: 用户头像
        type: string
      member_id:
        description: 用户id
        type: string
      member_mobile:
        description: 用户手机号
        type: string
      member_name:
        description: 用户名称
        type: string
      pet_age:
        description: 宠物年龄
        type: string
      pet_avatar:
        description: 宠物头像
        type: string
      pet_birthday:
        description: 宠物生日
        type: string
      pet_id:
        description: 宠物id
        type: string
      pet_kindof:
        description: 宠物分类
        type: string
      pet_kindof_code:
        description: 宠物分类类code
        type: string
      pet_name:
        description: 宠物名称
        type: string
      pet_neutering:
        description: 是否绝育
        type: integer
      pet_sex:
        description: 宠物性别
        type: integer
      pet_variety:
        description: 宠物种类
        type: string
      pet_variety_code:
        description: 宠物种类code
        type: string
    type: object
  oc.CreateInvoiceRequest:
    properties:
      bank_name:
        description: 开户银行
        type: string
      bank_number:
        description: 银行卡号
        type: string
      channel:
        description: 前端不需要传
        type: integer
      company_address:
        description: 企业地址
        type: string
      company_code:
        description: 前端不需要传
        type: string
      company_name:
        description: 企业名称
        type: string
      email:
        description: 电子邮箱
        type: string
      identify_number:
        description: 纳税人识别号
        type: string
      invoice_tt:
        description: 发票类型 1个人、2企业
        type: integer
      mobile:
        description: 手机号码/企业电话
        type: string
      order_sn:
        description: 订单号
        type: string
      pay_time:
        description: 支付时间
        type: integer
      scrm_id:
        description: 前端不需要传，仅用于标记用户
        type: string
    type: object
  oc.ExpressCompany:
    properties:
      company:
        description: 物流公司名称
        type: string
      id:
        description: id
        type: string
    type: object
  oc.ExpressCompanyListResponse:
    properties:
      code:
        description: 状态码
        type: integer
      dataList:
        items:
          $ref: '#/definitions/oc.ExpressCompany'
        type: array
      error:
        description: 错误信息
        type: string
      message:
        description: 消息
        type: string
    type: object
  oc.ExpressInfo:
    properties:
      datetime:
        description: 时间点
        type: string
      info:
        description: 物流信息
        type: string
      status:
        description: 状态
        type: string
    type: object
  oc.ExpressInfoResponse:
    properties:
      code:
        description: 状态码
        type: integer
      dataList:
        description: 物流信息列表
        items:
          $ref: '#/definitions/oc.ExpressInfo'
        type: array
      error:
        description: 错误信息
        type: string
      express_company:
        description: 物流公司
        type: string
      express_no:
        description: 物流号
        type: string
      message:
        description: 消息
        type: string
      order_no:
        description: 订单id
        type: string
    type: object
  oc.ExpressInfoUpdateRequest:
    properties:
      express_code:
        description: 物流公司代码，正向订单必填
        type: string
      express_company_id:
        description: 物流公司id 售后单必填
        type: integer
      express_info:
        description: 物流信息
        type: string
      express_no:
        description: 物流号(或快递单号)
        type: string
      order_express_id:
        description: 快递单记录id 正向单必填
        type: integer
      order_id:
        description: 订单号 正向订单必填
        type: string
      refund_sn:
        description: 服务单号  售后单必填
        type: string
      update_order_type:
        description: 更新订单类型 0售后单 1正向单
        type: integer
    type: object
  oc.ExternalResponse:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        description: json 格式数据
        type: string
      error:
        description: 错误信息
        type: string
      external_code:
        description: 外部接口返回错误码（例如美配，美团）
        type: string
      message:
        description: 消息
        type: string
    type: object
  oc.HealthSubOrderRequest:
    properties:
      category_code:
        description: 套餐编码
        type: string
      category_name:
        description: 套餐名称
        type: string
      ensure_code:
        description: 卡号
        type: string
      member_name:
        description: 会员名称
        type: string
      member_tel:
        description: member_tel
        type: string
      pay_money:
        description: 支付金额
        type: integer
      scrm_pet_id:
        description: scrm 宠物Id
        type: string
      scrm_user_id:
        description: scrm用户Id
        type: string
    type: object
  oc.HealthSubOrderResponse:
    properties:
      code:
        description: 状态码
        type: integer
      error:
        description: 错误信息
        type: string
      message:
        description: 消息
        type: string
      order_sn:
        description: 订单号
        type: string
    type: object
  oc.InvoiceCompanyInfoResponse:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        $ref: '#/definitions/oc.Company'
      message:
        description: 错误信息
        type: string
    type: object
  oc.InvoiceDetailData:
    properties:
      apply:
        allOf:
        - $ref: '#/definitions/oc.CreateInvoiceRequest'
        description: 申请发票信息
      created_at:
        description: 申请时间
        type: string
      fail_reason:
        description: 失败原因，当开票失败（status = 3）时返回
        type: string
      invoices:
        description: 发票链接，多张用逗号分割
        type: string
      status:
        description: 开票状态，0未开票、1开票成功、2开票中、3开票失败
        type: integer
    type: object
  oc.InvoiceDetailResponse:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        $ref: '#/definitions/oc.InvoiceDetailData'
      message:
        description: 消息
        type: string
    type: object
  oc.InvoiceResponse:
    properties:
      code:
        description: 状态码
        type: integer
      message:
        description: 消息
        type: string
    type: object
  oc.InvoiceSendEmailRequest:
    properties:
      email:
        type: string
      order_sn:
        description: 订单号
        type: string
      scrm_id:
        description: 前端不需要传，仅用于rpc标记用户
        type: string
    type: object
  oc.InvoiceStatusResponse:
    properties:
      code:
        description: 状态码
        type: integer
      invoice_status:
        description: 0未开票、1开票成功、2开票中、3开票失败、8开票关闭、9开票过期
        type: integer
      message:
        description: 消息
        type: string
    type: object
  oc.InvoiceTitleAddRequest:
    properties:
      buyer_name:
        description: 企业名称/个人抬头
        type: string
      buyer_tax_num:
        description: 企业识别号
        type: string
      customer_phone:
        description: 手机号
        type: string
      notify_email:
        description: 接收邮件地址
        type: string
      scrm_id:
        type: string
    type: object
  oc.InvoiceTitleAddResponse:
    properties:
      code:
        description: 状态码
        type: integer
      message:
        description: 消息
        type: string
    type: object
  oc.InvoiceTitleEditRequest:
    properties:
      buyer_name:
        description: 企业名称/个人抬头
        type: string
      buyer_tax_num:
        description: 企业识别号
        type: string
      customer_id:
        description: 客户id
        type: integer
      customer_phone:
        description: 手机号
        type: string
      notify_email:
        description: 接收邮件地址
        type: string
      scrm_id:
        type: string
    type: object
  oc.InvoiceTitleEditResponse:
    properties:
      code:
        description: 状态码
        type: integer
      message:
        description: 消息
        type: string
    type: object
  oc.InvoiceTitleListRequest:
    properties:
      scrm_id:
        type: string
    type: object
  oc.InvoiceTitleListResponse:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        items:
          $ref: '#/definitions/oc.InvoiceTitleListResponse_InvoiceTitleData'
        type: array
      message:
        description: 消息
        type: string
    type: object
  oc.InvoiceTitleListResponse_InvoiceTitleData:
    properties:
      buyer_name:
        type: string
      buyer_tax_num:
        type: string
      customer_id:
        type: integer
      customer_phone:
        type: string
      notify_email:
        type: string
      update_time:
        type: string
    type: object
  oc.OrderPrescribeCheckReq:
    properties:
      finance_code:
        description: 门店财务编码
        type: string
      scrm_id:
        description: 前端不需要传
        type: string
      skus:
        description: 药品信息
        items:
          $ref: '#/definitions/oc.OrderPrescribeSkuNum'
        type: array
    type: object
  oc.OrderPrescribeCheckRes:
    properties:
      code:
        description: 状态码 200成功 非200失败
        type: integer
      data:
        $ref: '#/definitions/oc.OrderPrescribeCheckRes_Data'
      message:
        description: 错误描述
        type: string
    type: object
  oc.OrderPrescribeCheckRes_Data:
    properties:
      consult_order_sn:
        description: 处方单号
        type: string
      skus:
        description: 药品信息，如果存在药品且没有处方单号，则表示可以开处方
        items:
          $ref: '#/definitions/oc.OrderPrescribeSkuNum'
        type: array
    type: object
  oc.OrderPrescribeRes:
    properties:
      code:
        description: 状态码 200成功 非200失败
        type: integer
      consult_order_sn:
        description: 处方单号
        type: string
      message:
        description: 错误描述
        type: string
    type: object
  oc.OrderPrescribeSkuNum:
    properties:
      num:
        description: 数量
        type: integer
      sku_id:
        description: sku_id
        type: integer
    type: object
  oc.PreSalePay:
    properties:
      endTime:
        description: 尾款支付结束时间
        type: string
      isVirtual:
        description: 是否虚拟订单 0 否 1是
        type: integer
      remarks:
        description: 温馨提醒
        type: string
      startTime:
        description: 尾款支付开始时间
        type: string
    type: object
  oc.PrescriptionDiagnose:
    properties:
      diagnose_content:
        description: 诊断内容
        type: string
      disease_code:
        description: 病种编号
        type: string
      is_sure:
        description: 是否拟：1确定 2不确定，写死1
        type: integer
      position:
        description: 位置：0-无 1-左、2-右、3-上、4-下、5-单侧、6-双侧，写死0
        type: integer
    type: object
  oc.PushTemplateRequest:
    properties:
      openId:
        description: 用户ID
        type: string
      orderSn:
        description: 订单编号
        type: string
      org_id:
        description: 主体:1-阿闻，2-极宠家，3-福码购
        type: integer
      preSalePay:
        allOf:
        - $ref: '#/definitions/oc.PreSalePay'
        description: 预售尾款支付通知
      pushType:
        description: 类型(1=>发送退款成功通知, 2=>发送退款失败通知, 3=>发送退款状态通知, 4=>推送尾款支付提醒通知)
        type: integer
      refundFail:
        allOf:
        - $ref: '#/definitions/oc.RefundFail'
        description: 发送退款失败通知
      refundStatus:
        allOf:
        - $ref: '#/definitions/oc.RefundStatus'
        description: 发送退款状态通知
      refundSuccess:
        allOf:
        - $ref: '#/definitions/oc.RefundSuccess'
        description: 发送退款成功通知
      remarks:
        description: 备注
        type: string
      templateId:
        description: 模板ID
        type: string
    type: object
  oc.QueryInvoiceCompanyResponse:
    properties:
      code:
        description: 状态码
        type: integer
      data:
        items:
          $ref: '#/definitions/oc.CompanyList'
        type: array
      message:
        description: 错误信息
        type: string
    type: object
  oc.QueryMallVirtualOrderWriteOffCodesResponse:
    properties:
      code:
        description: 状态码 200成功 非200失败
        type: integer
      data:
        description: 列表数据
        items:
          $ref: '#/definitions/oc.QueryMallVirtualOrderWriteOffCodesResponse_Data'
        type: array
      error:
        description: 错误信息
        type: string
      message:
        description: 错误描述
        type: string
      total:
        description: 总记录数
        type: integer
    type: object
  oc.QueryMallVirtualOrderWriteOffCodesResponse_Data:
    properties:
      goods_id:
        description: sku_id
        type: integer
      goods_name:
        description: 商品名称
        type: string
      goods_num:
        description: 商品数量
        type: integer
      goods_price:
        description: 商品价格
        type: number
      order_sn:
        description: 订单编号
        type: string
      pay_price:
        description: 实付金额
        type: number
      payment_time:
        description: 支付时间(秒)
        type: integer
      vr_code:
        description: 兑换码
        type: string
      vr_indate:
        description: 过期时间戳(秒)
        type: integer
    type: object
  oc.QuerySignIdRes:
    properties:
      code:
        description: 状态码
        type: integer
      message:
        description: 消息
        type: string
      sign_id:
        description: 签约id
        type: integer
    type: object
  oc.RefundFail:
    properties:
      refundId:
        description: 退款ID
        type: string
      refundSn:
        description: 退款订单
        type: string
      refundType:
        description: 退款类型
        type: string
      status:
        description: 状态
        type: string
    type: object
  oc.RefundInvoiceRequest:
    properties:
      channel:
        type: integer
      refund_sn:
        type: string
    type: object
  oc.RefundOrderApplyRequest:
    properties:
      activity_pt_amount:
        description: 平台承担的活动优惠金额
        type: number
      applyOpUserType:
        description: |-
          推送当前仅退款或退货退款流程的发起方，是用户还是商家；仅适用于支持退货退款的商家。
          1-用户
          2-商家
          3-客服
          4-BD
          5-系统
          6-开放平台
        type: string
      channel_id:
        description: 来源渠道id（datacenter.platform_channel表）1阿闻到家  2美团 3饿了么 4京东到家 5阿闻电商
          6门店
        type: integer
      delivery_price:
        description: 本次退款的配送费
        type: number
      external_order_id:
        description: 外部订单号 Id
        type: string
      full_refund:
        description: 1整单退款 2部分退款
        type: integer
      is_cancal_order:
        description: 京东到家 专属 1：取消订单 2正常售后订单
        type: integer
      old_refund_sn:
        description: 渠道退款单号
        type: string
      operation_type:
        description: 操作描述
        type: string
      operation_user:
        description: 操作用户
        type: string
      order_from:
        description: 渠道id(1-阿闻，2-美团，3-饿了么,4-阿闻到家 (废弃))
        type: integer
      order_id:
        description: 订单号    必镇
        type: string
      pictures:
        description: 用户申请退款时上传的退款图片，多个图片url以英文逗号隔开
        type: string
      reason:
        description: 因***原因部分退款 必镇
        type: string
      refund_amount:
        description: 退款总金额
        type: number
      refund_code:
        description: 京东到家 专属 售后原因id（201:商品质量问题，202:送错货，203:缺件少件，501:全部商品未收到，208:包装脏污有破损，207:缺斤少两，210:商家通知我缺货，303:实物与原图不符，502:未在时效内送达）
        type: string
      refund_order_goods_data:
        description: 部分退款商品sku数据集合的json格式数组
        items:
          $ref: '#/definitions/oc.RefundOrderGoodsData'
        type: array
      refund_order_sn:
        description: 售后单号
        type: string
      refund_remark:
        description: 售后单备注
        type: string
      refund_type:
        description: 退款类型1为退款,2为退货  目前不支持退货
        type: integer
      res_type:
        description: |-
          申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
          支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
        type: string
      service_type:
        description: 区分是否已开通退货退款售后业务。 未开通的场景： 0-退款流程或申诉流程 已开通场景： 1-仅退款流程 2-退款退货流程
        type: string
      shop_id:
        description: 门店id(财务编码)
        type: string
    type: object
  oc.RefundOrderApplyResponse:
    properties:
      code:
        description: code
        type: integer
      create_time:
        description: 下单时间(13位，毫秒)
        type: integer
      error:
        description: 错误信息
        type: string
      message:
        description: 消息提示
        type: string
      refund_order_sn:
        description: 退款退货单号
        type: string
    type: object
  oc.RefundOrderCancelRequest:
    properties:
      operation_type:
        description: 操作类型
        type: string
      operation_user:
        description: 操作用户
        type: string
      reason:
        description: 原因
        type: string
      refund_order_sn:
        type: string
      res_type:
        description: |-
          申退款状态类型，参考值：0-等待处理中；1-商家驳回退款请求；2-商家同意退款；3-客服驳回退款请求；4-客服帮商家同意退款；5-超时未处理系统自动同意；6-系统自动确认；7-用户取消退款申请；8-用户取消退款申诉。
          支持退货退款业务的门店，订单退款消息中notify_type和res_type字段不返回。未开通退货退款业务的门店，订单退款保持原逻辑不变。
        type: string
    type: object
  oc.RefundOrderGoodsData:
    properties:
      app_food_code:
        description: APP方商品id，即商家中台系统里商品的编码（spu_code值），字段信息限定长度不超过128个字符。
        type: string
      goods_name:
        description: 退款商品名称
        type: string
      order_product_id:
        description: 订单商品表主键id
        type: integer
      parent_sku_id:
        description: 订单商品的父sku 组合商品的子商品该字段有值
        type: string
      platform_sku_id:
        description: 订单商品的父sku 组合商品的子商品该字段有值
        type: integer
      promotion_type:
        description: 京东到家标识 商品促销类型（1203满赠，6买赠，1正品）
        type: integer
      quantity:
        description: 数量
        type: integer
      refund_amount:
        description: 总金额
        type: string
      refund_price:
        description: 商品单价  如本次部分退款是按件部分退，则此金额为单件商品sku的退款金额
        type: number
      refund_reality_price:
        description: 实际支付单价 当前商品sku参加商品类活动优惠后的金额（单价），单位是元。
        type: number
      sku_id:
        description: 商品唯一标识
        type: string
      spec:
        description: 商品sku的规格名称
        type: string
      sub_biz_order_id:
        description: 饿了么专属字段，子订单ID，可区分同商品ID的不同属性，订单逆向操作必须字段
        type: string
    type: object
  oc.RefundStatus:
    properties:
      refundId:
        description: 退款ID
        type: string
      refundType:
        description: 退款类型
        type: string
      status:
        description: 状态
        type: string
    type: object
  oc.RefundSuccess:
    properties:
      refundAmount:
        description: 退款金额
        type: string
      refundId:
        description: 退款ID
        type: string
      refundTime:
        description: 退款时间
        type: string
      refundType:
        description: 退款类型
        type: string
    type: object
  oc.RiderLocationRequest:
    properties:
      delivery_id:
        description: 配送活动标识
        type: integer
      delivery_service_code:
        description: |-
          配送服务代码，详情见合同
          飞速达: 4002
          快速达: 4011
          及时达: 4012
          集中送: 4013
          自由达: 4014
        type: integer
      mt_peisong_id:
        description: 美团配送内部订单id，最长不超过32个字符
        type: string
    type: object
  oc.SectionDto:
    properties:
      childSectionDto:
        description: 子区域
        items:
          $ref: '#/definitions/oc.SectionDto'
        type: array
      id:
        description: 区域Id
        type: integer
      parentId:
        description: 上级部门Id
        type: integer
      sectionName:
        description: 名称
        type: string
    type: object
  oc.SectionQueryRequest:
    properties:
      isChild:
        description: 是否加载子区域
        type: boolean
      parentId:
        description: 上级ID
        type: integer
    type: object
  oc.SectionQueryResponse:
    properties:
      code:
        description: 代码 非 200 取 message 错误信息
        type: integer
      data:
        description: 省市列表
        items:
          $ref: '#/definitions/oc.SectionDto'
        type: array
    type: object
  oc.UpetDjConfirmRequest:
    properties:
      orderId:
        description: 订单Id
        type: string
    type: object
  oc.UpetDjConfirmResponse:
    properties:
      code:
        description: 代码 非 200 取 message 错误信息
        type: integer
      message:
        description: 错误信息
        type: string
    type: object
  oc.UpetDjDeliverNodesDto:
    properties:
      createTime:
        description: 节点日期
        type: string
      message:
        description: 节点说明
        type: string
    type: object
  oc.UpetDjDeliveryDto:
    properties:
      deliveryId:
        description: 配送标识
        type: integer
      deliveryIdStr:
        description: 配送标识字符串类型
        type: string
      deliveryOrderId:
        description: 配送单号
        type: string
      deliveryServiceCode:
        description: 配送服务代码
        type: string
      deliveryTime:
        description: 送达时间
        type: string
      deliveryType:
        description: 配送方式 1 快递 2 外卖 3 自提 4 同城
        type: integer
      deliveryTypeName:
        description: 配送方式名称
        type: string
      express:
        description: 订单发货记录
        items:
          $ref: '#/definitions/oc.UpetDjDeliveryDto_OrderExpress'
        type: array
      freight:
        description: 配送费
        type: number
      latitude:
        description: 收货地址维度
        type: number
      longitude:
        description: 收货地址经度
        type: number
      nodes:
        description: 配送节点信息
        items:
          $ref: '#/definitions/oc.UpetDjDeliverNodesDto'
        type: array
      pickupStationAddress:
        description: 提货点地址
        type: string
      pickupStationName:
        description: 提货点名称
        type: string
      receiveraddress:
        description: 收货地址
        type: string
      receivercity:
        description: 收货市
        type: string
      receiverdistrict:
        description: 收货区
        type: string
      receivermobile:
        description: 收货人手机号
        type: string
      receivername:
        description: 收货人名称
        type: string
      receiverphone:
        description: 收货电话
        type: string
      receiverstate:
        description: 收货省
        type: string
      shopLatitude:
        description: 店铺维度
        type: number
      shopLongitude:
        description: 店铺经度
        type: number
    type: object
  oc.UpetDjDeliveryDto_OrderExpress:
    properties:
      express_name:
        description: 快递名称
        type: string
      express_no:
        description: 快递单号
        type: string
      num:
        description: 商品数量
        type: integer
    type: object
  oc.UpetDjOrderDetailRequest:
    properties:
      member_id:
        type: string
      orderId:
        description: 订单Id
        type: string
      orderSn:
        description: 订单号码 orderId 与 orderSn任选其一
        type: string
    type: object
  oc.UpetDjOrderDetailResponse:
    properties:
      code:
        description: 代码 非 200 取 message 错误信息
        type: integer
      deliveryInfo:
        allOf:
        - $ref: '#/definitions/oc.UpetDjDeliveryDto'
        description: 配送信息
      message:
        description: 错误信息
        type: string
      orderListInfo:
        allOf:
        - $ref: '#/definitions/oc.UpetDjOrderDto'
        description: 订单信息
      refundInfo:
        allOf:
        - $ref: '#/definitions/oc.UpetDjRefundDto'
        description: 退款信息
      refundProductList:
        description: 退款商品列表
        items:
          $ref: '#/definitions/oc.UpetDjRefundProductDto'
        type: array
      remainSeconds:
        description: 剩余秒数
        type: integer
    type: object
  oc.UpetDjOrderDto:
    properties:
      business_times:
        description: 店铺营业时间段
        type: string
      channel_id:
        description: 渠道id（datacenter.platform_channel表）,订单来源 1-阿闻到家 2-美团 3-饿了么 4-京东到家
          5-阿闻电商 6-门店 7-百度 8-H5, 9-互联网医疗
        type: integer
      childOrderList:
        description: 阿闻订单子订单列表
        items:
          $ref: '#/definitions/oc.UpetDjOrderDto'
        type: array
      consult_order_sn:
        description: 处方单号
        type: string
      createDateTime:
        description: 下单日期
        type: string
      enable_cancel:
        description: 是否可以取消订单
        type: boolean
      group_info:
        $ref: '#/definitions/oc.UpetDjOrderDto_GroupInfo'
      is_apply_btn:
        description: 是否显示退款按钮
        type: boolean
      is_pay:
        type: integer
      is_virtual:
        description: 是否是虚拟订单，0否1是
        type: integer
      no_display_after_payment:
        description: |-
          阿闻宠团团这个门店（财务编码：QZC00300）的所有订单，在订单“已支付”以后就不显示“申请退款”和“取消订单”的按钮，如果要退款就只能让客服在后台进行退款
          0默认显示 1不显示
        type: integer
      orderId:
        description: 订单Id
        type: string
      orderNo:
        description: 订单号码
        type: string
      parent_order_sn:
        description: 订单号码
        type: string
      payMode:
        description: 支付方式 0 待支付 1支付宝  2微信 3美团 4其他
        type: integer
      pickup_code:
        description: 取货码
        type: string
      privilege:
        description: 优惠金额
        type: number
      productList:
        description: 阿闻订单商品列表
        items:
          $ref: '#/definitions/oc.UpetDjOrderProductDto'
        type: array
      remarks:
        description: 订单备注
        type: string
      returnNo:
        description: 最近一次售后记录
        type: string
      returnState:
        description: 订单退款状态  1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败
        type: integer
      returnWay:
        description: 有售后记录 0 无 11 整单退款 12 整单退货 21 部分退款 22 部分退货
        type: integer
      shop_address:
        description: 店铺地址
        type: string
      shopId:
        description: 店铺Id
        type: string
      shopMobile:
        description: 店铺联系电话
        type: string
      shopName:
        description: 店铺名字
        type: string
      state:
        allOf:
        - $ref: '#/definitions/oc.UpetDjOrderState'
        description: 订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单
          24 骑手已接单 25 骑手配送中 26 商家配送中 27 商家手动已接单 28 备货完成, 待买家自提 29 商家已接单, 备货中 30 已完成,
          31拆单中, 32拆单失败, 33未核销, 34待骑手接单，50已发货
      stock_up_time:
        description: 店铺备货时长
        type: integer
      totalMoney:
        description: 订单总金额
        type: number
    type: object
  oc.UpetDjOrderDto_GroupInfo:
    properties:
      deliver_days:
        description: 最晚N日内送达
        type: string
      final_take_type:
        description: 团长代收状态 0不代收 1代收
        type: integer
      group_address:
        description: 团员信息 地址
        type: string
      group_id:
        description: 团ID
        type: integer
      group_leader:
        description: 是否团长 1是 0否
        type: integer
      group_mobile:
        description: 团员信息 手机
        type: string
      group_name:
        description: 团员信息 名字
        type: string
      group_status:
        description: 社区团购活动状态 0开团中 1拼团成功 2拼团失败
        type: integer
      receiver_address:
        description: 收件地址
        type: string
      receiver_city:
        description: 收件市
        type: string
      receiver_district:
        description: 收件区
        type: string
      receiver_mobile:
        description: 收件手机
        type: string
      receiver_name:
        description: 收件人
        type: string
      receiver_state:
        description: 收件省
        type: string
    type: object
  oc.UpetDjOrderIsAutoPrintQueryRequest:
    properties:
      orderSn:
        description: 订单号码
        type: string
    type: object
  oc.UpetDjOrderIsAutoPrintQueryResponse:
    properties:
      code:
        description: 代码 非 200 取 message 错误信息
        type: integer
      isAutoPrint:
        description: 是否自动打印
        type: boolean
      message:
        description: 错误信息
        type: string
    type: object
  oc.UpetDjOrderProductDto:
    properties:
      childProductList:
        description: 子商品列表
        items:
          $ref: '#/definitions/oc.UpetDjOrderProductDto'
        type: array
      is_prescribed_drug:
        description: 是否处方药
        type: integer
      parentProductId:
        description: 上级productid
        type: string
      parentSkuId:
        description: 上级skuid
        type: string
      productActaulMoney:
        description: 总实收
        type: number
      productActualPrice:
        description: 商品实际单价
        type: number
      productCategoryId:
        description: 分类
        type: string
      productCount:
        description: 数量
        type: integer
      productId:
        description: 商品Id
        type: string
      productName:
        description: 商品名称
        type: string
      productPic:
        description: 商品Logo
        type: string
      productPrice:
        description: 商品店铺单价
        type: number
      productSpecifica:
        description: 规格
        type: string
      productType:
        description: 商品类别1-实物商品2-虚拟商品3-组合商品
        type: integer
      promotionId:
        description: 参与限时折扣的活动id
        type: integer
      skuId:
        description: skuid
        type: string
      verifyCodeList:
        description: 核销码列表
        items:
          $ref: '#/definitions/oc.UpetDjProductVerifyCodeDto'
        type: array
    type: object
  oc.UpetDjOrderQueryRequest:
    properties:
      memberId:
        description: 登录会员Id
        type: string
      orderSn:
        description: 订单号
        type: string
      pageIndex:
        description: 页索引
        type: integer
      pageSize:
        description: 页大小
        type: integer
      shopId:
        description: 店铺Id
        items:
          type: string
        type: array
      state:
        allOf:
        - $ref: '#/definitions/oc.UpetDjOrderState'
        description: 订单状态 0 全部订单 10 待付款 11 订单已取消 20 已付款 21 付款后取消 22 店铺待接单 23 店铺已接单
          24 骑手已接单 25 骑手配送中 26 商家配送中 30 已完成
    type: object
  oc.UpetDjOrderQueryResponse:
    properties:
      code:
        description: 代码 非 200 取 message 错误信息
        type: integer
      data:
        description: 当前页数据
        items:
          $ref: '#/definitions/oc.UpetDjOrderDto'
        type: array
      hasMore:
        description: 是否可以加载更多
        type: boolean
      total:
        description: 总订单数据
        type: integer
    type: object
  oc.UpetDjOrderState:
    enum:
    - 0
    - 10
    - 11
    - 20
    - 21
    - 22
    - 23
    - 24
    - 25
    - 26
    - 27
    - 28
    - 29
    - 30
    - 31
    - 32
    - 33
    - 34
    - 41
    - 42
    - 43
    - 46
    - 47
    - 48
    - 49
    - 50
    - 51
    type: integer
    x-enum-varnames:
    - UpetDjOrderState_all
    - UpetDjOrderState_unPay
    - UpetDjOrderState_unPayCancel
    - UpetDjOrderState_payed
    - UpetDjOrderState_payedCancel
    - UpetDjOrderState_payedWaitShopReceive
    - UpetDjOrderState_payedShopReceived
    - UpetDjOrderState_deliveried
    - UpetDjOrderState_delivering
    - UpetDjOrderState_shipping
    - UpetDjOrderState_payedShopReceivedManual
    - UpetDjOrderState_buyerSelfCollection
    - UpetDjOrderState_payedShopReceivedPicking
    - UpetDjOrderState_finished
    - UpetDjOrderState_splitIng
    - UpetDjOrderState_splitFail
    - UpetDjOrderState_unVerify
    - UpetDjOrderState_waitDeliveried
    - UpetDjOrderState_refunding
    - UpetDjOrderState_refundClosed
    - UpetDjOrderState_refundSuccess
    - UpetDjOrderState_refundFirstPass
    - UpetDjOrderState_refundFinalPass
    - UpetDjOrderState_refundFail
    - UpetDjOrderState_refundUndo
    - UpetDjOrderState_expressWaitingDelivery
    - UpetDjOrderState_expressShipped
  oc.UpetDjProductVerifyCodeDto:
    properties:
      createTime:
        description: 创建时间
        type: string
      id:
        description: 核销Id
        type: integer
      orderSn:
        description: 所属子订单号
        type: string
      productId:
        description: 商品Id
        type: string
      updateTime:
        description: 最后更新时间
        type: string
      verifyCode:
        description: 核销码
        type: string
      verifyCodeExpiryDate:
        description: 核销码有效期
        type: string
      verifyShop:
        description: 核销地点财务编码
        type: string
      verifyStatus:
        description: 核销码状态 0未核销，1已核销，2已退款
        type: integer
      verifyTime:
        description: 核销时间
        type: string
    type: object
  oc.UpetDjRefundDto:
    properties:
      refundMoney:
        description: 总退款金额
        type: number
      refundNo:
        description: 最近退款单号
        type: string
      refundState:
        description: 退款单状态 1:退款中 2:退款关闭 3:退款成功,6:初审通过,7:终审通过,8:退款失败
        type: integer
    type: object
  oc.UpetDjRefundProductDto:
    properties:
      productId:
        description: 退款商品Id
        type: string
      refundCount:
        description: 最终退款数量
        type: integer
    type: object
host: **********:7040
info:
  contact: {}
  description: 这里是描述
  title: 项目接口文档
  version: "1.0"
paths:
  /order-api/aftersale/apply/list:
    get:
      consumes:
      - text/plain
      parameters:
      - description: 订单编号
        in: query
        name: order_sn
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ApplyListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: 售后申请单列表
      tags:
      - 订单售后相关
  /order-api/aftersale/refund/detail:
    get:
      consumes:
      - text/plain
      parameters:
      - description: 退款单号
        in: query
        name: refund_sn
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.RefundDetailResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: 服务单详情
      tags:
      - 订单售后相关
  /order-api/aftersale/refund/list:
    get:
      consumes:
      - text/plain
      parameters:
      - description: 订单/服务单编号
        in: query
        name: order_refund_sn
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.RefundListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: 售后服务单列表
      tags:
      - 订单售后相关
  /order-api/aftersale/refund/test:
    get:
      responses: {}
  /order-api/card/equity-receive:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.CardEquityReceiveReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
      summary: 权益领取
      tags:
      - 会员卡/服务包
  /order-api/card/new:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.CardNewReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.CardNewRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.CardNewRes'
      summary: 会员卡/服务包 开卡
      tags:
      - 会员卡/服务包
  /order-api/card/new-by-code:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.CardNewByCodeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
      summary: 会员卡通过卡密开卡
      tags:
      - 会员卡/服务包
  /order-api/card/query-sign-id:
    get:
      consumes:
      - application/json
      parameters:
      - description: 订单号
        in: query
        name: order_sn
        type: string
      - description: 前端不用传，用户id
        in: query
        name: scrm_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.QuerySignIdRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.QuerySignIdRes'
      summary: 查询签约ID
      tags:
      - 会员卡/服务包
  /order-api/card/service-pack-activity:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.CardServicePackActivityReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.CardBaseResponse'
      summary: 服务包激活
      tags:
      - 会员卡/服务包
  /order-api/health/cards-list:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: EnsureCardsListReq
        required: true
        schema:
          $ref: '#/definitions/models.EnsureCardsListReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.EnsureCardsList'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.EnsureCardsList'
      summary: 健康管理- 卡权益查询
      tags:
      - 健康管理
  /order-api/health/company-category:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: HealthSubOrderRequest
        required: true
        schema:
          $ref: '#/definitions/models.CompanyCategoryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.GetCompanyCategoryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.GetCompanyCategoryResponse'
      summary: 获取健康管理订阅套餐信息
      tags:
      - 健康管理
  /order-api/health/get:
    get:
      responses: {}
  /order-api/health/order-submit:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: HealthSubOrderRequest
        required: true
        schema:
          $ref: '#/definitions/oc.HealthSubOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.HealthSubOrderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.HealthSubOrderResponse'
      summary: 健康管理订阅订单提交
      tags:
      - 健康管理
  /order-api/health/refund-amount:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: RefundHealthPlanCardRequest
        required: true
        schema:
          $ref: '#/definitions/models.RefundAmountReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.RefundHealthAmountResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.RefundHealthAmountResponse'
      summary: 健康管理- 退款预算
      tags:
      - 健康管理
  /order-api/health/refund-card:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: RefundHealthPlanCardReq
        required: true
        schema:
          $ref: '#/definitions/models.RefundHealthPlanCardReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 健康管理-退卡
      tags:
      - 健康管理
  /order-api/health/set-meal:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: HealthSubOrderRequest
        required: true
        schema:
          $ref: '#/definitions/models.SetMealReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.GetSetMealsRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.GetSetMealsRes'
      summary: 获取健康管理订阅套餐信息（带详情）
      tags:
      - 健康管理
  /order-api/invoice-title/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: InvoiceTitleAdd
        required: true
        schema:
          $ref: '#/definitions/oc.InvoiceTitleAddRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceTitleAddResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceTitleAddResponse'
      summary: 添加票抬头
      tags:
      - 发票抬头
  /order-api/invoice-title/list:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: InvoiceTitleList
        required: true
        schema:
          $ref: '#/definitions/oc.InvoiceTitleListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceTitleListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceTitleListResponse'
      summary: 发票抬头列表
      tags:
      - 发票抬头
  /order-api/invoice/apply:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: InvoiceApply
        required: true
        schema:
          $ref: '#/definitions/oc.CreateInvoiceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceResponse'
      summary: 发票申请
      tags:
      - 开票
  /order-api/invoice/companies:
    get:
      consumes:
      - application/json
      parameters:
      - description: 搜索关键字
        in: query
        name: keyword
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.QueryInvoiceCompanyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: 模糊查询公司
      tags:
      - 开票
  /order-api/invoice/company-info:
    get:
      consumes:
      - application/json
      parameters:
      - description: 企业编码
        in: query
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceCompanyInfoResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceCompanyInfoResponse'
      summary: 通过编码查询企业信息
      tags:
      - 开票
  /order-api/invoice/detail:
    get:
      consumes:
      - application/json
      parameters:
      - description: 订单号
        in: query
        name: order_sn
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceDetailResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceDetailResponse'
      summary: 发票详情
      tags:
      - 开票
  /order-api/invoice/refund:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: InvoiceSendEmail
        required: true
        schema:
          $ref: '#/definitions/oc.RefundInvoiceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceResponse'
      summary: 发票退款，红冲
      tags:
      - 开票
  /order-api/invoice/send-email:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: InvoiceSendEmail
        required: true
        schema:
          $ref: '#/definitions/oc.InvoiceSendEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceResponse'
      summary: 发送邮件
      tags:
      - 开票
  /order-api/invoice/status:
    get:
      consumes:
      - application/json
      parameters:
      - description: 订单号
        in: query
        name: order_sn
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.InvoiceStatusResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.InvoiceStatusResponse'
      summary: 开票状态
      tags:
      - 开票
  /order-api/oms/delivered:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.PushOmsRequest
        required: true
        schema:
          $ref: '#/definitions/models.PushOmsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: oms订单出库成功 回调
      tags:
      - OMS
  /order-api/order/OrderPayCompleteTemporary:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.OrderPayRequest
        required: true
        schema:
          $ref: '#/definitions/models.OrderPayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OrderPayResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.OrderPayResponse'
      summary: 完成订单
      tags:
      - 订单
  /order-api/order/cancle:
    post:
      parameters:
      - description: ' '
        in: body
        name: AwenOrderCancleRequest
        required: true
        schema:
          $ref: '#/definitions/oc.AwenOrderCancleRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 订单取消
      tags:
      - 订单
  /order-api/order/docommit:
    post:
      consumes:
      - application/json
      description: Header头部添加group_id参数，社区团购id
      parameters:
      - description: ' '
        in: body
        name: OrderSubmitRequest
        required: true
        schema:
          $ref: '#/definitions/models.OrderSubmitRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OrderSubmitResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.OrderSubmitResponse'
      summary: 电商和小程序订单通用下单
      tags:
      - 订单
  /order-api/order/expresscompanylist:
    get:
      consumes:
      - application/json
      parameters:
      - description: 每页显示数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 当前页码
        in: query
        name: page
        required: true
        type: integer
      - description: 物流公司名称
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.ExpressCompanyListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.ExpressCompanyListResponse'
      summary: 物流公司列表
      tags:
      - 订单物流
  /order-api/order/expressinfo:
    get:
      consumes:
      - application/json
      parameters:
      - description: 物流单号
        in: query
        name: express_no
        type: string
      - description: 订单号
        in: query
        name: order_sn
        type: string
      - description: 服务单号
        in: query
        name: refund_sn
        type: string
      - description: 搜索类型 默认空：售后服务物流 1:子单号 2：父单号
        in: query
        name: search_type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.ExpressInfoResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.ExpressInfoResponse'
      summary: 物流路由对接
      tags:
      - 订单物流
  /order-api/order/expressinfoupdate:
    post:
      parameters:
      - description: ' '
        in: body
        name: ExpressInfoUpdateRequest
        required: true
        schema:
          $ref: '#/definitions/oc.ExpressInfoUpdateRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 物流信息更新
      tags:
      - 订单物流
  /order-api/order/link:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/dac.OrderLinkStoreReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dac.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dac.Response'
      summary: 新订单链路值保存
      tags:
      - 订单
  /order-api/order/offlinenotify:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.NotifyRequest
        required: true
        schema:
          $ref: '#/definitions/models.NotifyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.NotifyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.NotifyResponse'
      summary: 订单支付成功回调
      tags:
      - 订单
  /order-api/order/pay:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.OrderPayRequest
        required: true
        schema:
          $ref: '#/definitions/models.OrderPayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OrderPayResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.OrderPayResponse'
      summary: 订单支付
      tags:
      - 订单
  /order-api/order/pay-query:
    get:
      consumes:
      - application/json
      parameters:
      - description: order-api/order/pay接口返回的order_id
        in: query
        name: order_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.AwenOrderPayQueryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 订单支付状态查询
      tags:
      - 订单
  /order-api/order/pay/standard:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.PayRequest
        required: true
        schema:
          $ref: '#/definitions/models.PayRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.StandardPayResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.StandardPayResponse'
      summary: 订单标准支付
      tags:
      - 订单
  /order-api/order/paydyb2c:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.OrderPayRequest
        required: true
        schema:
          $ref: '#/definitions/models.OrderPayDyB2CRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OrderPayResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.OrderPayResponse'
      summary: 订单支付（电银B2C）
      tags:
      - 订单
  /order-api/order/paynotify:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.NotifyRequest
        required: true
        schema:
          $ref: '#/definitions/models.DYPayNotifyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PayNotifyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.PayNotifyResponse'
      summary: 电银订单支付成功回调
      tags:
      - 订单
  /order-api/order/pinpaynotify:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.NotifyRequest
        required: true
        schema:
          $ref: '#/definitions/models.NotifyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PayNotifyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.PayNotifyResponse'
      summary: 拼团订单支付成功回调
      tags:
      - 订单
  /order-api/order/pre-sale/push-integral:
    post:
      consumes:
      - text/plain
      parameters:
      - description: ' '
        in: body
        name: mallPushPreSaleRequest
        required: true
        schema:
          $ref: '#/definitions/models.MallPushPreSaleIntegralRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 预售订单定金积分
      tags:
      - 订单
  /order-api/order/pre-sale/push-message:
    post:
      consumes:
      - text/plain
      parameters:
      - description: ' '
        in: body
        name: mallPushPreSaleRequest
        required: true
        schema:
          $ref: '#/definitions/models.MallPushPreSaleMessageRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 预售订单提醒尾款支付订阅消息
      tags:
      - 订单
  /order-api/order/prescribe:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/models.OrderPrescribeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.OrderPrescribeRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.OrderPrescribeRes'
      summary: 开处方单
      tags:
      - 订单
  /order-api/order/prescribe-check:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: model
        required: true
        schema:
          $ref: '#/definitions/oc.OrderPrescribeCheckReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.OrderPrescribeCheckRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.OrderPrescribeCheckRes'
      summary: 检查是否开过处方单
      tags:
      - 订单
  /order-api/order/re-push-oms:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: models.PushOmsRequest
        required: true
        schema:
          $ref: '#/definitions/models.PushOmsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 重推OMS，有无库存都可以调用
      tags:
      - 订单
  /order-api/order/rider-location/get:
    get:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: ExpressInfoUpdateRequest
        required: true
        schema:
          $ref: '#/definitions/oc.RiderLocationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.ExternalResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.ExternalResponse'
      summary: 获取美团骑手当前所在的地址
      tags:
      - 订单物流
  /order-api/order/step-log:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: dac.OrderStepLogRequest
        required: true
        schema:
          $ref: '#/definitions/dac.OrderStepLogRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dac.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dac.Response'
      summary: 下单前浏览链路统计
      tags:
      - 订单
  /order-api/order/subscribe-log:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: dac.OrderSubscribeLogRequest
        required: true
        schema:
          $ref: '#/definitions/dac.OrderSubscribeLogRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dac.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/dac.Response'
      summary: 提交订单后收集订阅消息
      tags:
      - 订单
  /order-api/order/verify-codes:
    get:
      consumes:
      - text/plain
      - application/json
      parameters:
      - description: '当前多少页 从1开始 不传默认为1 '
        in: query
        name: pageIndex
        type: integer
      - description: 每页多少条数据 不传默认为15
        in: query
        name: pageSize
        type: integer
      - description: 第三方（美团 京东 饿了么）订单号
        in: query
        name: orderSn
        required: true
        type: string
      - description: 商品名称（支持模糊搜索）
        in: query
        name: productName
        type: string
      - description: '核销码状态 -1 所有 0未核销, 1已核销, 2已退款 不传默认为0 '
        in: query
        name: verifyStatus
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.GetVerifyCodeByOldOrderSnRes'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.GetVerifyCodeByOldOrderSnRes'
      summary: 根据第三方订单号查询核销码信息-v6.0
      tags:
      - 虚拟订单核销
  /order-api/refund/apply:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: apply
        required: true
        schema:
          $ref: '#/definitions/oc.RefundOrderApplyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.RefundOrderApplyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.RefundOrderApplyResponse'
      summary: 申请售后单
      tags:
      - 售后单
  /order-api/refund/cancel:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: cancel
        required: true
        schema:
          $ref: '#/definitions/oc.RefundOrderCancelRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BaseResponse'
      summary: 撤销申请售后单
      tags:
      - 售后单
  /order-api/refund/pay:
    get:
      responses: {}
  /order-api/subscribe-message/push:
    post:
      consumes:
      - text/plain
      parameters:
      - description: OpenId
        in: query
        name: openId
        required: true
        type: string
      - description: 订单号
        in: query
        name: orderSn
        required: true
        type: string
      - description: 模板ID
        in: query
        name: templateId
        required: true
        type: string
      - description: 推送类型
        in: query
        name: pushType
        required: true
        type: integer
      - description: 备注
        in: query
        name: remarks
        type: string
      - description: 退款ID
        in: query
        name: refundId
        type: string
      - description: 退款类型
        in: query
        name: refundType
        type: string
      - description: 退款金额
        in: query
        name: refundAmount
        type: string
      - description: 退款时间
        in: query
        name: refundTime
        type: string
      - description: 退款订单号
        in: query
        name: refundSn
        type: string
      - description: 状态
        in: query
        name: status
        type: string
      - description: 开始时间
        in: query
        name: startTime
        type: string
      - description: 结束时间
        in: query
        name: endTime
        type: string
      - description: 是否虚拟订单
        in: query
        name: isVirtual
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: 订阅消息推送
      tags:
      - 订阅消息
  /order-api/subscribe-message/send:
    post:
      consumes:
      - text/plain
      parameters:
      - description: ' '
        in: body
        name: models.NotifyRequest
        required: true
        schema:
          $ref: '#/definitions/oc.PushTemplateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: 推送订阅消息（多条件）
      tags:
      - 订阅消息
  /order-api/upetDj/CalcPromotionMoney:
    post:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/mk.PromotionCalcRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/mk.PromotionCalcResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 根据购物车商品计算优惠信息以及运费
      tags:
      - 阿闻到家
  /order-api/upetDj/ConfirmUpetDj:
    put:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/oc.UpetDjConfirmRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.UpetDjConfirmResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 修改订单状态，确认收货
      tags:
      - 阿闻到家
  /order-api/upetDj/GetDeliveryMoney:
    post:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/mk.PromotionCalcRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/mk.PromotionCalcResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 计算运费
      tags:
      - 宠物SAAS
  /order-api/upetDj/PrintWithOrderId:
    get:
      consumes:
      - application/json
      parameters:
      - description: 订单号码
        in: body
        name: orderId
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 根据门店配置是否自动打印配置推送给前端管理员
      tags:
      - 阿闻到家
  /order-api/upetDj/QuerySection:
    get:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/oc.SectionQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.SectionQueryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 加载省市区列表
      tags:
      - 阿闻到家
  /order-api/upetDj/QueryUpetDjOrderDetail:
    get:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/oc.UpetDjOrderDetailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.UpetDjOrderDetailResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 查询阿闻到家订单详情-v6.0
      tags:
      - 阿闻到家
  /order-api/upetDj/QueryUpetDjOrderIsAutoPrint:
    get:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/oc.UpetDjOrderIsAutoPrintQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.UpetDjOrderIsAutoPrintQueryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 根据订单号，查询门店信息中当前门店的订单是否需要自动打单
      tags:
      - 阿闻到家
  /order-api/upetDj/QueryUpetDjOrderList:
    get:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/oc.UpetDjOrderQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.UpetDjOrderQueryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 查询阿闻到家订单-v6.0
      tags:
      - 阿闻到家
  /order-api/upetDj/QueryUpetDjOrderListByModile:
    get:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/oc.UpetDjOrderQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.UpetDjOrderQueryResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 查询阿闻到家订单
      tags:
      - 阿闻到家
  /order-api/upetDj/QueryUpetDjShopDelivery:
    get:
      consumes:
      - application/json
      parameters:
      - description: 数据请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dac.ShopDeliveryServiceDetailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dac.ShopDeliveryServiceDetailResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 查询店铺的配送配置
      tags:
      - 阿闻到家
  /order-api/upetDj/pickup-station/nearest:
    get:
      consumes:
      - application/json
      parameters:
      - description: 门店财务编码
        in: query
        name: finance_code
        type: string
      - description: 地址纬度
        in: query
        name: lat
        type: string
      - description: 地址经度
        in: query
        name: lng
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dac.AWStationNearestResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 社区团购最近的站点
      tags:
      - 阿闻到家
  /order-api/upetDj/pickup-station/state:
    get:
      consumes:
      - application/json
      parameters:
      - description: 门店财务编码
        in: query
        name: finance_code
        type: string
      - description: 站点id
        in: query
        name: station_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dac.AWStationStateResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 社区团购站点状态
      tags:
      - 阿闻到家
  /order-api/upetDj/pickup-stations:
    get:
      consumes:
      - application/json
      parameters:
      - description: 门店财务编码
        in: query
        name: finance_code
        type: string
      - description: 搜索关键字
        in: query
        name: keyword
        type: string
      - description: 地址纬度
        in: query
        name: lat
        type: string
      - description: 地址经度
        in: query
        name: lng
        type: string
      - description: 当前页码
        in: query
        name: page_index
        type: integer
      - description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dac.AWStationsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.BadRequestResponse'
      summary: 社区团购附近站点查询
      tags:
      - 阿闻到家
  /order-api/user/token:
    get:
      consumes:
      - text/plain
      parameters:
      - description: 用户手机号
        in: query
        name: user_mobile
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: SCRM用户token
      tags:
      - SCRM用户相关
  /order/order/get:
    get:
      consumes:
      - application/json
      parameters:
      - description: 核销码
        in: query
        name: writtenoffcode
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OldOrderDetail'
        "400":
          description: 核销码不能为空
          schema:
            type: string
      summary: 查询需要核销的订单信息 -- 只支持核销码查询接口
      tags:
      - 虚拟订单核销
  /order/order/virtual-order-write-off-codes:
    get:
      consumes:
      - application/json
      parameters:
      - description: 当前页
        in: query
        name: page_index
        type: integer
      - description: 每页记录数量
        in: query
        name: page_size
        type: integer
      - description: SCRM会员编码
        in: query
        name: scrm_user_id
        type: string
      - description: 类型 不传默认全部，1、储值卡
        in: query
        name: type
        type: integer
      - description: 订单号
        in: query
        name: vr_order_sn
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/oc.QueryMallVirtualOrderWriteOffCodesResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/oc.QueryMallVirtualOrderWriteOffCodesResponse'
      summary: 获取用户未核销记录
      tags:
      - 虚拟订单核销
  /order/order/written-off:
    post:
      consumes:
      - application/json
      parameters:
      - description: ' '
        in: body
        name: WriteOffVirtualOrder
        required: true
        schema:
          $ref: '#/definitions/models.WriteOffReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.BaseResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.BaseResponse'
      summary: 核销虚拟信息
      tags:
      - 虚拟订单核销
swagger: "2.0"
